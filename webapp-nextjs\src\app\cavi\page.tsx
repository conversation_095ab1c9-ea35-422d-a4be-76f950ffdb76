'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import { caviApi } from '@/lib/api'
import { Cavo } from '@/types'
import {
  Cable,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Download,
  Upload,
  Loader2
} from 'lucide-react'

export default function CaviPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [cavi, setCavi] = useState<Cavo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()

  // Carica i cavi dal backend
  useEffect(() => {
    loadCavi()
  }, [])

  const loadCavi = async () => {
    try {
      setIsLoading(true)
      setError('')

      const cantiereId = cantiere?.id_cantiere || user?.id_utente
      if (!cantiereId) {
        setError('Cantiere non selezionato')
        return
      }

      const data = await caviApi.getCavi(cantiereId, {
        search: searchTerm,
        stato_installazione: selectedStatus === 'all' ? undefined : selectedStatus
      })

      setCavi(data)
    } catch (error: any) {
      console.error('Errore caricamento cavi:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei cavi')
    } finally {
      setIsLoading(false)
    }
  }

  // Ricarica quando cambiano i filtri
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadCavi()
    }, 500) // Debounce di 500ms

    return () => clearTimeout(timeoutId)
  }, [searchTerm, selectedStatus])

  const getStatusBadge = (stato: string | undefined) => {
    switch (stato) {
      case 'installato':
        return <Badge className="bg-green-100 text-green-800">Installato</Badge>
      case 'in_corso':
        return <Badge className="bg-yellow-100 text-yellow-800">In Corso</Badge>
      case 'non_installato':
      case '':
      case null:
      case undefined:
        return <Badge className="bg-gray-100 text-gray-800">Non Installato</Badge>
      default:
        return <Badge variant="secondary">{stato}</Badge>
    }
  }

  const getConnectionBadge = (collegamenti: number | undefined) => {
    switch (collegamenti) {
      case 3:
        return <Badge className="bg-green-100 text-green-800">Collegato</Badge>
      case 1:
      case 2:
        return <Badge className="bg-yellow-100 text-yellow-800">Parziale</Badge>
      case 0:
      case null:
      case undefined:
        return <Badge className="bg-gray-100 text-gray-800">Non Collegato</Badge>
      default:
        return <Badge variant="secondary">Stato {collegamenti}</Badge>
    }
  }

  const getCertificationBadge = (cavo: Cavo) => {
    if (cavo.data_certificazione) {
      return <Badge className="bg-green-100 text-green-800">Certificato</Badge>
    } else if (cavo.comanda_certificazione) {
      return <Badge className="bg-yellow-100 text-yellow-800">In Corso</Badge>
    } else {
      return <Badge className="bg-gray-100 text-gray-800">Non Certificato</Badge>
    }
  }

  const filteredCavi = cavi.filter(cavo => {
    const matchesSearch = cavo.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cavo.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cavo.n_conduttori?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cavo.sezione?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = selectedStatus === 'all' || cavo.stato_installazione === selectedStatus

    return matchesSearch && matchesStatus
  })

  const stats = {
    totali: cavi.length,
    installati: cavi.filter(c => c.metratura_reale && c.metratura_reale > 0).length,
    in_corso: cavi.filter(c => c.comanda_posa && !c.data_posa).length,
    non_installati: cavi.filter(c => !c.metratura_reale || c.metratura_reale === 0).length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Cable className="h-8 w-8 text-blue-600" />
              Gestione Cavi
            </h1>
            <p className="text-slate-600 mt-1">Visualizzazione e gestione completa dei cavi del cantiere</p>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Esporta
            </Button>
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Importa
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Nuovo Cavo
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Totali</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
                </div>
                <Cable className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Installati</p>
                  <p className="text-2xl font-bold text-green-600">{stats.installati}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">In Corso</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.in_corso}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Da Installare</p>
                  <p className="text-2xl font-bold text-gray-600">{stats.non_installati}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-gray-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Ricerca e Filtri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Cerca per nomenclatura, tipologia o formazione..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                {['all', 'installato', 'in_corso', 'non_installato'].map((status) => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status === 'all' ? 'Tutti' : 
                     status === 'installato' ? 'Installati' :
                     status === 'in_corso' ? 'In Corso' : 'Da Installare'}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cavi Table */}
        <Card>
          <CardHeader>
            <CardTitle>Elenco Cavi ({filteredCavi.length})</CardTitle>
            <CardDescription>
              Gestione completa dei cavi con stato installazione, collegamento e certificazione
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Cavo</TableHead>
                    <TableHead>Tipologia</TableHead>
                    <TableHead>Conduttori/Sezione</TableHead>
                    <TableHead>Partenza → Arrivo</TableHead>
                    <TableHead>Lunghezza</TableHead>
                    <TableHead>Bobina</TableHead>
                    <TableHead>Stato</TableHead>
                    <TableHead>Collegamento</TableHead>
                    <TableHead>Certificazione</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Caricamento cavi...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2 text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          {error}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredCavi.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8 text-slate-500">
                        Nessun cavo trovato
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredCavi.map((cavo) => (
                      <TableRow key={cavo.id_cavo}>
                        <TableCell className="font-medium">{cavo.id_cavo}</TableCell>
                        <TableCell>{cavo.tipologia || '-'}</TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{cavo.n_conduttori || '-'}</div>
                            <div className="text-slate-500">{cavo.sezione || '-'}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">{cavo.ubicazione_partenza || '-'}</div>
                            <div className="text-slate-500">↓</div>
                            <div className="font-medium">{cavo.ubicazione_arrivo || '-'}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{cavo.metratura_reale || 0}/{cavo.metri_teorici || 0}m</div>
                            <div className="text-slate-500">
                              {cavo.metri_teorici ? Math.round(((cavo.metratura_reale || 0) / cavo.metri_teorici) * 100) : 0}%
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={cavo.id_bobina === 'BOBINA_VUOTA' ? 'destructive' : 'secondary'}>
                            {cavo.id_bobina || 'BOBINA_VUOTA'}
                          </Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge(cavo.stato_installazione)}</TableCell>
                        <TableCell>{getConnectionBadge(cavo.collegamenti)}</TableCell>
                        <TableCell>{getCertificationBadge(cavo)}</TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
