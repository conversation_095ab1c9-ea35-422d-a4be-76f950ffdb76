{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\EstimationTool.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, TextField, Select, MenuItem, FormControl, InputLabel, Button, Paper, Chip, Avatar, Divider, Alert, CircularProgress, Stepper, Step, StepLabel, StepContent } from '@mui/material';\nimport { Calculate, TrendingUp, Schedule, Engineering, Speed, Assessment, CheckCircle, Info, Warning } from '@mui/icons-material';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EstimationTool = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    cable_type_id: '',\n    activity_type: 'Posa',\n    quantity_required: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    number_of_operators: 1,\n    experience_level: 'Senior'\n  });\n  const [cableTypes, setCableTypes] = useState([]);\n  const [estimation, setEstimation] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [{\n    value: 'Posa',\n    label: 'Posa'\n  }, {\n    value: 'Collegamento',\n    label: 'Collegamento'\n  }, {\n    value: 'Certificazione',\n    label: 'Certificazione'\n  }];\n  const environmentalConditions = [{\n    value: 'Normale',\n    label: 'Normale'\n  }, {\n    value: 'Spazi Ristretti',\n    label: 'Spazi Ristretti'\n  }, {\n    value: 'In Altezza',\n    label: 'In Altezza'\n  }, {\n    value: 'Esterno',\n    label: 'Esterno'\n  }];\n  const toolsUsed = [{\n    value: 'Manuale',\n    label: 'Manuale'\n  }, {\n    value: 'Automatico',\n    label: 'Automatico'\n  }];\n  const experienceLevels = [{\n    value: 'Apprentice',\n    label: 'Apprendista'\n  }, {\n    value: 'Junior',\n    label: 'Junior'\n  }, {\n    value: 'Senior',\n    label: 'Senior'\n  }];\n  useEffect(() => {\n    loadCableTypes();\n  }, []);\n  const loadCableTypes = async () => {\n    try {\n      const response = await axiosInstance.get('/admin/tipologie-cavi');\n      setCableTypes(response.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento tipi di cavo:', error);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n\n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n\n    // Reset estimation quando cambiano i parametri\n    if (estimation) {\n      setEstimation(null);\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity_required || formData.quantity_required <= 0) {\n      newErrors.quantity_required = 'Quantità deve essere maggiore di 0';\n    }\n    if (!formData.number_of_operators || formData.number_of_operators < 1) {\n      newErrors.number_of_operators = 'Numero operatori deve essere almeno 1';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setErrors({});\n\n      // Prepara i dati per l'invio\n      const submitData = {\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        activity_type: formData.activity_type,\n        quantity_required: parseFloat(formData.quantity_required),\n        environmental_conditions: formData.environmental_conditions,\n        tools_used: formData.tools_used,\n        number_of_operators: parseInt(formData.number_of_operators),\n        experience_level: formData.experience_level\n      };\n      const response = await axiosInstance.post('/v1/predict/estimation', submitData);\n      setEstimation(response.data);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore nella stima:', error);\n      setErrors({\n        general: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore nel calcolo della stima'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatHours = hours => {\n    if (hours < 1) {\n      return `${Math.round(hours * 60)} minuti`;\n    } else if (hours < 24) {\n      const h = Math.floor(hours);\n      const m = Math.round((hours - h) * 60);\n      return m > 0 ? `${h}h ${m}m` : `${h}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      const h = Math.round(hours % 24);\n      return h > 0 ? `${days}g ${h}h` : `${days}g`;\n    }\n  };\n  const getCorrectionFactorColor = factor => {\n    if (factor > 1) return 'text-green-600';\n    if (factor < 1) return 'text-red-600';\n    return 'text-gray-600';\n  };\n  const getCorrectionFactorIcon = factor => {\n    if (factor > 1) return '↗️';\n    if (factor < 1) return '↘️';\n    return '➡️';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Calculate, {\n          sx: {\n            fontSize: 40,\n            color: 'primary.main',\n            mr: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          sx: {\n            fontWeight: 700\n          },\n          children: \"Strumento di Stima Produttivit\\xE0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        color: \"text.secondary\",\n        children: \"Calcola tempi e costi stimati per i tuoi progetti di installazione cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Parametri del Lavoro\",\n            subheader: \"Inserisci i dettagli del progetto per ottenere una stima accurata\",\n            avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: 'primary.main'\n              },\n              children: /*#__PURE__*/_jsxDEV(Engineering, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [errors.general && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 3\n              },\n              children: errors.general\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              component: \"form\",\n              onSubmit: handleSubmit,\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.activity_type,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Tipo Attivit\\xE0 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"activity_type\",\n                  value: formData.activity_type,\n                  onChange: handleInputChange,\n                  label: \"Tipo Attivit\\xE0 *\",\n                  children: activityTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type.value,\n                    children: type.label\n                  }, type.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), errors.activity_type && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: errors.activity_type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Tipo di Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"cable_type_id\",\n                  value: formData.cable_type_id,\n                  onChange: handleInputChange,\n                  label: \"Tipo di Cavo\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: \"Seleziona tipo di cavo (opzionale)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this), cableTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type.id_tipologia,\n                    children: [type.codice_prodotto, \" - \", type.nome_commerciale]\n                  }, type.id_tipologia, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                name: \"quantity_required\",\n                label: `Quantità Richiesta * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`,\n                value: formData.quantity_required,\n                onChange: handleInputChange,\n                error: !!errors.quantity_required,\n                helperText: errors.quantity_required,\n                inputProps: {\n                  step: 0.1,\n                  min: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Condizioni Ambientali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"environmental_conditions\",\n                  value: formData.environmental_conditions,\n                  onChange: handleInputChange,\n                  label: \"Condizioni Ambientali\",\n                  children: environmentalConditions.map(condition => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: condition.value,\n                    children: condition.label\n                  }, condition.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Strumenti Utilizzati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"tools_used\",\n                  value: formData.tools_used,\n                  onChange: handleInputChange,\n                  label: \"Strumenti Utilizzati\",\n                  children: toolsUsed.map(tool => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: tool.value,\n                    children: tool.label\n                  }, tool.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    fullWidth: true,\n                    type: \"number\",\n                    name: \"number_of_operators\",\n                    label: \"Numero Operatori *\",\n                    value: formData.number_of_operators,\n                    onChange: handleInputChange,\n                    error: !!errors.number_of_operators,\n                    helperText: errors.number_of_operators,\n                    inputProps: {\n                      min: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: /*#__PURE__*/_jsxDEV(FormControl, {\n                    fullWidth: true,\n                    children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                      children: \"Livello di Esperienza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Select, {\n                      name: \"experience_level\",\n                      value: formData.experience_level,\n                      onChange: handleInputChange,\n                      label: \"Livello di Esperienza\",\n                      children: experienceLevels.map(level => /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: level.value,\n                        children: level.label\n                      }, level.value, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"contained\",\n                size: \"large\",\n                fullWidth: true,\n                disabled: loading,\n                startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 40\n                }, this) : /*#__PURE__*/_jsxDEV(Calculate, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 73\n                }, this),\n                sx: {\n                  py: 2,\n                  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n                  '&:hover': {\n                    background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n                  }\n                },\n                children: loading ? 'Calcolando...' : 'Calcola Stima'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Risultati della Stima\",\n            subheader: \"Analisi dettagliata dei tempi e costi previsti\",\n            avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: 'success.main'\n              },\n              children: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: estimation ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 2,\n                sx: {\n                  p: 3,\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      fontWeight: 600\n                    },\n                    children: \"Risultati Principali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h4\",\n                        sx: {\n                          fontWeight: 700,\n                          mb: 1\n                        },\n                        children: formatHours(estimation.estimated_time_for_team_hours)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 403,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          opacity: 0.8\n                        },\n                        children: \"Tempo stimato per il team\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 406,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h4\",\n                        sx: {\n                          fontWeight: 700,\n                          mb: 1\n                        },\n                        children: [estimation.estimated_total_man_hours.toFixed(1), \"h\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 413,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          opacity: 0.8\n                        },\n                        children: \"Ore-uomo totali\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 416,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 1,\n                sx: {\n                  p: 2,\n                  bgcolor: 'grey.50'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 2,\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Info, {\n                    sx: {\n                      mr: 1,\n                      color: 'primary.main'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), \"Riepilogo Input\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Attivit\\xE0:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: estimation.inputs.activity_type,\n                      size: \"small\",\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Quantit\\xE0:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: [estimation.inputs.quantity_required, \" \", estimation.inputs.activity_type === 'Posa' ? 'metri' : 'unità']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 437,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Operatori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: estimation.inputs.number_of_operators\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Condizioni:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: estimation.inputs.environmental_conditions\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 1,\n                sx: {\n                  p: 2,\n                  bgcolor: 'success.50'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 2,\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Speed, {\n                    sx: {\n                      mr: 1,\n                      color: 'success.main'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this), \"Dettagli Produttivit\\xE0\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Produttivit\\xE0 base:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 600,\n                        color: 'success.main'\n                      },\n                      children: [estimation.base_productivity.toFixed(1), \" \", estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Produttivit\\xE0 attesa:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 600,\n                        color: 'success.main'\n                      },\n                      children: [estimation.expected_productivity_per_operator.toFixed(1), \" \", estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h', \"/op\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 1,\n                sx: {\n                  p: 2,\n                  bgcolor: 'warning.50'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 2,\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Engineering, {\n                    sx: {\n                      mr: 1,\n                      color: 'warning.main'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 23\n                  }, this), \"Fattori di Correzione\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: Object.entries(estimation.correction_factors).map(([factor, value]) => /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 4,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        textAlign: 'center',\n                        p: 1,\n                        border: '1px solid',\n                        borderColor: 'divider',\n                        borderRadius: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        sx: {\n                          textTransform: 'capitalize'\n                        },\n                        children: factor.replace('_', ' ')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 488,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        sx: {\n                          fontWeight: 600,\n                          color: value > 1 ? 'success.main' : value < 1 ? 'error.main' : 'text.primary'\n                        },\n                        children: [getCorrectionFactorIcon(value), \" \", (value * 100).toFixed(0), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 491,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 27\n                    }, this)\n                  }, factor, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"info\",\n                variant: \"outlined\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 1\n                  },\n                  children: \"Note Importanti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"ul\",\n                  sx: {\n                    m: 0,\n                    pl: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Le stime sono basate su dati storici e fattori di correzione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"I tempi effettivi possono variare in base a condizioni specifiche\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Utilizzare come riferimento per la pianificazione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 8,\n                color: 'text.secondary'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calculate, {\n                sx: {\n                  fontSize: 80,\n                  mb: 2,\n                  opacity: 0.3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 1\n                },\n                children: \"Pronto per il Calcolo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Inserisci i parametri nel form a sinistra e clicca \\\"Calcola Stima\\\" per vedere i risultati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(EstimationTool, \"Fdfa3V1PCa+zRqPC5ZEYYrQiFCA=\");\n_c = EstimationTool;\nexport default EstimationTool;\nvar _c;\n$RefreshReg$(_c, \"EstimationTool\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Paper", "Chip", "Avatar", "Divider", "<PERSON><PERSON>", "CircularProgress", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Calculate", "TrendingUp", "Schedule", "Engineering", "Speed", "Assessment", "CheckCircle", "Info", "Warning", "axiosInstance", "jsxDEV", "_jsxDEV", "EstimationTool", "_s", "formData", "setFormData", "cable_type_id", "activity_type", "quantity_required", "environmental_conditions", "tools_used", "number_of_operators", "experience_level", "cableTypes", "setCableTypes", "estimation", "setEstimation", "loading", "setLoading", "errors", "setErrors", "activityTypes", "value", "label", "environmentalConditions", "toolsUsed", "experienceLevels", "loadCableTypes", "response", "get", "data", "error", "console", "handleInputChange", "e", "name", "type", "target", "prev", "parseFloat", "validateForm", "newErrors", "Object", "keys", "length", "handleSubmit", "preventDefault", "submitData", "parseInt", "post", "_error$response", "_error$response$data", "general", "detail", "formatHours", "hours", "Math", "round", "h", "floor", "m", "days", "getCorrectionFactorColor", "factor", "getCorrectionFactorIcon", "sx", "p", "children", "mb", "textAlign", "display", "justifyContent", "alignItems", "fontSize", "color", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "fontWeight", "container", "spacing", "item", "xs", "lg", "elevation", "title", "subheader", "avatar", "bgcolor", "severity", "onSubmit", "flexDirection", "gap", "fullWidth", "onChange", "map", "mt", "id_tipologia", "codice_prodotto", "nome_commerciale", "helperText", "inputProps", "step", "min", "condition", "tool", "sm", "level", "size", "disabled", "startIcon", "py", "background", "estimated_time_for_team_hours", "opacity", "estimated_total_man_hours", "toFixed", "inputs", "base_productivity", "expected_productivity_per_operator", "entries", "correction_factors", "border", "borderColor", "borderRadius", "textTransform", "replace", "pl", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/EstimationTool.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Button,\n  Paper,\n  Chip,\n  Avatar,\n  Divider,\n  Alert,\n  CircularProgress,\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>abel,\n  StepContent\n} from '@mui/material';\nimport {\n  Calculate,\n  TrendingUp,\n  Schedule,\n  Engineering,\n  Speed,\n  Assessment,\n  CheckCircle,\n  Info,\n  Warning\n} from '@mui/icons-material';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst EstimationTool = () => {\n  const [formData, setFormData] = useState({\n    cable_type_id: '',\n    activity_type: 'Posa',\n    quantity_required: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    number_of_operators: 1,\n    experience_level: 'Senior'\n  });\n\n  const [cableTypes, setCableTypes] = useState([]);\n  const [estimation, setEstimation] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [\n    { value: 'Posa', label: 'Posa' },\n    { value: 'Collegamento', label: 'Collegamento' },\n    { value: 'Certificazione', label: 'Certificazione' }\n  ];\n\n  const environmentalConditions = [\n    { value: 'Normale', label: 'Normale' },\n    { value: 'Spazi Ristretti', label: 'Spazi Ristretti' },\n    { value: 'In Altezza', label: 'In Altezza' },\n    { value: 'Esterno', label: 'Esterno' }\n  ];\n\n  const toolsUsed = [\n    { value: 'Manuale', label: 'Manuale' },\n    { value: 'Automatico', label: 'Automatico' }\n  ];\n\n  const experienceLevels = [\n    { value: 'Apprentice', label: 'Apprendista' },\n    { value: 'Junior', label: 'Junior' },\n    { value: 'Senior', label: 'Senior' }\n  ];\n\n  useEffect(() => {\n    loadCableTypes();\n  }, []);\n\n  const loadCableTypes = async () => {\n    try {\n      const response = await axiosInstance.get('/admin/tipologie-cavi');\n      setCableTypes(response.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento tipi di cavo:', error);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n    \n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: null }));\n    }\n\n    // Reset estimation quando cambiano i parametri\n    if (estimation) {\n      setEstimation(null);\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity_required || formData.quantity_required <= 0) {\n      newErrors.quantity_required = 'Quantità deve essere maggiore di 0';\n    }\n    if (!formData.number_of_operators || formData.number_of_operators < 1) {\n      newErrors.number_of_operators = 'Numero operatori deve essere almeno 1';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setErrors({});\n      \n      // Prepara i dati per l'invio\n      const submitData = {\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        activity_type: formData.activity_type,\n        quantity_required: parseFloat(formData.quantity_required),\n        environmental_conditions: formData.environmental_conditions,\n        tools_used: formData.tools_used,\n        number_of_operators: parseInt(formData.number_of_operators),\n        experience_level: formData.experience_level\n      };\n\n      const response = await axiosInstance.post('/v1/predict/estimation', submitData);\n      setEstimation(response.data);\n      \n    } catch (error) {\n      console.error('Errore nella stima:', error);\n      setErrors({ \n        general: error.response?.data?.detail || 'Errore nel calcolo della stima' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatHours = (hours) => {\n    if (hours < 1) {\n      return `${Math.round(hours * 60)} minuti`;\n    } else if (hours < 24) {\n      const h = Math.floor(hours);\n      const m = Math.round((hours - h) * 60);\n      return m > 0 ? `${h}h ${m}m` : `${h}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      const h = Math.round(hours % 24);\n      return h > 0 ? `${days}g ${h}h` : `${days}g`;\n    }\n  };\n\n  const getCorrectionFactorColor = (factor) => {\n    if (factor > 1) return 'text-green-600';\n    if (factor < 1) return 'text-red-600';\n    return 'text-gray-600';\n  };\n\n  const getCorrectionFactorIcon = (factor) => {\n    if (factor > 1) return '↗️';\n    if (factor < 1) return '↘️';\n    return '➡️';\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 4, textAlign: 'center' }}>\n        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>\n          <Calculate sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />\n          <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700 }}>\n            Strumento di Stima Produttività\n          </Typography>\n        </Box>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\">\n          Calcola tempi e costi stimati per i tuoi progetti di installazione cavi\n        </Typography>\n      </Box>\n\n      <Grid container spacing={4}>\n        {/* Form di Input */}\n        <Grid item xs={12} lg={6}>\n          <Card elevation={3}>\n            <CardHeader\n              title=\"Parametri del Lavoro\"\n              subheader=\"Inserisci i dettagli del progetto per ottenere una stima accurata\"\n              avatar={\n                <Avatar sx={{ bgcolor: 'primary.main' }}>\n                  <Engineering />\n                </Avatar>\n              }\n            />\n            <CardContent>\n              {errors.general && (\n                <Alert severity=\"error\" sx={{ mb: 3 }}>\n                  {errors.general}\n                </Alert>\n              )}\n\n              <Box component=\"form\" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>\n                {/* Tipo di Attività */}\n                <FormControl fullWidth error={!!errors.activity_type}>\n                  <InputLabel>Tipo Attività *</InputLabel>\n                  <Select\n                    name=\"activity_type\"\n                    value={formData.activity_type}\n                    onChange={handleInputChange}\n                    label=\"Tipo Attività *\"\n                  >\n                    {activityTypes.map(type => (\n                      <MenuItem key={type.value} value={type.value}>\n                        {type.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.activity_type && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1 }}>\n                      {errors.activity_type}\n                    </Typography>\n                  )}\n                </FormControl>\n\n                {/* Tipo di Cavo */}\n                <FormControl fullWidth>\n                  <InputLabel>Tipo di Cavo</InputLabel>\n                  <Select\n                    name=\"cable_type_id\"\n                    value={formData.cable_type_id}\n                    onChange={handleInputChange}\n                    label=\"Tipo di Cavo\"\n                  >\n                    <MenuItem value=\"\">Seleziona tipo di cavo (opzionale)</MenuItem>\n                    {cableTypes.map(type => (\n                      <MenuItem key={type.id_tipologia} value={type.id_tipologia}>\n                        {type.codice_prodotto} - {type.nome_commerciale}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n\n                {/* Quantità Richiesta */}\n                <TextField\n                  fullWidth\n                  type=\"number\"\n                  name=\"quantity_required\"\n                  label={`Quantità Richiesta * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`}\n                  value={formData.quantity_required}\n                  onChange={handleInputChange}\n                  error={!!errors.quantity_required}\n                  helperText={errors.quantity_required}\n                  inputProps={{ step: 0.1, min: 0 }}\n                />\n\n                {/* Condizioni Ambientali */}\n                <FormControl fullWidth>\n                  <InputLabel>Condizioni Ambientali</InputLabel>\n                  <Select\n                    name=\"environmental_conditions\"\n                    value={formData.environmental_conditions}\n                    onChange={handleInputChange}\n                    label=\"Condizioni Ambientali\"\n                  >\n                    {environmentalConditions.map(condition => (\n                      <MenuItem key={condition.value} value={condition.value}>\n                        {condition.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n\n                {/* Strumenti Utilizzati */}\n                <FormControl fullWidth>\n                  <InputLabel>Strumenti Utilizzati</InputLabel>\n                  <Select\n                    name=\"tools_used\"\n                    value={formData.tools_used}\n                    onChange={handleInputChange}\n                    label=\"Strumenti Utilizzati\"\n                  >\n                    {toolsUsed.map(tool => (\n                      <MenuItem key={tool.value} value={tool.value}>\n                        {tool.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n\n                <Grid container spacing={2}>\n                  {/* Numero Operatori */}\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      type=\"number\"\n                      name=\"number_of_operators\"\n                      label=\"Numero Operatori *\"\n                      value={formData.number_of_operators}\n                      onChange={handleInputChange}\n                      error={!!errors.number_of_operators}\n                      helperText={errors.number_of_operators}\n                      inputProps={{ min: 1 }}\n                    />\n                  </Grid>\n\n                  {/* Livello di Esperienza */}\n                  <Grid item xs={12} sm={6}>\n                    <FormControl fullWidth>\n                      <InputLabel>Livello di Esperienza</InputLabel>\n                      <Select\n                        name=\"experience_level\"\n                        value={formData.experience_level}\n                        onChange={handleInputChange}\n                        label=\"Livello di Esperienza\"\n                      >\n                        {experienceLevels.map(level => (\n                          <MenuItem key={level.value} value={level.value}>\n                            {level.label}\n                          </MenuItem>\n                        ))}\n                      </Select>\n                    </FormControl>\n                  </Grid>\n                </Grid>\n\n                <Button\n                  type=\"submit\"\n                  variant=\"contained\"\n                  size=\"large\"\n                  fullWidth\n                  disabled={loading}\n                  startIcon={loading ? <CircularProgress size={20} /> : <Calculate />}\n                  sx={{\n                    py: 2,\n                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n                    '&:hover': {\n                      background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n                    }\n                  }}\n                >\n                  {loading ? 'Calcolando...' : 'Calcola Stima'}\n                </Button>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Risultati della Stima */}\n        <Grid item xs={12} lg={6}>\n          <Card elevation={3}>\n            <CardHeader\n              title=\"Risultati della Stima\"\n              subheader=\"Analisi dettagliata dei tempi e costi previsti\"\n              avatar={\n                <Avatar sx={{ bgcolor: 'success.main' }}>\n                  <Assessment />\n                </Avatar>\n              }\n            />\n            <CardContent>\n              {estimation ? (\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>\n                  {/* Risultati Principali */}\n                  <Paper\n                    elevation={2}\n                    sx={{\n                      p: 3,\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      color: 'white'\n                    }}\n                  >\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <CheckCircle sx={{ mr: 1 }} />\n                      <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                        Risultati Principali\n                      </Typography>\n                    </Box>\n\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} sm={6}>\n                        <Box sx={{ textAlign: 'center' }}>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 1 }}>\n                            {formatHours(estimation.estimated_time_for_team_hours)}\n                          </Typography>\n                          <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                            Tempo stimato per il team\n                          </Typography>\n                        </Box>\n                      </Grid>\n                      <Grid item xs={12} sm={6}>\n                        <Box sx={{ textAlign: 'center' }}>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 1 }}>\n                            {estimation.estimated_total_man_hours.toFixed(1)}h\n                          </Typography>\n                          <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                            Ore-uomo totali\n                          </Typography>\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </Paper>\n\n                  {/* Riepilogo Input */}\n                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>\n                      <Info sx={{ mr: 1, color: 'primary.main' }} />\n                      Riepilogo Input\n                    </Typography>\n                    <Grid container spacing={1}>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">Attività:</Typography>\n                        <Chip label={estimation.inputs.activity_type} size=\"small\" color=\"primary\" />\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">Quantità:</Typography>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {estimation.inputs.quantity_required} {estimation.inputs.activity_type === 'Posa' ? 'metri' : 'unità'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">Operatori:</Typography>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {estimation.inputs.number_of_operators}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">Condizioni:</Typography>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {estimation.inputs.environmental_conditions}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  </Paper>\n\n                  {/* Dettagli Produttività */}\n                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'success.50' }}>\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>\n                      <Speed sx={{ mr: 1, color: 'success.main' }} />\n                      Dettagli Produttività\n                    </Typography>\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} sm={6}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">Produttività base:</Typography>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'success.main' }}>\n                          {estimation.base_productivity.toFixed(1)} {estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} sm={6}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">Produttività attesa:</Typography>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'success.main' }}>\n                          {estimation.expected_productivity_per_operator.toFixed(1)} {estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h'}/op\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  </Paper>\n\n                  {/* Fattori di Correzione */}\n                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'warning.50' }}>\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>\n                      <Engineering sx={{ mr: 1, color: 'warning.main' }} />\n                      Fattori di Correzione\n                    </Typography>\n                    <Grid container spacing={1}>\n                      {Object.entries(estimation.correction_factors).map(([factor, value]) => (\n                        <Grid item xs={12} sm={4} key={factor}>\n                          <Box sx={{ textAlign: 'center', p: 1, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>\n                            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ textTransform: 'capitalize' }}>\n                              {factor.replace('_', ' ')}\n                            </Typography>\n                            <Typography\n                              variant=\"h6\"\n                              sx={{\n                                fontWeight: 600,\n                                color: value > 1 ? 'success.main' : value < 1 ? 'error.main' : 'text.primary'\n                              }}\n                            >\n                              {getCorrectionFactorIcon(value)} {(value * 100).toFixed(0)}%\n                            </Typography>\n                          </Box>\n                        </Grid>\n                      ))}\n                    </Grid>\n                  </Paper>\n\n                  {/* Note */}\n                  <Alert severity=\"info\" variant=\"outlined\">\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, mb: 1 }}>\n                      Note Importanti\n                    </Typography>\n                    <Box component=\"ul\" sx={{ m: 0, pl: 2 }}>\n                      <li>Le stime sono basate su dati storici e fattori di correzione</li>\n                      <li>I tempi effettivi possono variare in base a condizioni specifiche</li>\n                      <li>Utilizzare come riferimento per la pianificazione</li>\n                    </Box>\n                  </Alert>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>\n                  <Calculate sx={{ fontSize: 80, mb: 2, opacity: 0.3 }} />\n                  <Typography variant=\"h6\" sx={{ mb: 1 }}>\n                    Pronto per il Calcolo\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    Inserisci i parametri nel form a sinistra e clicca \"Calcola Stima\" per vedere i risultati\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default EstimationTool;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,WAAW,QACN,eAAe;AACtB,SACEC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,WAAW,EACXC,IAAI,EACJC,OAAO,QACF,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC;IACvCyC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,MAAM;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,wBAAwB,EAAE,SAAS;IACnCC,UAAU,EAAE,SAAS;IACrBC,mBAAmB,EAAE,CAAC;IACtBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,MAAM,EAAEC,SAAS,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMwD,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,EAChD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACrD;EAED,MAAMC,uBAAuB,GAAG,CAC9B;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACvC;EAED,MAAME,SAAS,GAAG,CAChB;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,CAC7C;EAED,MAAMG,gBAAgB,GAAG,CACvB;IAAEJ,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC7C;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;EAEDzD,SAAS,CAAC,MAAM;IACd6D,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7B,aAAa,CAAC8B,GAAG,CAAC,uBAAuB,CAAC;MACjEf,aAAa,CAACc,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF,CAAC;EAED,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEb,KAAK;MAAEc;IAAK,CAAC,GAAGF,CAAC,CAACG,MAAM;IACtChC,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC,IAAI,KAAK,QAAQ,GAAGG,UAAU,CAACjB,KAAK,CAAC,IAAI,EAAE,GAAGA;IACxD,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIH,MAAM,CAACgB,IAAI,CAAC,EAAE;MAChBf,SAAS,CAACkB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAK,CAAC,CAAC,CAAC;IAChD;;IAEA;IACA,IAAIpB,UAAU,EAAE;MACdC,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMwB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACrC,QAAQ,CAACG,aAAa,EAAEkC,SAAS,CAAClC,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACH,QAAQ,CAACI,iBAAiB,IAAIJ,QAAQ,CAACI,iBAAiB,IAAI,CAAC,EAAE;MAClEiC,SAAS,CAACjC,iBAAiB,GAAG,oCAAoC;IACpE;IACA,IAAI,CAACJ,QAAQ,CAACO,mBAAmB,IAAIP,QAAQ,CAACO,mBAAmB,GAAG,CAAC,EAAE;MACrE8B,SAAS,CAAC9B,mBAAmB,GAAG,uCAAuC;IACzE;IAEAS,SAAS,CAACqB,SAAS,CAAC;IACpB,OAAOC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBE,SAAS,CAAC,CAAC,CAAC,CAAC;;MAEb;MACA,MAAM2B,UAAU,GAAG;QACjBzC,aAAa,EAAEF,QAAQ,CAACE,aAAa,GAAG0C,QAAQ,CAAC5C,QAAQ,CAACE,aAAa,CAAC,GAAG,IAAI;QAC/EC,aAAa,EAAEH,QAAQ,CAACG,aAAa;QACrCC,iBAAiB,EAAE+B,UAAU,CAACnC,QAAQ,CAACI,iBAAiB,CAAC;QACzDC,wBAAwB,EAAEL,QAAQ,CAACK,wBAAwB;QAC3DC,UAAU,EAAEN,QAAQ,CAACM,UAAU;QAC/BC,mBAAmB,EAAEqC,QAAQ,CAAC5C,QAAQ,CAACO,mBAAmB,CAAC;QAC3DC,gBAAgB,EAAER,QAAQ,CAACQ;MAC7B,CAAC;MAED,MAAMgB,QAAQ,GAAG,MAAM7B,aAAa,CAACkD,IAAI,CAAC,wBAAwB,EAAEF,UAAU,CAAC;MAC/E/B,aAAa,CAACY,QAAQ,CAACE,IAAI,CAAC;IAE9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdnB,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CX,SAAS,CAAC;QACRgC,OAAO,EAAE,EAAAF,eAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpB,IAAI,cAAAqB,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI;MAC3C,CAAC,CAAC;IACJ,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,WAAW,GAAIC,KAAK,IAAK;IAC7B,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,EAAE,CAAC,SAAS;IAC3C,CAAC,MAAM,IAAIA,KAAK,GAAG,EAAE,EAAE;MACrB,MAAMG,CAAC,GAAGF,IAAI,CAACG,KAAK,CAACJ,KAAK,CAAC;MAC3B,MAAMK,CAAC,GAAGJ,IAAI,CAACC,KAAK,CAAC,CAACF,KAAK,GAAGG,CAAC,IAAI,EAAE,CAAC;MACtC,OAAOE,CAAC,GAAG,CAAC,GAAG,GAAGF,CAAC,KAAKE,CAAC,GAAG,GAAG,GAAGF,CAAC,GAAG;IACxC,CAAC,MAAM;MACL,MAAMG,IAAI,GAAGL,IAAI,CAACG,KAAK,CAACJ,KAAK,GAAG,EAAE,CAAC;MACnC,MAAMG,CAAC,GAAGF,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,EAAE,CAAC;MAChC,OAAOG,CAAC,GAAG,CAAC,GAAG,GAAGG,IAAI,KAAKH,CAAC,GAAG,GAAG,GAAGG,IAAI,GAAG;IAC9C;EACF,CAAC;EAED,MAAMC,wBAAwB,GAAIC,MAAM,IAAK;IAC3C,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,gBAAgB;IACvC,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,cAAc;IACrC,OAAO,eAAe;EACxB,CAAC;EAED,MAAMC,uBAAuB,GAAID,MAAM,IAAK;IAC1C,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IAC3B,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IAC3B,OAAO,IAAI;EACb,CAAC;EAED,oBACE9D,OAAA,CAAClC,GAAG;IAACkG,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhBlE,OAAA,CAAClC,GAAG;MAACkG,EAAE,EAAE;QAAEG,EAAE,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAF,QAAA,gBACtClE,OAAA,CAAClC,GAAG;QAACkG,EAAE,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEJ,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBAClFlE,OAAA,CAACX,SAAS;UAAC2E,EAAE,EAAE;YAAEQ,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,cAAc;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjE9E,OAAA,CAAChC,UAAU;UAAC+G,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAChB,EAAE,EAAE;YAAEiB,UAAU,EAAE;UAAI,CAAE;UAAAf,QAAA,EAAC;QAEjE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN9E,OAAA,CAAChC,UAAU;QAAC+G,OAAO,EAAC,WAAW;QAACN,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEvD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN9E,OAAA,CAAC/B,IAAI;MAACiH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjB,QAAA,gBAEzBlE,OAAA,CAAC/B,IAAI;QAACmH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBlE,OAAA,CAAC9B,IAAI;UAACqH,SAAS,EAAE,CAAE;UAAArB,QAAA,gBACjBlE,OAAA,CAAC5B,UAAU;YACToH,KAAK,EAAC,sBAAsB;YAC5BC,SAAS,EAAC,mEAAmE;YAC7EC,MAAM,eACJ1F,OAAA,CAACnB,MAAM;cAACmF,EAAE,EAAE;gBAAE2B,OAAO,EAAE;cAAe,CAAE;cAAAzB,QAAA,eACtClE,OAAA,CAACR,WAAW;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9E,OAAA,CAAC7B,WAAW;YAAA+F,QAAA,GACThD,MAAM,CAACiC,OAAO,iBACbnD,OAAA,CAACjB,KAAK;cAAC6G,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,EACnChD,MAAM,CAACiC;YAAO;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACR,eAED9E,OAAA,CAAClC,GAAG;cAACkH,SAAS,EAAC,MAAM;cAACa,QAAQ,EAAEjD,YAAa;cAACoB,EAAE,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEyB,aAAa,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAA7B,QAAA,gBAErGlE,OAAA,CAACxB,WAAW;gBAACwH,SAAS;gBAAClE,KAAK,EAAE,CAAC,CAACZ,MAAM,CAACZ,aAAc;gBAAA4D,QAAA,gBACnDlE,OAAA,CAACvB,UAAU;kBAAAyF,QAAA,EAAC;gBAAe;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC9E,OAAA,CAAC1B,MAAM;kBACL4D,IAAI,EAAC,eAAe;kBACpBb,KAAK,EAAElB,QAAQ,CAACG,aAAc;kBAC9B2F,QAAQ,EAAEjE,iBAAkB;kBAC5BV,KAAK,EAAC,oBAAiB;kBAAA4C,QAAA,EAEtB9C,aAAa,CAAC8E,GAAG,CAAC/D,IAAI,iBACrBnC,OAAA,CAACzB,QAAQ;oBAAkB8C,KAAK,EAAEc,IAAI,CAACd,KAAM;oBAAA6C,QAAA,EAC1C/B,IAAI,CAACb;kBAAK,GADEa,IAAI,CAACd,KAAK;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR5D,MAAM,CAACZ,aAAa,iBACnBN,OAAA,CAAChC,UAAU;kBAAC+G,OAAO,EAAC,SAAS;kBAACN,KAAK,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEmC,EAAE,EAAE;kBAAE,CAAE;kBAAAjC,QAAA,EACvDhD,MAAM,CAACZ;gBAAa;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eAGd9E,OAAA,CAACxB,WAAW;gBAACwH,SAAS;gBAAA9B,QAAA,gBACpBlE,OAAA,CAACvB,UAAU;kBAAAyF,QAAA,EAAC;gBAAY;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC9E,OAAA,CAAC1B,MAAM;kBACL4D,IAAI,EAAC,eAAe;kBACpBb,KAAK,EAAElB,QAAQ,CAACE,aAAc;kBAC9B4F,QAAQ,EAAEjE,iBAAkB;kBAC5BV,KAAK,EAAC,cAAc;kBAAA4C,QAAA,gBAEpBlE,OAAA,CAACzB,QAAQ;oBAAC8C,KAAK,EAAC,EAAE;oBAAA6C,QAAA,EAAC;kBAAkC;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAC/DlE,UAAU,CAACsF,GAAG,CAAC/D,IAAI,iBAClBnC,OAAA,CAACzB,QAAQ;oBAAyB8C,KAAK,EAAEc,IAAI,CAACiE,YAAa;oBAAAlC,QAAA,GACxD/B,IAAI,CAACkE,eAAe,EAAC,KAAG,EAAClE,IAAI,CAACmE,gBAAgB;kBAAA,GADlCnE,IAAI,CAACiE,YAAY;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEtB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGd9E,OAAA,CAAC3B,SAAS;gBACR2H,SAAS;gBACT7D,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,mBAAmB;gBACxBZ,KAAK,EAAE,wBAAwBnB,QAAQ,CAACG,aAAa,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,EAAG;gBAC3Fe,KAAK,EAAElB,QAAQ,CAACI,iBAAkB;gBAClC0F,QAAQ,EAAEjE,iBAAkB;gBAC5BF,KAAK,EAAE,CAAC,CAACZ,MAAM,CAACX,iBAAkB;gBAClCgG,UAAU,EAAErF,MAAM,CAACX,iBAAkB;gBACrCiG,UAAU,EAAE;kBAAEC,IAAI,EAAE,GAAG;kBAAEC,GAAG,EAAE;gBAAE;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAGF9E,OAAA,CAACxB,WAAW;gBAACwH,SAAS;gBAAA9B,QAAA,gBACpBlE,OAAA,CAACvB,UAAU;kBAAAyF,QAAA,EAAC;gBAAqB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9C9E,OAAA,CAAC1B,MAAM;kBACL4D,IAAI,EAAC,0BAA0B;kBAC/Bb,KAAK,EAAElB,QAAQ,CAACK,wBAAyB;kBACzCyF,QAAQ,EAAEjE,iBAAkB;kBAC5BV,KAAK,EAAC,uBAAuB;kBAAA4C,QAAA,EAE5B3C,uBAAuB,CAAC2E,GAAG,CAACS,SAAS,iBACpC3G,OAAA,CAACzB,QAAQ;oBAAuB8C,KAAK,EAAEsF,SAAS,CAACtF,KAAM;oBAAA6C,QAAA,EACpDyC,SAAS,CAACrF;kBAAK,GADHqF,SAAS,CAACtF,KAAK;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGd9E,OAAA,CAACxB,WAAW;gBAACwH,SAAS;gBAAA9B,QAAA,gBACpBlE,OAAA,CAACvB,UAAU;kBAAAyF,QAAA,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C9E,OAAA,CAAC1B,MAAM;kBACL4D,IAAI,EAAC,YAAY;kBACjBb,KAAK,EAAElB,QAAQ,CAACM,UAAW;kBAC3BwF,QAAQ,EAAEjE,iBAAkB;kBAC5BV,KAAK,EAAC,sBAAsB;kBAAA4C,QAAA,EAE3B1C,SAAS,CAAC0E,GAAG,CAACU,IAAI,iBACjB5G,OAAA,CAACzB,QAAQ;oBAAkB8C,KAAK,EAAEuF,IAAI,CAACvF,KAAM;oBAAA6C,QAAA,EAC1C0C,IAAI,CAACtF;kBAAK,GADEsF,IAAI,CAACvF,KAAK;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEd9E,OAAA,CAAC/B,IAAI;gBAACiH,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAjB,QAAA,gBAEzBlE,OAAA,CAAC/B,IAAI;kBAACmH,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACwB,EAAE,EAAE,CAAE;kBAAA3C,QAAA,eACvBlE,OAAA,CAAC3B,SAAS;oBACR2H,SAAS;oBACT7D,IAAI,EAAC,QAAQ;oBACbD,IAAI,EAAC,qBAAqB;oBAC1BZ,KAAK,EAAC,oBAAoB;oBAC1BD,KAAK,EAAElB,QAAQ,CAACO,mBAAoB;oBACpCuF,QAAQ,EAAEjE,iBAAkB;oBAC5BF,KAAK,EAAE,CAAC,CAACZ,MAAM,CAACR,mBAAoB;oBACpC6F,UAAU,EAAErF,MAAM,CAACR,mBAAoB;oBACvC8F,UAAU,EAAE;sBAAEE,GAAG,EAAE;oBAAE;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGP9E,OAAA,CAAC/B,IAAI;kBAACmH,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACwB,EAAE,EAAE,CAAE;kBAAA3C,QAAA,eACvBlE,OAAA,CAACxB,WAAW;oBAACwH,SAAS;oBAAA9B,QAAA,gBACpBlE,OAAA,CAACvB,UAAU;sBAAAyF,QAAA,EAAC;oBAAqB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC9C9E,OAAA,CAAC1B,MAAM;sBACL4D,IAAI,EAAC,kBAAkB;sBACvBb,KAAK,EAAElB,QAAQ,CAACQ,gBAAiB;sBACjCsF,QAAQ,EAAEjE,iBAAkB;sBAC5BV,KAAK,EAAC,uBAAuB;sBAAA4C,QAAA,EAE5BzC,gBAAgB,CAACyE,GAAG,CAACY,KAAK,iBACzB9G,OAAA,CAACzB,QAAQ;wBAAmB8C,KAAK,EAAEyF,KAAK,CAACzF,KAAM;wBAAA6C,QAAA,EAC5C4C,KAAK,CAACxF;sBAAK,GADCwF,KAAK,CAACzF,KAAK;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEhB,CACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEP9E,OAAA,CAACtB,MAAM;gBACLyD,IAAI,EAAC,QAAQ;gBACb4C,OAAO,EAAC,WAAW;gBACnBgC,IAAI,EAAC,OAAO;gBACZf,SAAS;gBACTgB,QAAQ,EAAEhG,OAAQ;gBAClBiG,SAAS,EAAEjG,OAAO,gBAAGhB,OAAA,CAAChB,gBAAgB;kBAAC+H,IAAI,EAAE;gBAAG;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG9E,OAAA,CAACX,SAAS;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpEd,EAAE,EAAE;kBACFkD,EAAE,EAAE,CAAC;kBACLC,UAAU,EAAE,kDAAkD;kBAC9D,SAAS,EAAE;oBACTA,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAjD,QAAA,EAEDlD,OAAO,GAAG,eAAe,GAAG;cAAe;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP9E,OAAA,CAAC/B,IAAI;QAACmH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBlE,OAAA,CAAC9B,IAAI;UAACqH,SAAS,EAAE,CAAE;UAAArB,QAAA,gBACjBlE,OAAA,CAAC5B,UAAU;YACToH,KAAK,EAAC,uBAAuB;YAC7BC,SAAS,EAAC,gDAAgD;YAC1DC,MAAM,eACJ1F,OAAA,CAACnB,MAAM;cAACmF,EAAE,EAAE;gBAAE2B,OAAO,EAAE;cAAe,CAAE;cAAAzB,QAAA,eACtClE,OAAA,CAACN,UAAU;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9E,OAAA,CAAC7B,WAAW;YAAA+F,QAAA,EACTpD,UAAU,gBACTd,OAAA,CAAClC,GAAG;cAACkG,EAAE,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEyB,aAAa,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAA7B,QAAA,gBAE5DlE,OAAA,CAACrB,KAAK;gBACJ4G,SAAS,EAAE,CAAE;gBACbvB,EAAE,EAAE;kBACFC,CAAC,EAAE,CAAC;kBACJkD,UAAU,EAAE,mDAAmD;kBAC/D1C,KAAK,EAAE;gBACT,CAAE;gBAAAP,QAAA,gBAEFlE,OAAA,CAAClC,GAAG;kBAACkG,EAAE,EAAE;oBAAEK,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEJ,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,gBACxDlE,OAAA,CAACL,WAAW;oBAACqE,EAAE,EAAE;sBAAEU,EAAE,EAAE;oBAAE;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9B9E,OAAA,CAAChC,UAAU;oBAAC+G,OAAO,EAAC,IAAI;oBAACf,EAAE,EAAE;sBAAEiB,UAAU,EAAE;oBAAI,CAAE;oBAAAf,QAAA,EAAC;kBAElD;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAEN9E,OAAA,CAAC/B,IAAI;kBAACiH,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAjB,QAAA,gBACzBlE,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACwB,EAAE,EAAE,CAAE;oBAAA3C,QAAA,eACvBlE,OAAA,CAAClC,GAAG;sBAACkG,EAAE,EAAE;wBAAEI,SAAS,EAAE;sBAAS,CAAE;sBAAAF,QAAA,gBAC/BlE,OAAA,CAAChC,UAAU;wBAAC+G,OAAO,EAAC,IAAI;wBAACf,EAAE,EAAE;0BAAEiB,UAAU,EAAE,GAAG;0BAAEd,EAAE,EAAE;wBAAE,CAAE;wBAAAD,QAAA,EACrDb,WAAW,CAACvC,UAAU,CAACsG,6BAA6B;sBAAC;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,eACb9E,OAAA,CAAChC,UAAU;wBAAC+G,OAAO,EAAC,OAAO;wBAACf,EAAE,EAAE;0BAAEqD,OAAO,EAAE;wBAAI,CAAE;wBAAAnD,QAAA,EAAC;sBAElD;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACP9E,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACwB,EAAE,EAAE,CAAE;oBAAA3C,QAAA,eACvBlE,OAAA,CAAClC,GAAG;sBAACkG,EAAE,EAAE;wBAAEI,SAAS,EAAE;sBAAS,CAAE;sBAAAF,QAAA,gBAC/BlE,OAAA,CAAChC,UAAU;wBAAC+G,OAAO,EAAC,IAAI;wBAACf,EAAE,EAAE;0BAAEiB,UAAU,EAAE,GAAG;0BAAEd,EAAE,EAAE;wBAAE,CAAE;wBAAAD,QAAA,GACrDpD,UAAU,CAACwG,yBAAyB,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GACnD;sBAAA;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACb9E,OAAA,CAAChC,UAAU;wBAAC+G,OAAO,EAAC,OAAO;wBAACf,EAAE,EAAE;0BAAEqD,OAAO,EAAE;wBAAI,CAAE;wBAAAnD,QAAA,EAAC;sBAElD;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGR9E,OAAA,CAACrB,KAAK;gBAAC4G,SAAS,EAAE,CAAE;gBAACvB,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAE0B,OAAO,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,gBACpDlE,OAAA,CAAChC,UAAU;kBAAC+G,OAAO,EAAC,WAAW;kBAACf,EAAE,EAAE;oBAAEiB,UAAU,EAAE,GAAG;oBAAEd,EAAE,EAAE,CAAC;oBAAEE,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAL,QAAA,gBACpGlE,OAAA,CAACJ,IAAI;oBAACoE,EAAE,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAED,KAAK,EAAE;oBAAe;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9E,OAAA,CAAC/B,IAAI;kBAACiH,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAjB,QAAA,gBACzBlE,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAnB,QAAA,gBACflE,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzE9E,OAAA,CAACpB,IAAI;sBAAC0C,KAAK,EAAER,UAAU,CAAC0G,MAAM,CAAClH,aAAc;sBAACyG,IAAI,EAAC,OAAO;sBAACtC,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACP9E,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAnB,QAAA,gBACflE,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzE9E,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACf,EAAE,EAAE;wBAAEiB,UAAU,EAAE;sBAAI,CAAE;sBAAAf,QAAA,GACjDpD,UAAU,CAAC0G,MAAM,CAACjH,iBAAiB,EAAC,GAAC,EAACO,UAAU,CAAC0G,MAAM,CAAClH,aAAa,KAAK,MAAM,GAAG,OAAO,GAAG,OAAO;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACP9E,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAnB,QAAA,gBACflE,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAU;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1E9E,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACf,EAAE,EAAE;wBAAEiB,UAAU,EAAE;sBAAI,CAAE;sBAAAf,QAAA,EACjDpD,UAAU,CAAC0G,MAAM,CAAC9G;oBAAmB;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACP9E,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAnB,QAAA,gBACflE,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAW;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3E9E,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACf,EAAE,EAAE;wBAAEiB,UAAU,EAAE;sBAAI,CAAE;sBAAAf,QAAA,EACjDpD,UAAU,CAAC0G,MAAM,CAAChH;oBAAwB;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGR9E,OAAA,CAACrB,KAAK;gBAAC4G,SAAS,EAAE,CAAE;gBAACvB,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAE0B,OAAO,EAAE;gBAAa,CAAE;gBAAAzB,QAAA,gBACvDlE,OAAA,CAAChC,UAAU;kBAAC+G,OAAO,EAAC,WAAW;kBAACf,EAAE,EAAE;oBAAEiB,UAAU,EAAE,GAAG;oBAAEd,EAAE,EAAE,CAAC;oBAAEE,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAL,QAAA,gBACpGlE,OAAA,CAACP,KAAK;oBAACuE,EAAE,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAED,KAAK,EAAE;oBAAe;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,4BAEjD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9E,OAAA,CAAC/B,IAAI;kBAACiH,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAjB,QAAA,gBACzBlE,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACwB,EAAE,EAAE,CAAE;oBAAA3C,QAAA,gBACvBlE,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAkB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClF9E,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,IAAI;sBAACf,EAAE,EAAE;wBAAEiB,UAAU,EAAE,GAAG;wBAAER,KAAK,EAAE;sBAAe,CAAE;sBAAAP,QAAA,GACrEpD,UAAU,CAAC2G,iBAAiB,CAACF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACzG,UAAU,CAAC0G,MAAM,CAAClH,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG,SAAS;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACP9E,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACwB,EAAE,EAAE,CAAE;oBAAA3C,QAAA,gBACvBlE,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAoB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACpF9E,OAAA,CAAChC,UAAU;sBAAC+G,OAAO,EAAC,IAAI;sBAACf,EAAE,EAAE;wBAAEiB,UAAU,EAAE,GAAG;wBAAER,KAAK,EAAE;sBAAe,CAAE;sBAAAP,QAAA,GACrEpD,UAAU,CAAC4G,kCAAkC,CAACH,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACzG,UAAU,CAAC0G,MAAM,CAAClH,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG,SAAS,EAAC,KAC7H;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGR9E,OAAA,CAACrB,KAAK;gBAAC4G,SAAS,EAAE,CAAE;gBAACvB,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAE0B,OAAO,EAAE;gBAAa,CAAE;gBAAAzB,QAAA,gBACvDlE,OAAA,CAAChC,UAAU;kBAAC+G,OAAO,EAAC,WAAW;kBAACf,EAAE,EAAE;oBAAEiB,UAAU,EAAE,GAAG;oBAAEd,EAAE,EAAE,CAAC;oBAAEE,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAL,QAAA,gBACpGlE,OAAA,CAACR,WAAW;oBAACwE,EAAE,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAED,KAAK,EAAE;oBAAe;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,yBAEvD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9E,OAAA,CAAC/B,IAAI;kBAACiH,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAjB,QAAA,EACxBzB,MAAM,CAACkF,OAAO,CAAC7G,UAAU,CAAC8G,kBAAkB,CAAC,CAAC1B,GAAG,CAAC,CAAC,CAACpC,MAAM,EAAEzC,KAAK,CAAC,kBACjErB,OAAA,CAAC/B,IAAI;oBAACmH,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACwB,EAAE,EAAE,CAAE;oBAAA3C,QAAA,eACvBlE,OAAA,CAAClC,GAAG;sBAACkG,EAAE,EAAE;wBAAEI,SAAS,EAAE,QAAQ;wBAAEH,CAAC,EAAE,CAAC;wBAAE4D,MAAM,EAAE,WAAW;wBAAEC,WAAW,EAAE,SAAS;wBAAEC,YAAY,EAAE;sBAAE,CAAE;sBAAA7D,QAAA,gBACnGlE,OAAA,CAAChC,UAAU;wBAAC+G,OAAO,EAAC,SAAS;wBAACN,KAAK,EAAC,gBAAgB;wBAACT,EAAE,EAAE;0BAAEgE,aAAa,EAAE;wBAAa,CAAE;wBAAA9D,QAAA,EACtFJ,MAAM,CAACmE,OAAO,CAAC,GAAG,EAAE,GAAG;sBAAC;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC,eACb9E,OAAA,CAAChC,UAAU;wBACT+G,OAAO,EAAC,IAAI;wBACZf,EAAE,EAAE;0BACFiB,UAAU,EAAE,GAAG;0BACfR,KAAK,EAAEpD,KAAK,GAAG,CAAC,GAAG,cAAc,GAAGA,KAAK,GAAG,CAAC,GAAG,YAAY,GAAG;wBACjE,CAAE;wBAAA6C,QAAA,GAEDH,uBAAuB,CAAC1C,KAAK,CAAC,EAAC,GAAC,EAAC,CAACA,KAAK,GAAG,GAAG,EAAEkG,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7D;sBAAA;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC,GAduBhB,MAAM;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAe/B,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGR9E,OAAA,CAACjB,KAAK;gBAAC6G,QAAQ,EAAC,MAAM;gBAACb,OAAO,EAAC,UAAU;gBAAAb,QAAA,gBACvClE,OAAA,CAAChC,UAAU;kBAAC+G,OAAO,EAAC,WAAW;kBAACf,EAAE,EAAE;oBAAEiB,UAAU,EAAE,GAAG;oBAAEd,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEhE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9E,OAAA,CAAClC,GAAG;kBAACkH,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEL,CAAC,EAAE,CAAC;oBAAEuE,EAAE,EAAE;kBAAE,CAAE;kBAAAhE,QAAA,gBACtClE,OAAA;oBAAAkE,QAAA,EAAI;kBAA4D;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrE9E,OAAA;oBAAAkE,QAAA,EAAI;kBAAiE;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1E9E,OAAA;oBAAAkE,QAAA,EAAI;kBAAiD;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAEN9E,OAAA,CAAClC,GAAG;cAACkG,EAAE,EAAE;gBAAEI,SAAS,EAAE,QAAQ;gBAAE8C,EAAE,EAAE,CAAC;gBAAEzC,KAAK,EAAE;cAAiB,CAAE;cAAAP,QAAA,gBAC/DlE,OAAA,CAACX,SAAS;gBAAC2E,EAAE,EAAE;kBAAEQ,QAAQ,EAAE,EAAE;kBAAEL,EAAE,EAAE,CAAC;kBAAEkD,OAAO,EAAE;gBAAI;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxD9E,OAAA,CAAChC,UAAU;gBAAC+G,OAAO,EAAC,IAAI;gBAACf,EAAE,EAAE;kBAAEG,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,EAAC;cAExC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9E,OAAA,CAAChC,UAAU;gBAAC+G,OAAO,EAAC,OAAO;gBAAAb,QAAA,EAAC;cAE5B;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5E,EAAA,CA/eID,cAAc;AAAAkI,EAAA,GAAdlI,cAAc;AAifpB,eAAeA,cAAc;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}