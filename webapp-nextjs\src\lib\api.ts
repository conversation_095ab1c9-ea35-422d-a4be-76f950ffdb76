import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// Configurazione base per l'API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'

// Crea istanza axios con configurazione base
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Interceptor per aggiungere il token di autenticazione
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Interceptor per gestire le risposte e gli errori
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token scaduto o non valido
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_data')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Tipi per le risposte API
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// Funzioni helper per le chiamate API
export const api = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get<T>(url, config)
    return response.data
  },

  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<T>(url, data, config)
    return response.data
  },

  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put<T>(url, data, config)
    return response.data
  },

  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.patch<T>(url, data, config)
    return response.data
  },

  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete<T>(url, config)
    return response.data
  },
}

// Servizi API specifici per CABLYS
export const authApi = {
  // Login utente
  login: (credentials: { username: string; password: string }) =>
    api.post<{ access_token: string; token_type: string; user: any }>('/auth/login', credentials),

  // Login cantiere
  loginCantiere: (credentials: { codice_cantiere: string; password_cantiere: string }) =>
    api.post<{ access_token: string; token_type: string; cantiere: any }>('/auth/login-cantiere', credentials),

  // Verifica token
  verifyToken: () =>
    api.get<{ user: any }>('/auth/me'),

  // Logout
  logout: () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('user_data')
    window.location.href = '/login'
  }
}

export const caviApi = {
  // Ottieni tutti i cavi
  getCavi: (cantiereId: number, params?: any) =>
    api.get<any[]>(`/cavi/${cantiereId}`, { params }),

  // Ottieni cavo specifico
  getCavo: (cantiereId: number, idCavo: string) =>
    api.get<any>(`/cavi/${cantiereId}/${idCavo}`),

  // Crea nuovo cavo
  createCavo: (cantiereId: number, cavo: any) =>
    api.post<any>(`/cavi/${cantiereId}`, cavo),

  // Aggiorna cavo
  updateCavo: (cantiereId: number, idCavo: string, updates: any) =>
    api.put<any>(`/cavi/${cantiereId}/${idCavo}`, updates),

  // Elimina cavo
  deleteCavo: (cantiereId: number, idCavo: string) =>
    api.delete(`/cavi/${cantiereId}/${idCavo}`),

  // Aggiorna metri posati
  updateMetriPosati: (cantiereId: number, idCavo: string, metri: number) =>
    api.patch<any>(`/cavi/${cantiereId}/${idCavo}/metri-posati`, { metri_posati: metri }),

  // Aggiorna bobina
  updateBobina: (cantiereId: number, idCavo: string, bobina: string) =>
    api.patch<any>(`/cavi/${cantiereId}/${idCavo}/bobina`, { id_bobina: bobina }),

  // Aggiorna collegamento
  updateCollegamento: (cantiereId: number, idCavo: string, collegamento: number) =>
    api.patch<any>(`/cavi/${cantiereId}/${idCavo}/collegamento`, { collegamenti: collegamento }),
}

export const parcoCaviApi = {
  // Ottieni tutte le bobine
  getBobine: (cantiereId: number) =>
    api.get<any[]>(`/parco-cavi/${cantiereId}`),

  // Ottieni bobina specifica
  getBobina: (cantiereId: number, idBobina: string) =>
    api.get<any>(`/parco-cavi/${cantiereId}/${idBobina}`),

  // Crea nuova bobina
  createBobina: (cantiereId: number, bobina: any) =>
    api.post<any>(`/parco-cavi/${cantiereId}`, bobina),

  // Aggiorna bobina
  updateBobina: (cantiereId: number, idBobina: string, updates: any) =>
    api.put<any>(`/parco-cavi/${cantiereId}/${idBobina}`, updates),

  // Elimina bobina
  deleteBobina: (cantiereId: number, idBobina: string) =>
    api.delete(`/parco-cavi/${cantiereId}/${idBobina}`),
}

export const comandeApi = {
  // Ottieni tutte le comande
  getComande: (cantiereId: number) =>
    api.get<any[]>(`/comande/${cantiereId}`),

  // Ottieni comanda specifica
  getComanda: (cantiereId: number, codiceComanda: string) =>
    api.get<any>(`/comande/${cantiereId}/${codiceComanda}`),

  // Crea nuova comanda
  createComanda: (cantiereId: number, comanda: any) =>
    api.post<any>(`/comande/${cantiereId}`, comanda),

  // Aggiorna comanda
  updateComanda: (cantiereId: number, codiceComanda: string, updates: any) =>
    api.put<any>(`/comande/${cantiereId}/${codiceComanda}`, updates),

  // Elimina comanda
  deleteComanda: (cantiereId: number, codiceComanda: string) =>
    api.delete(`/comande/${cantiereId}/${codiceComanda}`),

  // Assegna cavi a comanda
  assegnaCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>
    api.post<any>(`/comande/${cantiereId}/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),
}

export const responsabiliApi = {
  // Ottieni tutti i responsabili
  getResponsabili: (cantiereId: number) =>
    api.get<any[]>(`/responsabili/${cantiereId}`),

  // Crea nuovo responsabile
  createResponsabile: (cantiereId: number, responsabile: any) =>
    api.post<any>(`/responsabili/${cantiereId}`, responsabile),

  // Aggiorna responsabile
  updateResponsabile: (cantiereId: number, id: number, updates: any) =>
    api.put<any>(`/responsabili/${cantiereId}/${id}`, updates),

  // Elimina responsabile
  deleteResponsabile: (cantiereId: number, id: number) =>
    api.delete(`/responsabili/${cantiereId}/${id}`),
}

export const reportsApi = {
  // Report avanzamento
  getReportAvanzamento: (cantiereId: number) =>
    api.get<any>(`/reports/${cantiereId}/avanzamento`),

  // Report BOQ
  getReportBOQ: (cantiereId: number) =>
    api.get<any>(`/reports/${cantiereId}/boq`),

  // Report utilizzo bobine
  getReportUtilizzoBobine: (cantiereId: number) =>
    api.get<any>(`/reports/${cantiereId}/utilizzo-bobine`),

  // Report progress
  getReportProgress: (cantiereId: number) =>
    api.get<any>(`/reports/${cantiereId}/progress`),
}

export const cantieriApi = {
  // Ottieni tutti i cantieri
  getCantieri: () =>
    api.get<any[]>('/cantieri'),

  // Ottieni cantiere specifico
  getCantiere: (id: number) =>
    api.get<any>(`/cantieri/${id}`),

  // Crea nuovo cantiere
  createCantiere: (cantiere: any) =>
    api.post<any>('/cantieri', cantiere),

  // Aggiorna cantiere
  updateCantiere: (id: number, updates: any) =>
    api.put<any>(`/cantieri/${id}`, updates),
}

export default apiClient
