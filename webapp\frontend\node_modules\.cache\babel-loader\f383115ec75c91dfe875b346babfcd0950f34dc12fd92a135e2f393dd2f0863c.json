{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, Select, MenuItem, FormControl, InputLabel, TextField, Chip, Avatar, LinearProgress, Divider, Paper, IconButton, Tooltip as MuiTooltip, Alert } from '@mui/material';\nimport { TrendingUp, Schedule, Engineering, Speed, Assessment, FilterList, Refresh, Download, Construction } from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\n// Registra i componenti Chart.js\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement);\nconst Dashboard = () => {\n  _s();\n  var _statistics$totalQuan, _statistics$totalManH, _statistics$averagePr;\n  const [workLogs, setWorkLogs] = useState([]);\n  const [statistics, setStatistics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [selectedCantiere, setSelectedCantiere] = useState('');\n  const [cantieri, setCantieri] = useState([]);\n  const [dateRange, setDateRange] = useState({\n    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    // 30 giorni fa\n    end: new Date().toISOString().split('T')[0] // oggi\n  });\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n  useEffect(() => {\n    if (cantieri.length > 0) {\n      loadDashboardData();\n    }\n  }, [selectedCantiere, dateRange, cantieri]);\n  const loadInitialData = async () => {\n    try {\n      const cantieriRes = await axiosInstance.get('/cantieri');\n      setCantieri(cantieriRes.data || []);\n\n      // Seleziona il primo cantiere automaticamente\n      if (cantieriRes.data && cantieriRes.data.length > 0) {\n        setSelectedCantiere(cantieriRes.data[0].id_cantiere.toString());\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento cantieri:', error);\n    }\n  };\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Parametri per le chiamate API\n      const params = {\n        id_cantiere: selectedCantiere,\n        start_date: dateRange.start,\n        end_date: dateRange.end,\n        per_page: 100\n      };\n\n      // Carica work logs\n      const workLogsRes = await axiosInstance.get('/v1/work-logs', {\n        params\n      });\n      setWorkLogs(workLogsRes.data.work_logs || []);\n\n      // Calcola statistiche\n      calculateStatistics(workLogsRes.data.work_logs || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati dashboard:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const calculateStatistics = logs => {\n    const stats = {\n      totalLogs: logs.length,\n      totalQuantity: 0,\n      totalManHours: 0,\n      averageProductivity: 0,\n      byActivity: {},\n      byOperator: {},\n      byConditions: {},\n      dailyTrend: {},\n      productivityTrend: []\n    };\n    logs.forEach(log => {\n      // Totali\n      stats.totalQuantity += log.quantity || 0;\n      stats.totalManHours += log.total_man_hours || 0;\n\n      // Per attività\n      if (!stats.byActivity[log.activity_type]) {\n        stats.byActivity[log.activity_type] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byActivity[log.activity_type].count++;\n      stats.byActivity[log.activity_type].quantity += log.quantity || 0;\n      stats.byActivity[log.activity_type].manHours += log.total_man_hours || 0;\n\n      // Per operatore\n      const operatorKey = `${log.operator_id}`;\n      if (!stats.byOperator[operatorKey]) {\n        stats.byOperator[operatorKey] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byOperator[operatorKey].count++;\n      stats.byOperator[operatorKey].quantity += log.quantity || 0;\n      stats.byOperator[operatorKey].manHours += log.total_man_hours || 0;\n\n      // Per condizioni ambientali\n      if (!stats.byConditions[log.environmental_conditions]) {\n        stats.byConditions[log.environmental_conditions] = {\n          count: 0,\n          quantity: 0,\n          productivity: 0\n        };\n      }\n      stats.byConditions[log.environmental_conditions].count++;\n      stats.byConditions[log.environmental_conditions].quantity += log.quantity || 0;\n\n      // Trend giornaliero\n      const date = new Date(log.start_timestamp).toISOString().split('T')[0];\n      if (!stats.dailyTrend[date]) {\n        stats.dailyTrend[date] = {\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.dailyTrend[date].quantity += log.quantity || 0;\n      stats.dailyTrend[date].manHours += log.total_man_hours || 0;\n    });\n\n    // Calcola produttività medie\n    stats.averageProductivity = stats.totalManHours > 0 ? stats.totalQuantity / stats.totalManHours : 0;\n    Object.keys(stats.byActivity).forEach(activity => {\n      const data = stats.byActivity[activity];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.byOperator).forEach(operator => {\n      const data = stats.byOperator[operator];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.byConditions).forEach(condition => {\n      const data = stats.byConditions[condition];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.dailyTrend).forEach(date => {\n      const data = stats.dailyTrend[date];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    setStatistics(stats);\n  };\n\n  // Configurazioni grafici\n  const productivityByActivityChart = {\n    labels: Object.keys(statistics.byActivity || {}),\n    datasets: [{\n      label: 'Produttività (unità/ora)',\n      data: Object.values(statistics.byActivity || {}).map(d => d.productivity.toFixed(2)),\n      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n      borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],\n      borderWidth: 1\n    }]\n  };\n  const dailyTrendChart = {\n    labels: Object.keys(statistics.dailyTrend || {}).sort(),\n    datasets: [{\n      label: 'Quantità Giornaliera',\n      data: Object.keys(statistics.dailyTrend || {}).sort().map(date => statistics.dailyTrend[date].quantity),\n      borderColor: '#3B82F6',\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      tension: 0.1\n    }, {\n      label: 'Produttività Giornaliera',\n      data: Object.keys(statistics.dailyTrend || {}).sort().map(date => statistics.dailyTrend[date].productivity),\n      borderColor: '#10B981',\n      backgroundColor: 'rgba(16, 185, 129, 0.1)',\n      tension: 0.1,\n      yAxisID: 'y1'\n    }]\n  };\n  const conditionsChart = {\n    labels: Object.keys(statistics.byConditions || {}),\n    datasets: [{\n      data: Object.values(statistics.byConditions || {}).map(d => d.count),\n      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n      borderWidth: 2\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true\n      },\n      y1: {\n        type: 'linear',\n        display: true,\n        position: 'right',\n        grid: {\n          drawOnChartArea: false\n        }\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Caricamento dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Assessment, {\n            sx: {\n              fontSize: 32,\n              color: 'primary.main',\n              mr: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            sx: {\n              fontWeight: 700\n            },\n            children: \"Dashboard Produttivit\\xE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(MuiTooltip, {\n            title: \"Aggiorna dati\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: loadDashboardData,\n              color: \"primary\",\n              children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MuiTooltip, {\n            title: \"Esporta report\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              children: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 1,\n        sx: {\n          p: 2,\n          bgcolor: 'grey.50'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FilterList, {\n            sx: {\n              color: 'text.secondary'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            color: \"text.secondary\",\n            sx: {\n              mr: 2\n            },\n            children: \"Filtri:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 200\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedCantiere,\n              onChange: e => setSelectedCantiere(e.target.value),\n              label: \"Cantiere\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutti i cantieri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: cantiere.id_cantiere,\n                children: cantiere.commessa\n              }, cantiere.id_cantiere, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            type: \"date\",\n            label: \"Data inizio\",\n            size: \"small\",\n            value: dateRange.start,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              start: e.target.value\n            })),\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            type: \"date\",\n            label: \"Data fine\",\n            size: \"small\",\n            value: dateRange.end,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              end: e.target.value\n            })),\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            position: 'relative',\n            overflow: 'visible'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Work Logs Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: statistics.totalLogs\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(Engineering, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Quantit\\xE0 Totale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: (_statistics$totalQuan = statistics.totalQuantity) === null || _statistics$totalQuan === void 0 ? void 0 : _statistics$totalQuan.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"metri/unit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Ore-Uomo Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: (_statistics$totalManH = statistics.totalManHours) === null || _statistics$totalManH === void 0 ? void 0 : _statistics$totalManH.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"ore lavorate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(Schedule, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Produttivit\\xE0 Media\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: (_statistics$averagePr = statistics.averageProductivity) === null || _statistics$averagePr === void 0 ? void 0 : _statistics$averagePr.toFixed(2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"unit\\xE0/ora\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(Speed, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Produttivit\\xE0 per Attivit\\xE0\",\n            subheader: \"Confronto performance tra diverse tipologie di lavoro\",\n            avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: 'primary.main'\n              },\n              children: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: Object.keys(statistics.byActivity || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: 400\n              },\n              children: /*#__PURE__*/_jsxDEV(Bar, {\n                data: productivityByActivityChart,\n                options: {\n                  ...chartOptions,\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 8,\n                color: 'text.secondary'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Assessment, {\n                sx: {\n                  fontSize: 64,\n                  mb: 2,\n                  opacity: 0.3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"Nessun dato disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Aggiungi dei work logs per visualizzare le statistiche\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Condizioni Ambientali\",\n            subheader: \"Distribuzione dei lavori per ambiente\",\n            avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: 'secondary.main'\n              },\n              children: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: Object.keys(statistics.byConditions || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: 300,\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Doughnut, {\n                data: conditionsChart,\n                options: {\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      position: 'bottom'\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 4,\n                color: 'text.secondary'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Assessment, {\n                sx: {\n                  fontSize: 48,\n                  mb: 1,\n                  opacity: 0.3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Nessun dato disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        title: \"Trend Giornaliero\",\n        subheader: \"Andamento della produttivit\\xE0 nel tempo\",\n        avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: 'success.main'\n          },\n          children: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        children: Object.keys(statistics.dailyTrend || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            height: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: dailyTrendChart,\n            options: {\n              ...chartOptions,\n              responsive: true,\n              maintainAspectRatio: false,\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Quantità'\n                  }\n                },\n                y1: {\n                  type: 'linear',\n                  display: true,\n                  position: 'right',\n                  title: {\n                    display: true,\n                    text: 'Produttività'\n                  },\n                  grid: {\n                    drawOnChartArea: false\n                  }\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            py: 8,\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n            sx: {\n              fontSize: 64,\n              mb: 2,\n              opacity: 0.3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Nessun trend disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"I dati del trend appariranno dopo alcuni giorni di lavoro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 3,\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        title: \"Dettagli per Attivit\\xE0\",\n        subheader: \"Analisi dettagliata delle performance per tipologia di lavoro\",\n        avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: 'info.main'\n          },\n          children: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 0\n        },\n        children: Object.keys(statistics.byActivity || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            overflow: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            component: \"table\",\n            sx: {\n              width: '100%',\n              borderCollapse: 'collapse'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              component: \"thead\",\n              sx: {\n                bgcolor: 'grey.50'\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                component: \"tr\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Attivit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Work Logs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Quantit\\xE0 Totale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Ore-Uomo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Produttivit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              component: \"tbody\",\n              children: Object.entries(statistics.byActivity || {}).map(([activity, data], index) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"tr\",\n                sx: {\n                  '&:hover': {\n                    bgcolor: 'grey.50'\n                  },\n                  borderBottom: index < Object.keys(statistics.byActivity).length - 1 ? '1px solid' : 'none',\n                  borderColor: 'divider'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: activity,\n                    color: \"primary\",\n                    variant: \"outlined\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2,\n                    fontWeight: 500\n                  },\n                  children: data.count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: data.quantity.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: data.manHours.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600\n                      },\n                      children: data.productivity.toFixed(2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 686,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"unit\\xE0/ora\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 23\n                }, this)]\n              }, activity, true, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            py: 8,\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Assessment, {\n            sx: {\n              fontSize: 64,\n              mb: 2,\n              opacity: 0.3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Nessun dettaglio disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"I dettagli appariranno dopo aver registrato dei work logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 304,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"wA34wQBYuMVKTEKEJCMksNeQ7T4=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Line", "Bar", "Doughnut", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Select", "MenuItem", "FormControl", "InputLabel", "TextField", "Chip", "Avatar", "LinearProgress", "Divider", "Paper", "IconButton", "MuiTooltip", "<PERSON><PERSON>", "TrendingUp", "Schedule", "Engineering", "Speed", "Assessment", "FilterList", "Refresh", "Download", "Construction", "useAuth", "axiosInstance", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "_statistics$totalQuan", "_statistics$totalManH", "_statistics$averagePr", "workLogs", "setWorkLogs", "statistics", "setStatistics", "loading", "setLoading", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "cantieri", "set<PERSON><PERSON><PERSON>", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "now", "toISOString", "split", "end", "loadInitialData", "length", "loadDashboardData", "cantieriRes", "get", "data", "id_cantiere", "toString", "error", "console", "params", "start_date", "end_date", "per_page", "workLogsRes", "work_logs", "calculateStatistics", "logs", "stats", "totalLogs", "totalQuantity", "totalManHours", "averageProductivity", "byActivity", "byOperator", "byConditions", "dailyTrend", "productivityTrend", "for<PERSON>ach", "log", "quantity", "total_man_hours", "activity_type", "count", "manHours", "productivity", "operatorKey", "operator_id", "environmental_conditions", "date", "start_timestamp", "Object", "keys", "activity", "operator", "condition", "productivityByActivityChart", "labels", "datasets", "label", "values", "map", "d", "toFixed", "backgroundColor", "borderColor", "borderWidth", "dailyTrendChart", "sort", "tension", "yAxisID", "<PERSON><PERSON><PERSON>", "chartOptions", "responsive", "plugins", "legend", "position", "scales", "y", "beginAtZero", "y1", "type", "display", "grid", "drawOnChartArea", "sx", "p", "textAlign", "children", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "justifyContent", "alignItems", "fontSize", "mr", "component", "fontWeight", "gap", "title", "onClick", "elevation", "bgcolor", "flexWrap", "size", "min<PERSON><PERSON><PERSON>", "value", "onChange", "e", "target", "cantiere", "commessa", "prev", "InputLabelProps", "shrink", "container", "spacing", "item", "xs", "sm", "md", "background", "overflow", "pb", "opacity", "width", "height", "lg", "subheader", "avatar", "options", "maintainAspectRatio", "py", "text", "borderCollapse", "entries", "index", "borderBottom", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  TextField,\n  Chip,\n  Avatar,\n  LinearProgress,\n  Divider,\n  Paper,\n  IconButton,\n  Tooltip as MuiTooltip,\n  Alert\n} from '@mui/material';\nimport {\n  TrendingUp,\n  Schedule,\n  Engineering,\n  Speed,\n  Assessment,\n  FilterList,\n  Refresh,\n  Download,\n  Construction\n} from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\n// Registra i componenti Chart.js\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\nconst Dashboard = () => {\n  const [workLogs, setWorkLogs] = useState([]);\n  const [statistics, setStatistics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [selectedCantiere, setSelectedCantiere] = useState('');\n  const [cantieri, setCantieri] = useState([]);\n  const [dateRange, setDateRange] = useState({\n    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 giorni fa\n    end: new Date().toISOString().split('T')[0] // oggi\n  });\n\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n\n  useEffect(() => {\n    if (cantieri.length > 0) {\n      loadDashboardData();\n    }\n  }, [selectedCantiere, dateRange, cantieri]);\n\n  const loadInitialData = async () => {\n    try {\n      const cantieriRes = await axiosInstance.get('/cantieri');\n      setCantieri(cantieriRes.data || []);\n      \n      // Seleziona il primo cantiere automaticamente\n      if (cantieriRes.data && cantieriRes.data.length > 0) {\n        setSelectedCantiere(cantieriRes.data[0].id_cantiere.toString());\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento cantieri:', error);\n    }\n  };\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Parametri per le chiamate API\n      const params = {\n        id_cantiere: selectedCantiere,\n        start_date: dateRange.start,\n        end_date: dateRange.end,\n        per_page: 100\n      };\n\n      // Carica work logs\n      const workLogsRes = await axiosInstance.get('/v1/work-logs', { params });\n      setWorkLogs(workLogsRes.data.work_logs || []);\n\n      // Calcola statistiche\n      calculateStatistics(workLogsRes.data.work_logs || []);\n\n    } catch (error) {\n      console.error('Errore nel caricamento dati dashboard:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateStatistics = (logs) => {\n    const stats = {\n      totalLogs: logs.length,\n      totalQuantity: 0,\n      totalManHours: 0,\n      averageProductivity: 0,\n      byActivity: {},\n      byOperator: {},\n      byConditions: {},\n      dailyTrend: {},\n      productivityTrend: []\n    };\n\n    logs.forEach(log => {\n      // Totali\n      stats.totalQuantity += log.quantity || 0;\n      stats.totalManHours += log.total_man_hours || 0;\n\n      // Per attività\n      if (!stats.byActivity[log.activity_type]) {\n        stats.byActivity[log.activity_type] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byActivity[log.activity_type].count++;\n      stats.byActivity[log.activity_type].quantity += log.quantity || 0;\n      stats.byActivity[log.activity_type].manHours += log.total_man_hours || 0;\n\n      // Per operatore\n      const operatorKey = `${log.operator_id}`;\n      if (!stats.byOperator[operatorKey]) {\n        stats.byOperator[operatorKey] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byOperator[operatorKey].count++;\n      stats.byOperator[operatorKey].quantity += log.quantity || 0;\n      stats.byOperator[operatorKey].manHours += log.total_man_hours || 0;\n\n      // Per condizioni ambientali\n      if (!stats.byConditions[log.environmental_conditions]) {\n        stats.byConditions[log.environmental_conditions] = {\n          count: 0,\n          quantity: 0,\n          productivity: 0\n        };\n      }\n      stats.byConditions[log.environmental_conditions].count++;\n      stats.byConditions[log.environmental_conditions].quantity += log.quantity || 0;\n\n      // Trend giornaliero\n      const date = new Date(log.start_timestamp).toISOString().split('T')[0];\n      if (!stats.dailyTrend[date]) {\n        stats.dailyTrend[date] = {\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.dailyTrend[date].quantity += log.quantity || 0;\n      stats.dailyTrend[date].manHours += log.total_man_hours || 0;\n    });\n\n    // Calcola produttività medie\n    stats.averageProductivity = stats.totalManHours > 0 ? stats.totalQuantity / stats.totalManHours : 0;\n\n    Object.keys(stats.byActivity).forEach(activity => {\n      const data = stats.byActivity[activity];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.byOperator).forEach(operator => {\n      const data = stats.byOperator[operator];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.byConditions).forEach(condition => {\n      const data = stats.byConditions[condition];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.dailyTrend).forEach(date => {\n      const data = stats.dailyTrend[date];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    setStatistics(stats);\n  };\n\n  // Configurazioni grafici\n  const productivityByActivityChart = {\n    labels: Object.keys(statistics.byActivity || {}),\n    datasets: [\n      {\n        label: 'Produttività (unità/ora)',\n        data: Object.values(statistics.byActivity || {}).map(d => d.productivity.toFixed(2)),\n        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n        borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const dailyTrendChart = {\n    labels: Object.keys(statistics.dailyTrend || {}).sort(),\n    datasets: [\n      {\n        label: 'Quantità Giornaliera',\n        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => \n          statistics.dailyTrend[date].quantity\n        ),\n        borderColor: '#3B82F6',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        tension: 0.1,\n      },\n      {\n        label: 'Produttività Giornaliera',\n        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => \n          statistics.dailyTrend[date].productivity\n        ),\n        borderColor: '#10B981',\n        backgroundColor: 'rgba(16, 185, 129, 0.1)',\n        tension: 0.1,\n        yAxisID: 'y1',\n      },\n    ],\n  };\n\n  const conditionsChart = {\n    labels: Object.keys(statistics.byConditions || {}),\n    datasets: [\n      {\n        data: Object.values(statistics.byConditions || {}).map(d => d.count),\n        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n      },\n      y1: {\n        type: 'linear',\n        display: true,\n        position: 'right',\n        grid: {\n          drawOnChartArea: false,\n        },\n      },\n    },\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ p: 4, textAlign: 'center' }}>\n        <LinearProgress sx={{ mb: 2 }} />\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Caricamento dashboard...\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header con filtri */}\n      <Box sx={{ mb: 4 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Assessment sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />\n            <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700 }}>\n              Dashboard Produttività\n            </Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            <MuiTooltip title=\"Aggiorna dati\">\n              <IconButton onClick={loadDashboardData} color=\"primary\">\n                <Refresh />\n              </IconButton>\n            </MuiTooltip>\n            <MuiTooltip title=\"Esporta report\">\n              <IconButton color=\"primary\">\n                <Download />\n              </IconButton>\n            </MuiTooltip>\n          </Box>\n        </Box>\n\n        {/* Filtri moderni */}\n        <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>\n            <FilterList sx={{ color: 'text.secondary' }} />\n            <Typography variant=\"subtitle2\" color=\"text.secondary\" sx={{ mr: 2 }}>\n              Filtri:\n            </Typography>\n\n            <FormControl size=\"small\" sx={{ minWidth: 200 }}>\n              <InputLabel>Cantiere</InputLabel>\n              <Select\n                value={selectedCantiere}\n                onChange={(e) => setSelectedCantiere(e.target.value)}\n                label=\"Cantiere\"\n              >\n                <MenuItem value=\"\">Tutti i cantieri</MenuItem>\n                {cantieri.map(cantiere => (\n                  <MenuItem key={cantiere.id_cantiere} value={cantiere.id_cantiere}>\n                    {cantiere.commessa}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n\n            <TextField\n              type=\"date\"\n              label=\"Data inizio\"\n              size=\"small\"\n              value={dateRange.start}\n              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n              InputLabelProps={{ shrink: true }}\n            />\n\n            <TextField\n              type=\"date\"\n              label=\"Data fine\"\n              size=\"small\"\n              value={dateRange.end}\n              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n              InputLabelProps={{ shrink: true }}\n            />\n          </Box>\n        </Paper>\n      </Box>\n\n      {/* KPI Cards moderne */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              position: 'relative',\n              overflow: 'visible'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Work Logs Totali\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.totalLogs}\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <Engineering sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n              color: 'white'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Quantità Totale\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.totalQuantity?.toFixed(1)}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    metri/unità\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <TrendingUp sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n              color: 'white'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Ore-Uomo Totali\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.totalManHours?.toFixed(1)}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    ore lavorate\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <Schedule sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n              color: 'white'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Produttività Media\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.averageProductivity?.toFixed(2)}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    unità/ora\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <Speed sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Grafici moderni */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        {/* Produttività per Attività */}\n        <Grid item xs={12} lg={8}>\n          <Card elevation={3}>\n            <CardHeader\n              title=\"Produttività per Attività\"\n              subheader=\"Confronto performance tra diverse tipologie di lavoro\"\n              avatar={\n                <Avatar sx={{ bgcolor: 'primary.main' }}>\n                  <TrendingUp />\n                </Avatar>\n              }\n            />\n            <CardContent>\n              {Object.keys(statistics.byActivity || {}).length > 0 ? (\n                <Box sx={{ height: 400 }}>\n                  <Bar data={productivityByActivityChart} options={{\n                    ...chartOptions,\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        display: false\n                      }\n                    }\n                  }} />\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>\n                  <Assessment sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />\n                  <Typography variant=\"h6\">Nessun dato disponibile</Typography>\n                  <Typography variant=\"body2\">\n                    Aggiungi dei work logs per visualizzare le statistiche\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Distribuzione Condizioni Ambientali */}\n        <Grid item xs={12} lg={4}>\n          <Card elevation={3}>\n            <CardHeader\n              title=\"Condizioni Ambientali\"\n              subheader=\"Distribuzione dei lavori per ambiente\"\n              avatar={\n                <Avatar sx={{ bgcolor: 'secondary.main' }}>\n                  <Assessment />\n                </Avatar>\n              }\n            />\n            <CardContent>\n              {Object.keys(statistics.byConditions || {}).length > 0 ? (\n                <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>\n                  <Doughnut data={conditionsChart} options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'bottom'\n                      }\n                    }\n                  }} />\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>\n                  <Assessment sx={{ fontSize: 48, mb: 1, opacity: 0.3 }} />\n                  <Typography variant=\"body2\">Nessun dato disponibile</Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Trend Giornaliero */}\n      <Card elevation={3} sx={{ mb: 4 }}>\n        <CardHeader\n          title=\"Trend Giornaliero\"\n          subheader=\"Andamento della produttività nel tempo\"\n          avatar={\n            <Avatar sx={{ bgcolor: 'success.main' }}>\n              <TrendingUp />\n            </Avatar>\n          }\n        />\n        <CardContent>\n          {Object.keys(statistics.dailyTrend || {}).length > 0 ? (\n            <Box sx={{ height: 400 }}>\n              <Line data={dailyTrendChart} options={{\n                ...chartOptions,\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    title: {\n                      display: true,\n                      text: 'Quantità'\n                    }\n                  },\n                  y1: {\n                    type: 'linear',\n                    display: true,\n                    position: 'right',\n                    title: {\n                      display: true,\n                      text: 'Produttività'\n                    },\n                    grid: {\n                      drawOnChartArea: false,\n                    },\n                  },\n                }\n              }} />\n            </Box>\n          ) : (\n            <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>\n              <TrendingUp sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />\n              <Typography variant=\"h6\">Nessun trend disponibile</Typography>\n              <Typography variant=\"body2\">\n                I dati del trend appariranno dopo alcuni giorni di lavoro\n              </Typography>\n            </Box>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Tabella Dettagli per Attività */}\n      <Card elevation={3}>\n        <CardHeader\n          title=\"Dettagli per Attività\"\n          subheader=\"Analisi dettagliata delle performance per tipologia di lavoro\"\n          avatar={\n            <Avatar sx={{ bgcolor: 'info.main' }}>\n              <Assessment />\n            </Avatar>\n          }\n        />\n        <CardContent sx={{ p: 0 }}>\n          {Object.keys(statistics.byActivity || {}).length > 0 ? (\n            <Box sx={{ overflow: 'auto' }}>\n              <Box component=\"table\" sx={{ width: '100%', borderCollapse: 'collapse' }}>\n                <Box component=\"thead\" sx={{ bgcolor: 'grey.50' }}>\n                  <Box component=\"tr\">\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Attività\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Work Logs\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Quantità Totale\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Ore-Uomo\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Produttività\n                    </Box>\n                  </Box>\n                </Box>\n                <Box component=\"tbody\">\n                  {Object.entries(statistics.byActivity || {}).map(([activity, data], index) => (\n                    <Box\n                      component=\"tr\"\n                      key={activity}\n                      sx={{\n                        '&:hover': { bgcolor: 'grey.50' },\n                        borderBottom: index < Object.keys(statistics.byActivity).length - 1 ? '1px solid' : 'none',\n                        borderColor: 'divider'\n                      }}\n                    >\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        <Chip\n                          label={activity}\n                          color=\"primary\"\n                          variant=\"outlined\"\n                          size=\"small\"\n                        />\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2, fontWeight: 500 }}>\n                        {data.count}\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        {data.quantity.toFixed(1)}\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        {data.manHours.toFixed(1)}\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                            {data.productivity.toFixed(2)}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            unità/ora\n                          </Typography>\n                        </Box>\n                      </Box>\n                    </Box>\n                  ))}\n                </Box>\n              </Box>\n            </Box>\n          ) : (\n            <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>\n              <Assessment sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />\n              <Typography variant=\"h6\">Nessun dettaglio disponibile</Typography>\n              <Typography variant=\"body2\">\n                I dettagli appariranno dopo aver registrato dei work logs\n              </Typography>\n            </Box>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AACrD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVpB,OAAO,IAAIqB,UAAU,EACrBC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,YAAY,QACP,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,aAAa,MAAM,4BAA4B;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA1C,OAAO,CAAC2C,QAAQ,CACd1C,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMmC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC;IACzCmE,KAAK,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IACpFC,GAAG,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC;EAEFtE,SAAS,CAAC,MAAM;IACdwE,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAENxE,SAAS,CAAC,MAAM;IACd,IAAI8D,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAE;MACvBC,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACd,gBAAgB,EAAEI,SAAS,EAAEF,QAAQ,CAAC,CAAC;EAE3C,MAAMU,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,WAAW,GAAG,MAAM9B,aAAa,CAAC+B,GAAG,CAAC,WAAW,CAAC;MACxDb,WAAW,CAACY,WAAW,CAACE,IAAI,IAAI,EAAE,CAAC;;MAEnC;MACA,IAAIF,WAAW,CAACE,IAAI,IAAIF,WAAW,CAACE,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;QACnDZ,mBAAmB,CAACc,WAAW,CAACE,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;EAED,MAAMN,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMuB,MAAM,GAAG;QACbJ,WAAW,EAAElB,gBAAgB;QAC7BuB,UAAU,EAAEnB,SAAS,CAACE,KAAK;QAC3BkB,QAAQ,EAAEpB,SAAS,CAACO,GAAG;QACvBc,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,MAAMC,WAAW,GAAG,MAAMzC,aAAa,CAAC+B,GAAG,CAAC,eAAe,EAAE;QAAEM;MAAO,CAAC,CAAC;MACxE3B,WAAW,CAAC+B,WAAW,CAACT,IAAI,CAACU,SAAS,IAAI,EAAE,CAAC;;MAE7C;MACAC,mBAAmB,CAACF,WAAW,CAACT,IAAI,CAACU,SAAS,IAAI,EAAE,CAAC;IAEvD,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,KAAK,GAAG;MACZC,SAAS,EAAEF,IAAI,CAAChB,MAAM;MACtBmB,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,mBAAmB,EAAE,CAAC;MACtBC,UAAU,EAAE,CAAC,CAAC;MACdC,UAAU,EAAE,CAAC,CAAC;MACdC,YAAY,EAAE,CAAC,CAAC;MAChBC,UAAU,EAAE,CAAC,CAAC;MACdC,iBAAiB,EAAE;IACrB,CAAC;IAEDV,IAAI,CAACW,OAAO,CAACC,GAAG,IAAI;MAClB;MACAX,KAAK,CAACE,aAAa,IAAIS,GAAG,CAACC,QAAQ,IAAI,CAAC;MACxCZ,KAAK,CAACG,aAAa,IAAIQ,GAAG,CAACE,eAAe,IAAI,CAAC;;MAE/C;MACA,IAAI,CAACb,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,EAAE;QACxCd,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,GAAG;UACpCC,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,CAACC,KAAK,EAAE;MAC3Cf,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,CAACF,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;MACjEZ,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,CAACE,QAAQ,IAAIL,GAAG,CAACE,eAAe,IAAI,CAAC;;MAExE;MACA,MAAMK,WAAW,GAAG,GAAGP,GAAG,CAACQ,WAAW,EAAE;MACxC,IAAI,CAACnB,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,EAAE;QAClClB,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,GAAG;UAC9BH,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,CAACH,KAAK,EAAE;MACrCf,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,CAACN,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;MAC3DZ,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,CAACF,QAAQ,IAAIL,GAAG,CAACE,eAAe,IAAI,CAAC;;MAElE;MACA,IAAI,CAACb,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,EAAE;QACrDpB,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,GAAG;UACjDL,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXK,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,CAACL,KAAK,EAAE;MACxDf,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,CAACR,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;;MAE9E;MACA,MAAMS,IAAI,GAAG,IAAI5C,IAAI,CAACkC,GAAG,CAACW,eAAe,CAAC,CAAC3C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtE,IAAI,CAACoB,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,EAAE;QAC3BrB,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,GAAG;UACvBT,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,CAACT,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;MACpDZ,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,CAACL,QAAQ,IAAIL,GAAG,CAACE,eAAe,IAAI,CAAC;IAC7D,CAAC,CAAC;;IAEF;IACAb,KAAK,CAACI,mBAAmB,GAAGJ,KAAK,CAACG,aAAa,GAAG,CAAC,GAAGH,KAAK,CAACE,aAAa,GAAGF,KAAK,CAACG,aAAa,GAAG,CAAC;IAEnGoB,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACK,UAAU,CAAC,CAACK,OAAO,CAACe,QAAQ,IAAI;MAChD,MAAMtC,IAAI,GAAGa,KAAK,CAACK,UAAU,CAACoB,QAAQ,CAAC;MACvCtC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACM,UAAU,CAAC,CAACI,OAAO,CAACgB,QAAQ,IAAI;MAChD,MAAMvC,IAAI,GAAGa,KAAK,CAACM,UAAU,CAACoB,QAAQ,CAAC;MACvCvC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACO,YAAY,CAAC,CAACG,OAAO,CAACiB,SAAS,IAAI;MACnD,MAAMxC,IAAI,GAAGa,KAAK,CAACO,YAAY,CAACoB,SAAS,CAAC;MAC1CxC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACQ,UAAU,CAAC,CAACE,OAAO,CAACW,IAAI,IAAI;MAC5C,MAAMlC,IAAI,GAAGa,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC;MACnClC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFjD,aAAa,CAACiC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAM4B,2BAA2B,GAAG;IAClCC,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC;IAChDyB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,0BAA0B;MACjC5C,IAAI,EAAEoC,MAAM,CAACS,MAAM,CAAClE,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC4B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,YAAY,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC;MACpFC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC7DC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACzDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBV,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,CAAC,CAAC;IACvDV,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,sBAAsB;MAC7B5C,IAAI,EAAEoC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,CAAC,CAAC,CAACP,GAAG,CAACZ,IAAI,IAC5DvD,UAAU,CAAC0C,UAAU,CAACa,IAAI,CAAC,CAACT,QAC9B,CAAC;MACDyB,WAAW,EAAE,SAAS;MACtBD,eAAe,EAAE,yBAAyB;MAC1CK,OAAO,EAAE;IACX,CAAC,EACD;MACEV,KAAK,EAAE,0BAA0B;MACjC5C,IAAI,EAAEoC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,CAAC,CAAC,CAACP,GAAG,CAACZ,IAAI,IAC5DvD,UAAU,CAAC0C,UAAU,CAACa,IAAI,CAAC,CAACJ,YAC9B,CAAC;MACDoB,WAAW,EAAE,SAAS;MACtBD,eAAe,EAAE,yBAAyB;MAC1CK,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBd,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACyC,YAAY,IAAI,CAAC,CAAC,CAAC;IAClDuB,QAAQ,EAAE,CACR;MACE3C,IAAI,EAAEoC,MAAM,CAACS,MAAM,CAAClE,UAAU,CAACyC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,KAAK,CAAC;MACpEqB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC7DE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMM,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE;MACf,CAAC;MACDC,EAAE,EAAE;QACFC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,IAAI;QACbN,QAAQ,EAAE,OAAO;QACjBO,IAAI,EAAE;UACJC,eAAe,EAAE;QACnB;MACF;IACF;EACF,CAAC;EAED,IAAIxF,OAAO,EAAE;IACX,oBACEX,OAAA,CAAChC,GAAG;MAACoI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrCvG,OAAA,CAAClB,cAAc;QAACsH,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjC5G,OAAA,CAAC9B,UAAU;QAAC2I,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEhD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE5G,OAAA,CAAChC,GAAG;IAACoI,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBvG,OAAA,CAAChC,GAAG;MAACoI,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjBvG,OAAA,CAAChC,GAAG;QAACoI,EAAE,EAAE;UAAEH,OAAO,EAAE,MAAM;UAAEc,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAER,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBACzFvG,OAAA,CAAChC,GAAG;UAACoI,EAAE,EAAE;YAAEH,OAAO,EAAE,MAAM;YAAEe,UAAU,EAAE;UAAS,CAAE;UAAAT,QAAA,gBACjDvG,OAAA,CAACR,UAAU;YAAC4G,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAEH,KAAK,EAAE,cAAc;cAAEI,EAAE,EAAE;YAAE;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClE5G,OAAA,CAAC9B,UAAU;YAAC2I,OAAO,EAAC,IAAI;YAACM,SAAS,EAAC,IAAI;YAACf,EAAE,EAAE;cAAEgB,UAAU,EAAE;YAAI,CAAE;YAAAb,QAAA,EAAC;UAEjE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN5G,OAAA,CAAChC,GAAG;UAACoI,EAAE,EAAE;YAAEH,OAAO,EAAE,MAAM;YAAEoB,GAAG,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACnCvG,OAAA,CAACd,UAAU;YAACoI,KAAK,EAAC,eAAe;YAAAf,QAAA,eAC/BvG,OAAA,CAACf,UAAU;cAACsI,OAAO,EAAE5F,iBAAkB;cAACmF,KAAK,EAAC,SAAS;cAAAP,QAAA,eACrDvG,OAAA,CAACN,OAAO;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACb5G,OAAA,CAACd,UAAU;YAACoI,KAAK,EAAC,gBAAgB;YAAAf,QAAA,eAChCvG,OAAA,CAACf,UAAU;cAAC6H,KAAK,EAAC,SAAS;cAAAP,QAAA,eACzBvG,OAAA,CAACL,QAAQ;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5G,OAAA,CAAChB,KAAK;QAACwI,SAAS,EAAE,CAAE;QAACpB,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEoB,OAAO,EAAE;QAAU,CAAE;QAAAlB,QAAA,eACpDvG,OAAA,CAAChC,GAAG;UAACoI,EAAE,EAAE;YAAEH,OAAO,EAAE,MAAM;YAAEe,UAAU,EAAE,QAAQ;YAAEK,GAAG,EAAE,CAAC;YAAEK,QAAQ,EAAE;UAAO,CAAE;UAAAnB,QAAA,gBAC3EvG,OAAA,CAACP,UAAU;YAAC2G,EAAE,EAAE;cAAEU,KAAK,EAAE;YAAiB;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C5G,OAAA,CAAC9B,UAAU;YAAC2I,OAAO,EAAC,WAAW;YAACC,KAAK,EAAC,gBAAgB;YAACV,EAAE,EAAE;cAAEc,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb5G,OAAA,CAACvB,WAAW;YAACkJ,IAAI,EAAC,OAAO;YAACvB,EAAE,EAAE;cAAEwB,QAAQ,EAAE;YAAI,CAAE;YAAArB,QAAA,gBAC9CvG,OAAA,CAACtB,UAAU;cAAA6H,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjC5G,OAAA,CAACzB,MAAM;cACLsJ,KAAK,EAAEhH,gBAAiB;cACxBiH,QAAQ,EAAGC,CAAC,IAAKjH,mBAAmB,CAACiH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDnD,KAAK,EAAC,UAAU;cAAA6B,QAAA,gBAEhBvG,OAAA,CAACxB,QAAQ;gBAACqJ,KAAK,EAAC,EAAE;gBAAAtB,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAC7C7F,QAAQ,CAAC6D,GAAG,CAACqD,QAAQ,iBACpBjI,OAAA,CAACxB,QAAQ;gBAA4BqJ,KAAK,EAAEI,QAAQ,CAAClG,WAAY;gBAAAwE,QAAA,EAC9D0B,QAAQ,CAACC;cAAQ,GADLD,QAAQ,CAAClG,WAAW;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEd5G,OAAA,CAACrB,SAAS;YACRqH,IAAI,EAAC,MAAM;YACXtB,KAAK,EAAC,aAAa;YACnBiD,IAAI,EAAC,OAAO;YACZE,KAAK,EAAE5G,SAAS,CAACE,KAAM;YACvB2G,QAAQ,EAAGC,CAAC,IAAK7G,YAAY,CAACiH,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEhH,KAAK,EAAE4G,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC5EO,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEF5G,OAAA,CAACrB,SAAS;YACRqH,IAAI,EAAC,MAAM;YACXtB,KAAK,EAAC,WAAW;YACjBiD,IAAI,EAAC,OAAO;YACZE,KAAK,EAAE5G,SAAS,CAACO,GAAI;YACrBsG,QAAQ,EAAGC,CAAC,IAAK7G,YAAY,CAACiH,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE3G,GAAG,EAAEuG,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC1EO,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN5G,OAAA,CAAC7B,IAAI;MAACmK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACnC,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACxCvG,OAAA,CAAC7B,IAAI;QAACqK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BvG,OAAA,CAAC5B,IAAI;UACHoJ,SAAS,EAAE,CAAE;UACbpB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE,OAAO;YACdnB,QAAQ,EAAE,UAAU;YACpBkD,QAAQ,EAAE;UACZ,CAAE;UAAAtC,QAAA,eAEFvG,OAAA,CAAC3B,WAAW;YAAC+H,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBvG,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAR,QAAA,gBAClFvG,OAAA,CAAChC,GAAG;gBAAAuI,QAAA,gBACFvG,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEgB,UAAU,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAC9C9F,UAAU,CAACmC;gBAAS;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5G,OAAA,CAACnB,MAAM;gBAACuH,EAAE,EAAE;kBAAEqB,OAAO,EAAE,uBAAuB;kBAAEuB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEvG,OAAA,CAACV,WAAW;kBAAC8G,EAAE,EAAE;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5G,OAAA,CAAC7B,IAAI;QAACqK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BvG,OAAA,CAAC5B,IAAI;UACHoJ,SAAS,EAAE,CAAE;UACbpB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,eAEFvG,OAAA,CAAC3B,WAAW;YAAC+H,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBvG,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAR,QAAA,gBAClFvG,OAAA,CAAChC,GAAG;gBAAAuI,QAAA,gBACFvG,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEgB,UAAU,EAAE;kBAAI,CAAE;kBAAAb,QAAA,GAAAnG,qBAAA,GAC9CK,UAAU,CAACoC,aAAa,cAAAzC,qBAAA,uBAAxBA,qBAAA,CAA0B0E,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACb5G,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE;kBAAI,CAAE;kBAAAxC,QAAA,EAAC;gBAEpD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5G,OAAA,CAACnB,MAAM;gBAACuH,EAAE,EAAE;kBAAEqB,OAAO,EAAE,uBAAuB;kBAAEuB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEvG,OAAA,CAACZ,UAAU;kBAACgH,EAAE,EAAE;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5G,OAAA,CAAC7B,IAAI;QAACqK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BvG,OAAA,CAAC5B,IAAI;UACHoJ,SAAS,EAAE,CAAE;UACbpB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,eAEFvG,OAAA,CAAC3B,WAAW;YAAC+H,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBvG,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAR,QAAA,gBAClFvG,OAAA,CAAChC,GAAG;gBAAAuI,QAAA,gBACFvG,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEgB,UAAU,EAAE;kBAAI,CAAE;kBAAAb,QAAA,GAAAlG,qBAAA,GAC9CI,UAAU,CAACqC,aAAa,cAAAzC,qBAAA,uBAAxBA,qBAAA,CAA0ByE,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACb5G,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE;kBAAI,CAAE;kBAAAxC,QAAA,EAAC;gBAEpD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5G,OAAA,CAACnB,MAAM;gBAACuH,EAAE,EAAE;kBAAEqB,OAAO,EAAE,uBAAuB;kBAAEuB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEvG,OAAA,CAACX,QAAQ;kBAAC+G,EAAE,EAAE;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5G,OAAA,CAAC7B,IAAI;QAACqK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BvG,OAAA,CAAC5B,IAAI;UACHoJ,SAAS,EAAE,CAAE;UACbpB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,eAEFvG,OAAA,CAAC3B,WAAW;YAAC+H,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBvG,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEe,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAR,QAAA,gBAClFvG,OAAA,CAAChC,GAAG;gBAAAuI,QAAA,gBACFvG,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEgB,UAAU,EAAE;kBAAI,CAAE;kBAAAb,QAAA,GAAAjG,qBAAA,GAC9CG,UAAU,CAACsC,mBAAmB,cAAAzC,qBAAA,uBAA9BA,qBAAA,CAAgCwE,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACb5G,OAAA,CAAC9B,UAAU;kBAAC2I,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE;kBAAI,CAAE;kBAAAxC,QAAA,EAAC;gBAEpD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5G,OAAA,CAACnB,MAAM;gBAACuH,EAAE,EAAE;kBAAEqB,OAAO,EAAE,uBAAuB;kBAAEuB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEvG,OAAA,CAACT,KAAK;kBAAC6G,EAAE,EAAE;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP5G,OAAA,CAAC7B,IAAI;MAACmK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACnC,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAExCvG,OAAA,CAAC7B,IAAI;QAACqK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACvBvG,OAAA,CAAC5B,IAAI;UAACoJ,SAAS,EAAE,CAAE;UAAAjB,QAAA,gBACjBvG,OAAA,CAAC1B,UAAU;YACTgJ,KAAK,EAAC,iCAA2B;YACjC6B,SAAS,EAAC,uDAAuD;YACjEC,MAAM,eACJpJ,OAAA,CAACnB,MAAM;cAACuH,EAAE,EAAE;gBAAEqB,OAAO,EAAE;cAAe,CAAE;cAAAlB,QAAA,eACtCvG,OAAA,CAACZ,UAAU;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF5G,OAAA,CAAC3B,WAAW;YAAAkI,QAAA,EACTrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC,CAACtB,MAAM,GAAG,CAAC,gBAClD1B,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAE6C,MAAM,EAAE;cAAI,CAAE;cAAA1C,QAAA,eACvBvG,OAAA,CAAC7C,GAAG;gBAAC2E,IAAI,EAAEyC,2BAA4B;gBAAC8E,OAAO,EAAE;kBAC/C,GAAG9D,YAAY;kBACfC,UAAU,EAAE,IAAI;kBAChB8D,mBAAmB,EAAE,KAAK;kBAC1B7D,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNO,OAAO,EAAE;oBACX;kBACF;gBACF;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,gBAEN5G,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAEE,SAAS,EAAE,QAAQ;gBAAEiD,EAAE,EAAE,CAAC;gBAAEzC,KAAK,EAAE;cAAiB,CAAE;cAAAP,QAAA,gBAC/DvG,OAAA,CAACR,UAAU;gBAAC4G,EAAE,EAAE;kBAAEa,QAAQ,EAAE,EAAE;kBAAET,EAAE,EAAE,CAAC;kBAAEuC,OAAO,EAAE;gBAAI;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzD5G,OAAA,CAAC9B,UAAU;gBAAC2I,OAAO,EAAC,IAAI;gBAAAN,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7D5G,OAAA,CAAC9B,UAAU;gBAAC2I,OAAO,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAE5B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP5G,OAAA,CAAC7B,IAAI;QAACqK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACvBvG,OAAA,CAAC5B,IAAI;UAACoJ,SAAS,EAAE,CAAE;UAAAjB,QAAA,gBACjBvG,OAAA,CAAC1B,UAAU;YACTgJ,KAAK,EAAC,uBAAuB;YAC7B6B,SAAS,EAAC,uCAAuC;YACjDC,MAAM,eACJpJ,OAAA,CAACnB,MAAM;cAACuH,EAAE,EAAE;gBAAEqB,OAAO,EAAE;cAAiB,CAAE;cAAAlB,QAAA,eACxCvG,OAAA,CAACR,UAAU;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF5G,OAAA,CAAC3B,WAAW;YAAAkI,QAAA,EACTrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACyC,YAAY,IAAI,CAAC,CAAC,CAAC,CAACxB,MAAM,GAAG,CAAC,gBACpD1B,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAE6C,MAAM,EAAE,GAAG;gBAAEhD,OAAO,EAAE,MAAM;gBAAEc,cAAc,EAAE;cAAS,CAAE;cAAAR,QAAA,eAClEvG,OAAA,CAAC5C,QAAQ;gBAAC0E,IAAI,EAAEwD,eAAgB;gBAAC+D,OAAO,EAAE;kBACxC7D,UAAU,EAAE,IAAI;kBAChB8D,mBAAmB,EAAE,KAAK;kBAC1B7D,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNC,QAAQ,EAAE;oBACZ;kBACF;gBACF;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,gBAEN5G,OAAA,CAAChC,GAAG;cAACoI,EAAE,EAAE;gBAAEE,SAAS,EAAE,QAAQ;gBAAEiD,EAAE,EAAE,CAAC;gBAAEzC,KAAK,EAAE;cAAiB,CAAE;cAAAP,QAAA,gBAC/DvG,OAAA,CAACR,UAAU;gBAAC4G,EAAE,EAAE;kBAAEa,QAAQ,EAAE,EAAE;kBAAET,EAAE,EAAE,CAAC;kBAAEuC,OAAO,EAAE;gBAAI;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzD5G,OAAA,CAAC9B,UAAU;gBAAC2I,OAAO,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP5G,OAAA,CAAC5B,IAAI;MAACoJ,SAAS,EAAE,CAAE;MAACpB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAChCvG,OAAA,CAAC1B,UAAU;QACTgJ,KAAK,EAAC,mBAAmB;QACzB6B,SAAS,EAAC,2CAAwC;QAClDC,MAAM,eACJpJ,OAAA,CAACnB,MAAM;UAACuH,EAAE,EAAE;YAAEqB,OAAO,EAAE;UAAe,CAAE;UAAAlB,QAAA,eACtCvG,OAAA,CAACZ,UAAU;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACF5G,OAAA,CAAC3B,WAAW;QAAAkI,QAAA,EACTrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACzB,MAAM,GAAG,CAAC,gBAClD1B,OAAA,CAAChC,GAAG;UAACoI,EAAE,EAAE;YAAE6C,MAAM,EAAE;UAAI,CAAE;UAAA1C,QAAA,eACvBvG,OAAA,CAAC9C,IAAI;YAAC4E,IAAI,EAAEoD,eAAgB;YAACmE,OAAO,EAAE;cACpC,GAAG9D,YAAY;cACfC,UAAU,EAAE,IAAI;cAChB8D,mBAAmB,EAAE,KAAK;cAC1B1D,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBwB,KAAK,EAAE;oBACLrB,OAAO,EAAE,IAAI;oBACbuD,IAAI,EAAE;kBACR;gBACF,CAAC;gBACDzD,EAAE,EAAE;kBACFC,IAAI,EAAE,QAAQ;kBACdC,OAAO,EAAE,IAAI;kBACbN,QAAQ,EAAE,OAAO;kBACjB2B,KAAK,EAAE;oBACLrB,OAAO,EAAE,IAAI;oBACbuD,IAAI,EAAE;kBACR,CAAC;kBACDtD,IAAI,EAAE;oBACJC,eAAe,EAAE;kBACnB;gBACF;cACF;YACF;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,gBAEN5G,OAAA,CAAChC,GAAG;UAACoI,EAAE,EAAE;YAAEE,SAAS,EAAE,QAAQ;YAAEiD,EAAE,EAAE,CAAC;YAAEzC,KAAK,EAAE;UAAiB,CAAE;UAAAP,QAAA,gBAC/DvG,OAAA,CAACZ,UAAU;YAACgH,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAET,EAAE,EAAE,CAAC;cAAEuC,OAAO,EAAE;YAAI;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzD5G,OAAA,CAAC9B,UAAU;YAAC2I,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9D5G,OAAA,CAAC9B,UAAU;YAAC2I,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP5G,OAAA,CAAC5B,IAAI;MAACoJ,SAAS,EAAE,CAAE;MAAAjB,QAAA,gBACjBvG,OAAA,CAAC1B,UAAU;QACTgJ,KAAK,EAAC,0BAAuB;QAC7B6B,SAAS,EAAC,+DAA+D;QACzEC,MAAM,eACJpJ,OAAA,CAACnB,MAAM;UAACuH,EAAE,EAAE;YAAEqB,OAAO,EAAE;UAAY,CAAE;UAAAlB,QAAA,eACnCvG,OAAA,CAACR,UAAU;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACF5G,OAAA,CAAC3B,WAAW;QAAC+H,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,EACvBrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC,CAACtB,MAAM,GAAG,CAAC,gBAClD1B,OAAA,CAAChC,GAAG;UAACoI,EAAE,EAAE;YAAEyC,QAAQ,EAAE;UAAO,CAAE;UAAAtC,QAAA,eAC5BvG,OAAA,CAAChC,GAAG;YAACmJ,SAAS,EAAC,OAAO;YAACf,EAAE,EAAE;cAAE4C,KAAK,EAAE,MAAM;cAAES,cAAc,EAAE;YAAW,CAAE;YAAAlD,QAAA,gBACvEvG,OAAA,CAAChC,GAAG;cAACmJ,SAAS,EAAC,OAAO;cAACf,EAAE,EAAE;gBAAEqB,OAAO,EAAE;cAAU,CAAE;cAAAlB,QAAA,eAChDvG,OAAA,CAAChC,GAAG;gBAACmJ,SAAS,EAAC,IAAI;gBAAAZ,QAAA,gBACjBvG,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEc,UAAU,EAAE,GAAG;oBAAEN,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEc,UAAU,EAAE,GAAG;oBAAEN,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEc,UAAU,EAAE,GAAG;oBAAEN,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEc,UAAU,EAAE,GAAG;oBAAEN,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEc,UAAU,EAAE,GAAG;oBAAEN,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5G,OAAA,CAAChC,GAAG;cAACmJ,SAAS,EAAC,OAAO;cAAAZ,QAAA,EACnBrC,MAAM,CAACwF,OAAO,CAACjJ,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC4B,GAAG,CAAC,CAAC,CAACR,QAAQ,EAAEtC,IAAI,CAAC,EAAE6H,KAAK,kBACvE3J,OAAA,CAAChC,GAAG;gBACFmJ,SAAS,EAAC,IAAI;gBAEdf,EAAE,EAAE;kBACF,SAAS,EAAE;oBAAEqB,OAAO,EAAE;kBAAU,CAAC;kBACjCmC,YAAY,EAAED,KAAK,GAAGzF,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACuC,UAAU,CAAC,CAACtB,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM;kBAC1FsD,WAAW,EAAE;gBACf,CAAE;gBAAAuB,QAAA,gBAEFvG,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,eAC/BvG,OAAA,CAACpB,IAAI;oBACH8F,KAAK,EAAEN,QAAS;oBAChB0C,KAAK,EAAC,SAAS;oBACfD,OAAO,EAAC,UAAU;oBAClBc,IAAI,EAAC;kBAAO;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEe,UAAU,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAC/CzE,IAAI,CAAC4B;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,EAC9BzE,IAAI,CAACyB,QAAQ,CAACuB,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,EAC9BzE,IAAI,CAAC6B,QAAQ,CAACmB,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN5G,OAAA,CAAChC,GAAG;kBAACmJ,SAAS,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,eAC/BvG,OAAA,CAAChC,GAAG;oBAACoI,EAAE,EAAE;sBAAEH,OAAO,EAAE,MAAM;sBAAEe,UAAU,EAAE,QAAQ;sBAAEK,GAAG,EAAE;oBAAE,CAAE;oBAAAd,QAAA,gBACzDvG,OAAA,CAAC9B,UAAU;sBAAC2I,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEgB,UAAU,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EACjDzE,IAAI,CAAC8B,YAAY,CAACkB,OAAO,CAAC,CAAC;oBAAC;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACb5G,OAAA,CAAC9B,UAAU;sBAAC2I,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAjCDxC,QAAQ;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkCV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN5G,OAAA,CAAChC,GAAG;UAACoI,EAAE,EAAE;YAAEE,SAAS,EAAE,QAAQ;YAAEiD,EAAE,EAAE,CAAC;YAAEzC,KAAK,EAAE;UAAiB,CAAE;UAAAP,QAAA,gBAC/DvG,OAAA,CAACR,UAAU;YAAC4G,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAET,EAAE,EAAE,CAAC;cAAEuC,OAAO,EAAE;YAAI;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzD5G,OAAA,CAAC9B,UAAU;YAAC2I,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClE5G,OAAA,CAAC9B,UAAU;YAAC2I,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzG,EAAA,CAxoBID,SAAS;AAAA2J,EAAA,GAAT3J,SAAS;AA0oBf,eAAeA,SAAS;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}