// Tipi per l'autenticazione
export interface User {
  id_utente: number
  username: string
  ruolo: string
  data_scadenza?: string
  abilitato: boolean
  ragione_sociale?: string
  indirizzo?: string
  nazione?: string
  email?: string
  vat?: string
  referente_aziendale?: string
  telefono?: string
  cellulare?: string
}

export interface Cantiere {
  id_cantiere: number
  commessa: string
  descrizione: string
  nome_cliente?: string
  indirizzo_cantiere?: string
  citta_cantiere?: string
  nazione_cantiere?: string
  data_creazione: string
  password_cantiere: string
  id_utente: number
  codice_univoco: string
  riferimenti_normativi?: string
  documentazione_progetto?: string
}

// Tipi per i cavi
export interface Cavo {
  id_cavo: string
  id_cantiere: number
  revisione_ufficiale: string
  sistema?: string
  utility?: string
  colore_cavo?: string
  tipologia?: string
  n_conduttori?: string
  sezione?: string
  sh?: string
  ubicazione_partenza?: string
  utenza_partenza?: string
  descrizione_utenza_partenza?: string
  ubicazione_arrivo?: string
  utenza_arrivo?: string
  descrizione_utenza_arrivo?: string
  metri_teorici?: number
  metratura_reale?: number
  responsabile_posa?: string
  id_bobina?: string
  stato_installazione?: string
  modificato_manualmente?: number
  timestamp?: string
  collegamenti?: number
  responsabile_partenza?: string
  responsabile_arrivo?: string
  comanda_posa?: string
  comanda_partenza?: string
  comanda_arrivo?: string
  comanda_certificazione?: string
  data_posa?: string
  data_collegamento_partenza?: string
  data_collegamento_arrivo?: string
  data_certificazione?: string
  note_posa?: string
  note_collegamento?: string
  note_certificazione?: string
  spare?: number
}

// Tipi per il parco cavi (bobine)
export interface ParcoCavo {
  id_bobina: string
  id_cantiere: number
  numero_bobina: string
  utility: string
  tipologia: string
  n_conduttori: string
  sezione: string
  metri_totali: number
  metri_residui: number
  stato_bobina: string
  ubicazione_bobina?: string
  fornitore?: string
  n_DDT?: string
  data_DDT?: string
  configurazione?: string
}

// Tipi per le comande
export interface Comanda {
  codice_comanda: string
  tipo_comanda: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'
  descrizione?: string
  data_creazione: string
  data_scadenza?: string
  data_completamento?: string
  responsabile?: string
  stato: 'CREATA' | 'ASSEGNATA' | 'IN_CORSO' | 'COMPLETATA' | 'ANNULLATA'
  id_cantiere: number
  dettagli_certificazione?: any
  numero_componenti_squadra?: number
}

export interface ComandaDettaglio {
  id_dettaglio: number
  codice_comanda: string
  id_cavo: string
  id_cantiere: number
}

// Tipi per i responsabili
export interface Responsabile {
  id_responsabile: number
  nome_responsabile: string
  numero_telefono?: string
  mail?: string
  id_cantiere: number
}

// Tipi per le certificazioni
export interface CertificazioneCavo {
  id_certificazione: number
  id_cavo: string
  id_cantiere: number
  data_certificazione: string
  risultato: string
  note?: string
  strumento_utilizzato?: string
  operatore?: string
}

export interface StrumentoCertificato {
  id_strumento: number
  nome_strumento: string
  modello?: string
  numero_serie?: string
  data_calibrazione?: string
  data_scadenza_calibrazione?: string
  certificato_calibrazione?: string
  id_cantiere: number
}

// Tipi per i report
export interface ReportAvanzamento {
  totale_cavi: number
  cavi_posati: number
  cavi_collegati: number
  cavi_certificati: number
  percentuale_completamento: number
  metri_totali: number
  metri_posati: number
  percentuale_metri: number
  data_inizio_lavori?: string
  data_fine_prevista?: string
  giorni_lavorativi: number
  media_giornaliera: number
  stima_completamento: string
}

export interface ReportBOQ {
  tipologie: Array<{
    tipologia: string
    n_conduttori: string
    sezione: string
    metri_richiesti: number
    metri_disponibili: number
    metri_mancanti: number
    bobine_necessarie: number
  }>
}

// Tipi per le tipologie cavi
export interface CategoriaCavo {
  id_categoria: number
  nome_categoria: string
  descrizione?: string
  colore_identificativo?: string
}

export interface ProduttoreCavo {
  id_produttore: number
  nome_produttore: string
  paese_origine?: string
  sito_web?: string
  contatto_tecnico?: string
}

export interface StandardCavo {
  id_standard: number
  nome_standard: string
  descrizione?: string
  organismo_normativo?: string
  anno_pubblicazione?: number
}

export interface TipologiaCavo {
  id_tipologia: number
  nome_tipologia: string
  id_categoria: number
  id_produttore?: number
  descrizione?: string
  applicazioni?: string
  temperatura_esercizio_min?: number
  temperatura_esercizio_max?: number
  tensione_nominale?: number
  corrente_nominale?: number
  raggio_curvatura_min?: number
  peso_specifico?: number
  diametro_esterno?: number
  colore_guaina?: string
  materiale_conduttore?: string
  materiale_isolante?: string
  materiale_guaina?: string
  resistenza_acqua?: boolean
  resistenza_uv?: boolean
  resistenza_oli?: boolean
  resistenza_acidi?: boolean
  certificazioni_disponibili?: string
  note_tecniche?: string
  scheda_tecnica_url?: string
  immagine_url?: string
  attivo: boolean
}

// Tipi per i work logs (produttività)
export interface WorkLog {
  id_log: number
  id_cantiere: number
  data_lavoro: string
  tipo_attivita: string
  numero_persone: number
  ore_lavorate: number
  cavi_installati?: number
  metri_posati?: number
  collegamenti_effettuati?: number
  certificazioni_completate?: number
  note?: string
  responsabile?: string
  condizioni_meteo?: string
  temperatura?: number
  umidita?: number
}

// Tipi per le API responses
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// Tipi per i filtri e ricerche
export interface FiltriCavi {
  tipologia?: string
  stato_installazione?: string
  responsabile_posa?: string
  id_bobina?: string
  search?: string
  page?: number
  size?: number
}

export interface FiltriComande {
  tipo_comanda?: string
  stato?: string
  responsabile?: string
  data_da?: string
  data_a?: string
}

// Tipi per le statistiche
export interface StatisticheCantiere {
  totale_cavi: number
  cavi_installati: number
  cavi_in_corso: number
  cavi_da_installare: number
  percentuale_completamento: number
  comande_attive: number
  comande_completate: number
  team_attivi: number
  produttivita_media: number
}

// Tipi per i form
export interface CavoFormData {
  id_cavo: string
  revisione_ufficiale: string
  sistema?: string
  utility?: string
  tipologia?: string
  n_conduttori?: string
  sezione?: string
  ubicazione_partenza?: string
  ubicazione_arrivo?: string
  metri_teorici?: number
  responsabile_posa?: string
}

export interface ComandaFormData {
  tipo_comanda: string
  descrizione?: string
  data_scadenza?: string
  responsabile: string
  numero_componenti_squadra?: number
  cavi_selezionati: string[]
}

// Enum per i valori fissi
export enum StatoInstallazione {
  NON_INSTALLATO = 'non_installato',
  IN_CORSO = 'in_corso',
  INSTALLATO = 'installato'
}

export enum TipoComanda {
  POSA = 'POSA',
  COLLEGAMENTO_PARTENZA = 'COLLEGAMENTO_PARTENZA',
  COLLEGAMENTO_ARRIVO = 'COLLEGAMENTO_ARRIVO',
  CERTIFICAZIONE = 'CERTIFICAZIONE'
}

export enum StatoComanda {
  CREATA = 'CREATA',
  ASSEGNATA = 'ASSEGNATA',
  IN_CORSO = 'IN_CORSO',
  COMPLETATA = 'COMPLETATA',
  ANNULLATA = 'ANNULLATA'
}

export enum RuoloUtente {
  OWNER = 'owner',
  USER = 'user',
  CANTIERI_USER = 'cantieri_user'
}
