{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\WorkLogForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, TextField, Select, MenuItem, FormControl, InputLabel, Button, Paper, Chip, Avatar, Divider, Alert, CircularProgress, Stepper, Step, StepLabel, StepContent } from '@mui/material';\nimport { Assignment, Person, Schedule, Engineering, Save, Cancel, CheckCircle, Construction } from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkLogForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null\n}) => {\n  _s();\n  const {\n    selectedCantiere: authSelectedCantiere\n  } = useAuth();\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.id_cantiere) || parseInt(localStorage.getItem('selectedCantiereId'), 10) || null;\n  const currentCantiereName = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.commessa) || localStorage.getItem('selectedCantiereName') || 'Cantiere non selezionato';\n  const [formData, setFormData] = useState({\n    operator_id: '',\n    cable_type_id: '',\n    activity_type: 'Posa',\n    sub_activity_detail: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    quantity: '',\n    start_timestamp: '',\n    end_timestamp: '',\n    number_of_operators_on_task: 1,\n    notes: '',\n    id_cantiere: currentCantiereId || '' // Usa il cantiere attivo\n  });\n  const [operators, setOperators] = useState([]);\n  const [cableTypes, setCableTypes] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [{\n    value: 'Posa',\n    label: 'Posa'\n  }, {\n    value: 'Collegamento',\n    label: 'Collegamento'\n  }, {\n    value: 'Certificazione',\n    label: 'Certificazione'\n  }];\n  const environmentalConditions = [{\n    value: 'Normale',\n    label: 'Normale'\n  }, {\n    value: 'Spazi Ristretti',\n    label: 'Spazi Ristretti'\n  }, {\n    value: 'In Altezza',\n    label: 'In Altezza'\n  }, {\n    value: 'Esterno',\n    label: 'Esterno'\n  }];\n  const toolsUsed = [{\n    value: 'Manuale',\n    label: 'Manuale'\n  }, {\n    value: 'Automatico',\n    label: 'Automatico'\n  }];\n  useEffect(() => {\n    loadInitialData();\n    if (initialData) {\n      setFormData({\n        ...initialData,\n        start_timestamp: formatDateTimeLocal(initialData.start_timestamp),\n        end_timestamp: formatDateTimeLocal(initialData.end_timestamp)\n      });\n    }\n  }, [initialData]);\n  const formatDateTimeLocal = dateString => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toISOString().slice(0, 16);\n  };\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n\n      // Carica operatori e tipi di cavo (non i cantieri perché usiamo quello attivo)\n      const [operatorsRes, cableTypesRes] = await Promise.all([axiosInstance.get('/responsabili'), axiosInstance.get('/admin/tipologie-cavi')]);\n      setOperators(operatorsRes.data || []);\n      setCableTypes(cableTypesRes.data || []);\n\n      // Assicurati che il cantiere attivo sia impostato nel form\n      if (currentCantiereId) {\n        setFormData(prev => ({\n          ...prev,\n          id_cantiere: currentCantiereId\n        }));\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n      setErrors({\n        general: 'Errore nel caricamento dei dati'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n\n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.operator_id) newErrors.operator_id = 'Operatore richiesto';\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Quantità deve essere maggiore di 0';\n    if (!formData.start_timestamp) newErrors.start_timestamp = 'Data/ora inizio richiesta';\n    if (!formData.end_timestamp) newErrors.end_timestamp = 'Data/ora fine richiesta';\n    if (!formData.id_cantiere) newErrors.id_cantiere = 'Cantiere richiesto';\n    if (!formData.number_of_operators_on_task || formData.number_of_operators_on_task < 1) {\n      newErrors.number_of_operators_on_task = 'Numero operatori deve essere almeno 1';\n    }\n\n    // Valida che end_timestamp sia dopo start_timestamp\n    if (formData.start_timestamp && formData.end_timestamp) {\n      const start = new Date(formData.start_timestamp);\n      const end = new Date(formData.end_timestamp);\n      if (end <= start) {\n        newErrors.end_timestamp = 'Data/ora fine deve essere dopo inizio';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Prepara i dati per l'invio\n      const submitData = {\n        ...formData,\n        operator_id: parseInt(formData.operator_id),\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        quantity: parseFloat(formData.quantity),\n        number_of_operators_on_task: parseInt(formData.number_of_operators_on_task),\n        id_cantiere: parseInt(formData.id_cantiere),\n        start_timestamp: new Date(formData.start_timestamp).toISOString(),\n        end_timestamp: new Date(formData.end_timestamp).toISOString()\n      };\n      await onSubmit(submitData);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore nell\\'invio del form:', error);\n      setErrors({\n        general: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore nell\\'invio del work log'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading && !operators.length) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Caricamento dati...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Assignment, {\n          sx: {\n            fontSize: 40,\n            color: 'primary.main',\n            mr: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          sx: {\n            fontWeight: 700\n          },\n          children: initialData ? 'Modifica Work Log' : 'Nuovo Work Log'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        color: \"text.secondary\",\n        children: \"Registra i dettagli del lavoro svolto per il monitoraggio della produttivit\\xE0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 3,\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Person, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), \"Informazioni Base\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.operator_id,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Operatore *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"operator_id\",\n                  value: formData.operator_id,\n                  onChange: handleInputChange,\n                  label: \"Operatore *\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: \"Seleziona operatore\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), operators.map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: op.id_responsabile,\n                    children: [op.nome_responsabile, \" (\", op.experience_level || 'Senior', \")\"]\n                  }, op.id_responsabile, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this), errors.operator_id && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: errors.operator_id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.id_cantiere,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Cantiere *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"id_cantiere\",\n                  value: formData.id_cantiere,\n                  onChange: handleInputChange,\n                  label: \"Cantiere *\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: \"Seleziona cantiere\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this), cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: cantiere.id_cantiere,\n                    children: cantiere.commessa\n                  }, cantiere.id_cantiere, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), errors.id_cantiere && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: errors.id_cantiere\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.activity_type,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Tipo Attivit\\xE0 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"activity_type\",\n                  value: formData.activity_type,\n                  onChange: handleInputChange,\n                  label: \"Tipo Attivit\\xE0 *\",\n                  children: activityTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type.value,\n                    children: type.label\n                  }, type.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), errors.activity_type && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: errors.activity_type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Tipo di Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"cable_type_id\",\n                  value: formData.cable_type_id,\n                  onChange: handleInputChange,\n                  label: \"Tipo di Cavo\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: \"Seleziona tipo di cavo (opzionale)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), cableTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type.id_tipologia,\n                    children: [type.codice_prodotto, \" - \", type.nome_commerciale]\n                  }, type.id_tipologia, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  mt: 2,\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Engineering, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), \"Dettagli del Lavoro\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                name: \"sub_activity_detail\",\n                label: \"Dettaglio Attivit\\xE0\",\n                value: formData.sub_activity_detail,\n                onChange: handleInputChange,\n                placeholder: \"es. Posa in canalina a vista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                name: \"quantity\",\n                label: `Quantità * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`,\n                value: formData.quantity,\n                onChange: handleInputChange,\n                error: !!errors.quantity,\n                helperText: errors.quantity,\n                inputProps: {\n                  step: 0.1,\n                  min: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Condizioni Ambientali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"environmental_conditions\",\n                  value: formData.environmental_conditions,\n                  onChange: handleInputChange,\n                  label: \"Condizioni Ambientali\",\n                  children: environmentalConditions.map(condition => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: condition.value,\n                    children: condition.label\n                  }, condition.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Strumenti Utilizzati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"tools_used\",\n                  value: formData.tools_used,\n                  onChange: handleInputChange,\n                  label: \"Strumenti Utilizzati\",\n                  children: toolsUsed.map(tool => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: tool.value,\n                    children: tool.label\n                  }, tool.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  mt: 2,\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Schedule, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this), \"Tempi di Lavoro\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"datetime-local\",\n                name: \"start_timestamp\",\n                label: \"Data/Ora Inizio *\",\n                value: formData.start_timestamp,\n                onChange: handleInputChange,\n                error: !!errors.start_timestamp,\n                helperText: errors.start_timestamp,\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"datetime-local\",\n                name: \"end_timestamp\",\n                label: \"Data/Ora Fine *\",\n                value: formData.end_timestamp,\n                onChange: handleInputChange,\n                error: !!errors.end_timestamp,\n                helperText: errors.end_timestamp,\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                name: \"number_of_operators_on_task\",\n                label: \"Numero Operatori *\",\n                value: formData.number_of_operators_on_task,\n                onChange: handleInputChange,\n                error: !!errors.number_of_operators_on_task,\n                helperText: errors.number_of_operators_on_task,\n                inputProps: {\n                  min: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                multiline: true,\n                rows: 3,\n                name: \"notes\",\n                label: \"Note\",\n                value: formData.notes,\n                onChange: handleInputChange,\n                placeholder: \"Note aggiuntive sul lavoro svolto...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: 2,\n                  pt: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: onCancel,\n                  startIcon: /*#__PURE__*/_jsxDEV(Cancel, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 32\n                  }, this),\n                  size: \"large\",\n                  children: \"Annulla\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"contained\",\n                  disabled: loading,\n                  startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 42\n                  }, this) : /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 75\n                  }, this),\n                  size: \"large\",\n                  sx: {\n                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n                    '&:hover': {\n                      background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n                    }\n                  },\n                  children: loading ? 'Salvando...' : initialData ? 'Aggiorna Work Log' : 'Crea Work Log'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkLogForm, \"Ap0g1wMuZzTj3LKvRYp4NFc7/Ps=\", false, function () {\n  return [useAuth];\n});\n_c = WorkLogForm;\nexport default WorkLogForm;\nvar _c;\n$RefreshReg$(_c, \"WorkLogForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Paper", "Chip", "Avatar", "Divider", "<PERSON><PERSON>", "CircularProgress", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Assignment", "Person", "Schedule", "Engineering", "Save", "Cancel", "CheckCircle", "Construction", "useAuth", "axiosInstance", "jsxDEV", "_jsxDEV", "WorkLogForm", "onSubmit", "onCancel", "initialData", "_s", "selected<PERSON><PERSON><PERSON>", "authSelectedCantiere", "currentCantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "currentCantiereName", "commessa", "formData", "setFormData", "operator_id", "cable_type_id", "activity_type", "sub_activity_detail", "environmental_conditions", "tools_used", "quantity", "start_timestamp", "end_timestamp", "number_of_operators_on_task", "notes", "operators", "setOperators", "cableTypes", "setCableTypes", "loading", "setLoading", "errors", "setErrors", "activityTypes", "value", "label", "environmentalConditions", "toolsUsed", "loadInitialData", "formatDateTimeLocal", "dateString", "date", "Date", "toISOString", "slice", "operatorsRes", "cableTypesRes", "Promise", "all", "get", "data", "prev", "error", "console", "general", "handleInputChange", "e", "name", "type", "target", "parseFloat", "validateForm", "newErrors", "start", "end", "Object", "keys", "length", "handleSubmit", "preventDefault", "submitData", "_error$response", "_error$response$data", "response", "detail", "sx", "p", "textAlign", "children", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "max<PERSON><PERSON><PERSON>", "py", "display", "justifyContent", "alignItems", "fontSize", "mr", "component", "fontWeight", "severity", "elevation", "container", "spacing", "item", "xs", "md", "fullWidth", "onChange", "map", "op", "id_responsabile", "nome_responsabile", "experience_level", "mt", "cantieri", "cantiere", "id_tipologia", "codice_prodotto", "nome_commerciale", "placeholder", "helperText", "inputProps", "step", "min", "condition", "tool", "InputLabelProps", "shrink", "multiline", "rows", "gap", "pt", "onClick", "startIcon", "size", "disabled", "background", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/WorkLogForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Button,\n  Paper,\n  Chip,\n  Avatar,\n  Divider,\n  Alert,\n  CircularProgress,\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  StepContent\n} from '@mui/material';\nimport {\n  Assignment,\n  Person,\n  Schedule,\n  Engineering,\n  Save,\n  Cancel,\n  CheckCircle,\n  Construction\n} from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst WorkLogForm = ({ onSubmit, onCancel, initialData = null }) => {\n  const { selectedCantiere: authSelectedCantiere } = useAuth();\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = authSelectedCantiere?.id_cantiere ||\n                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||\n                           null;\n  const currentCantiereName = authSelectedCantiere?.commessa ||\n                             localStorage.getItem('selectedCantiereName') ||\n                             'Cantiere non selezionato';\n\n  const [formData, setFormData] = useState({\n    operator_id: '',\n    cable_type_id: '',\n    activity_type: 'Posa',\n    sub_activity_detail: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    quantity: '',\n    start_timestamp: '',\n    end_timestamp: '',\n    number_of_operators_on_task: 1,\n    notes: '',\n    id_cantiere: currentCantiereId || '' // Usa il cantiere attivo\n  });\n\n  const [operators, setOperators] = useState([]);\n  const [cableTypes, setCableTypes] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [\n    { value: 'Posa', label: 'Posa' },\n    { value: 'Collegamento', label: 'Collegamento' },\n    { value: 'Certificazione', label: 'Certificazione' }\n  ];\n\n  const environmentalConditions = [\n    { value: 'Normale', label: 'Normale' },\n    { value: 'Spazi Ristretti', label: 'Spazi Ristretti' },\n    { value: 'In Altezza', label: 'In Altezza' },\n    { value: 'Esterno', label: 'Esterno' }\n  ];\n\n  const toolsUsed = [\n    { value: 'Manuale', label: 'Manuale' },\n    { value: 'Automatico', label: 'Automatico' }\n  ];\n\n  useEffect(() => {\n    loadInitialData();\n    if (initialData) {\n      setFormData({\n        ...initialData,\n        start_timestamp: formatDateTimeLocal(initialData.start_timestamp),\n        end_timestamp: formatDateTimeLocal(initialData.end_timestamp)\n      });\n    }\n  }, [initialData]);\n\n  const formatDateTimeLocal = (dateString) => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toISOString().slice(0, 16);\n  };\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n\n      // Carica operatori e tipi di cavo (non i cantieri perché usiamo quello attivo)\n      const [operatorsRes, cableTypesRes] = await Promise.all([\n        axiosInstance.get('/responsabili'),\n        axiosInstance.get('/admin/tipologie-cavi')\n      ]);\n\n      setOperators(operatorsRes.data || []);\n      setCableTypes(cableTypesRes.data || []);\n\n      // Assicurati che il cantiere attivo sia impostato nel form\n      if (currentCantiereId) {\n        setFormData(prev => ({ ...prev, id_cantiere: currentCantiereId }));\n      }\n\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n      setErrors({ general: 'Errore nel caricamento dei dati' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n    \n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: null }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.operator_id) newErrors.operator_id = 'Operatore richiesto';\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Quantità deve essere maggiore di 0';\n    if (!formData.start_timestamp) newErrors.start_timestamp = 'Data/ora inizio richiesta';\n    if (!formData.end_timestamp) newErrors.end_timestamp = 'Data/ora fine richiesta';\n    if (!formData.id_cantiere) newErrors.id_cantiere = 'Cantiere richiesto';\n    if (!formData.number_of_operators_on_task || formData.number_of_operators_on_task < 1) {\n      newErrors.number_of_operators_on_task = 'Numero operatori deve essere almeno 1';\n    }\n\n    // Valida che end_timestamp sia dopo start_timestamp\n    if (formData.start_timestamp && formData.end_timestamp) {\n      const start = new Date(formData.start_timestamp);\n      const end = new Date(formData.end_timestamp);\n      if (end <= start) {\n        newErrors.end_timestamp = 'Data/ora fine deve essere dopo inizio';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      // Prepara i dati per l'invio\n      const submitData = {\n        ...formData,\n        operator_id: parseInt(formData.operator_id),\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        quantity: parseFloat(formData.quantity),\n        number_of_operators_on_task: parseInt(formData.number_of_operators_on_task),\n        id_cantiere: parseInt(formData.id_cantiere),\n        start_timestamp: new Date(formData.start_timestamp).toISOString(),\n        end_timestamp: new Date(formData.end_timestamp).toISOString()\n      };\n\n      await onSubmit(submitData);\n      \n    } catch (error) {\n      console.error('Errore nell\\'invio del form:', error);\n      setErrors({ \n        general: error.response?.data?.detail || 'Errore nell\\'invio del work log' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading && !operators.length) {\n    return (\n      <Box sx={{ p: 4, textAlign: 'center' }}>\n        <CircularProgress sx={{ mb: 2 }} />\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Caricamento dati...\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 4, textAlign: 'center' }}>\n        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>\n          <Assignment sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />\n          <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700 }}>\n            {initialData ? 'Modifica Work Log' : 'Nuovo Work Log'}\n          </Typography>\n        </Box>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\">\n          Registra i dettagli del lavoro svolto per il monitoraggio della produttività\n        </Typography>\n      </Box>\n\n      {errors.general && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {errors.general}\n        </Alert>\n      )}\n\n      <Card elevation={3}>\n        <CardContent sx={{ p: 4 }}>\n          <Box component=\"form\" onSubmit={handleSubmit}>\n            <Grid container spacing={3}>\n              {/* Sezione Informazioni Base */}\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>\n                  <Person sx={{ mr: 1, color: 'primary.main' }} />\n                  Informazioni Base\n                </Typography>\n                <Divider sx={{ mb: 3 }} />\n              </Grid>\n\n              {/* Operatore */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.operator_id}>\n                  <InputLabel>Operatore *</InputLabel>\n                  <Select\n                    name=\"operator_id\"\n                    value={formData.operator_id}\n                    onChange={handleInputChange}\n                    label=\"Operatore *\"\n                  >\n                    <MenuItem value=\"\">Seleziona operatore</MenuItem>\n                    {operators.map(op => (\n                      <MenuItem key={op.id_responsabile} value={op.id_responsabile}>\n                        {op.nome_responsabile} ({op.experience_level || 'Senior'})\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.operator_id && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1 }}>\n                      {errors.operator_id}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Cantiere */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.id_cantiere}>\n                  <InputLabel>Cantiere *</InputLabel>\n                  <Select\n                    name=\"id_cantiere\"\n                    value={formData.id_cantiere}\n                    onChange={handleInputChange}\n                    label=\"Cantiere *\"\n                  >\n                    <MenuItem value=\"\">Seleziona cantiere</MenuItem>\n                    {cantieri.map(cantiere => (\n                      <MenuItem key={cantiere.id_cantiere} value={cantiere.id_cantiere}>\n                        {cantiere.commessa}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.id_cantiere && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1 }}>\n                      {errors.id_cantiere}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Tipo di Attività */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.activity_type}>\n                  <InputLabel>Tipo Attività *</InputLabel>\n                  <Select\n                    name=\"activity_type\"\n                    value={formData.activity_type}\n                    onChange={handleInputChange}\n                    label=\"Tipo Attività *\"\n                  >\n                    {activityTypes.map(type => (\n                      <MenuItem key={type.value} value={type.value}>\n                        {type.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.activity_type && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1 }}>\n                      {errors.activity_type}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Tipo di Cavo */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Tipo di Cavo</InputLabel>\n                  <Select\n                    name=\"cable_type_id\"\n                    value={formData.cable_type_id}\n                    onChange={handleInputChange}\n                    label=\"Tipo di Cavo\"\n                  >\n                    <MenuItem value=\"\">Seleziona tipo di cavo (opzionale)</MenuItem>\n                    {cableTypes.map(type => (\n                      <MenuItem key={type.id_tipologia} value={type.id_tipologia}>\n                        {type.codice_prodotto} - {type.nome_commerciale}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              {/* Sezione Dettagli Lavoro */}\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, mt: 2, display: 'flex', alignItems: 'center' }}>\n                  <Engineering sx={{ mr: 1, color: 'primary.main' }} />\n                  Dettagli del Lavoro\n                </Typography>\n                <Divider sx={{ mb: 3 }} />\n              </Grid>\n\n              {/* Dettaglio Sub-Attività */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  name=\"sub_activity_detail\"\n                  label=\"Dettaglio Attività\"\n                  value={formData.sub_activity_detail}\n                  onChange={handleInputChange}\n                  placeholder=\"es. Posa in canalina a vista\"\n                />\n              </Grid>\n\n              {/* Quantità */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"number\"\n                  name=\"quantity\"\n                  label={`Quantità * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`}\n                  value={formData.quantity}\n                  onChange={handleInputChange}\n                  error={!!errors.quantity}\n                  helperText={errors.quantity}\n                  inputProps={{ step: 0.1, min: 0 }}\n                />\n              </Grid>\n\n              {/* Condizioni Ambientali */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Condizioni Ambientali</InputLabel>\n                  <Select\n                    name=\"environmental_conditions\"\n                    value={formData.environmental_conditions}\n                    onChange={handleInputChange}\n                    label=\"Condizioni Ambientali\"\n                  >\n                    {environmentalConditions.map(condition => (\n                      <MenuItem key={condition.value} value={condition.value}>\n                        {condition.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              {/* Strumenti Utilizzati */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Strumenti Utilizzati</InputLabel>\n                  <Select\n                    name=\"tools_used\"\n                    value={formData.tools_used}\n                    onChange={handleInputChange}\n                    label=\"Strumenti Utilizzati\"\n                  >\n                    {toolsUsed.map(tool => (\n                      <MenuItem key={tool.value} value={tool.value}>\n                        {tool.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              {/* Sezione Tempi */}\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, mt: 2, display: 'flex', alignItems: 'center' }}>\n                  <Schedule sx={{ mr: 1, color: 'primary.main' }} />\n                  Tempi di Lavoro\n                </Typography>\n                <Divider sx={{ mb: 3 }} />\n              </Grid>\n\n              {/* Data/Ora Inizio */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"datetime-local\"\n                  name=\"start_timestamp\"\n                  label=\"Data/Ora Inizio *\"\n                  value={formData.start_timestamp}\n                  onChange={handleInputChange}\n                  error={!!errors.start_timestamp}\n                  helperText={errors.start_timestamp}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              {/* Data/Ora Fine */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"datetime-local\"\n                  name=\"end_timestamp\"\n                  label=\"Data/Ora Fine *\"\n                  value={formData.end_timestamp}\n                  onChange={handleInputChange}\n                  error={!!errors.end_timestamp}\n                  helperText={errors.end_timestamp}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              {/* Numero Operatori */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"number\"\n                  name=\"number_of_operators_on_task\"\n                  label=\"Numero Operatori *\"\n                  value={formData.number_of_operators_on_task}\n                  onChange={handleInputChange}\n                  error={!!errors.number_of_operators_on_task}\n                  helperText={errors.number_of_operators_on_task}\n                  inputProps={{ min: 1 }}\n                />\n              </Grid>\n\n              {/* Note */}\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  multiline\n                  rows={3}\n                  name=\"notes\"\n                  label=\"Note\"\n                  value={formData.notes}\n                  onChange={handleInputChange}\n                  placeholder=\"Note aggiuntive sul lavoro svolto...\"\n                />\n              </Grid>\n\n              {/* Pulsanti */}\n              <Grid item xs={12}>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, pt: 3 }}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={onCancel}\n                    startIcon={<Cancel />}\n                    size=\"large\"\n                  >\n                    Annulla\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"contained\"\n                    disabled={loading}\n                    startIcon={loading ? <CircularProgress size={20} /> : <Save />}\n                    size=\"large\"\n                    sx={{\n                      background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n                      '&:hover': {\n                        background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n                      }\n                    }}\n                  >\n                    {loading ? 'Salvando...' : (initialData ? 'Aggiorna Work Log' : 'Crea Work Log')}\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </Box>\n        </CardContent>\n      </Card>\n    </Container>\n  );\n};\n\nexport default WorkLogForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,WAAW,QACN,eAAe;AACtB,SACEC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,YAAY,QACP,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM;IAAEC,gBAAgB,EAAEC;EAAqB,CAAC,GAAGV,OAAO,CAAC,CAAC;;EAE5D;EACA,MAAMW,iBAAiB,GAAG,CAAAD,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEE,WAAW,KAClCC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC,IACxD,IAAI;EAC7B,MAAMC,mBAAmB,GAAG,CAAAN,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEO,QAAQ,KAC/BH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAC5C,0BAA0B;EAErD,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC;IACvCqD,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,MAAM;IACrBC,mBAAmB,EAAE,EAAE;IACvBC,wBAAwB,EAAE,SAAS;IACnCC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE,EAAE;IACjBC,2BAA2B,EAAE,CAAC;IAC9BC,KAAK,EAAE,EAAE;IACTlB,WAAW,EAAED,iBAAiB,IAAI,EAAE,CAAC;EACvC,CAAC,CAAC;EAEF,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,MAAM,EAAEC,SAAS,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMwE,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,EAChD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACrD;EAED,MAAMC,uBAAuB,GAAG,CAC9B;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACvC;EAED,MAAME,SAAS,GAAG,CAChB;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,CAC7C;EAEDzE,SAAS,CAAC,MAAM;IACd4E,eAAe,CAAC,CAAC;IACjB,IAAIrC,WAAW,EAAE;MACfY,WAAW,CAAC;QACV,GAAGZ,WAAW;QACdoB,eAAe,EAAEkB,mBAAmB,CAACtC,WAAW,CAACoB,eAAe,CAAC;QACjEC,aAAa,EAAEiB,mBAAmB,CAACtC,WAAW,CAACqB,aAAa;MAC9D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,WAAW,CAAC,CAAC;EAEjB,MAAMsC,mBAAmB,GAAIC,UAAU,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;EAED,MAAMN,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACe,YAAY,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtDrD,aAAa,CAACsD,GAAG,CAAC,eAAe,CAAC,EAClCtD,aAAa,CAACsD,GAAG,CAAC,uBAAuB,CAAC,CAC3C,CAAC;MAEFvB,YAAY,CAACmB,YAAY,CAACK,IAAI,IAAI,EAAE,CAAC;MACrCtB,aAAa,CAACkB,aAAa,CAACI,IAAI,IAAI,EAAE,CAAC;;MAEvC;MACA,IAAI7C,iBAAiB,EAAE;QACrBQ,WAAW,CAACsC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE7C,WAAW,EAAED;QAAkB,CAAC,CAAC,CAAC;MACpE;IAEF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DpB,SAAS,CAAC;QAAEsB,OAAO,EAAE;MAAkC,CAAC,CAAC;IAC3D,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEvB,KAAK;MAAEwB;IAAK,CAAC,GAAGF,CAAC,CAACG,MAAM;IACtC9C,WAAW,CAACsC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACM,IAAI,GAAGC,IAAI,KAAK,QAAQ,GAAGE,UAAU,CAAC1B,KAAK,CAAC,IAAI,EAAE,GAAGA;IACxD,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIH,MAAM,CAAC0B,IAAI,CAAC,EAAE;MAChBzB,SAAS,CAACmB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACM,IAAI,GAAG;MAAK,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAClD,QAAQ,CAACE,WAAW,EAAEgD,SAAS,CAAChD,WAAW,GAAG,qBAAqB;IACxE,IAAI,CAACF,QAAQ,CAACI,aAAa,EAAE8C,SAAS,CAAC9C,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACJ,QAAQ,CAACQ,QAAQ,IAAIR,QAAQ,CAACQ,QAAQ,IAAI,CAAC,EAAE0C,SAAS,CAAC1C,QAAQ,GAAG,oCAAoC;IAC3G,IAAI,CAACR,QAAQ,CAACS,eAAe,EAAEyC,SAAS,CAACzC,eAAe,GAAG,2BAA2B;IACtF,IAAI,CAACT,QAAQ,CAACU,aAAa,EAAEwC,SAAS,CAACxC,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACV,QAAQ,CAACN,WAAW,EAAEwD,SAAS,CAACxD,WAAW,GAAG,oBAAoB;IACvE,IAAI,CAACM,QAAQ,CAACW,2BAA2B,IAAIX,QAAQ,CAACW,2BAA2B,GAAG,CAAC,EAAE;MACrFuC,SAAS,CAACvC,2BAA2B,GAAG,uCAAuC;IACjF;;IAEA;IACA,IAAIX,QAAQ,CAACS,eAAe,IAAIT,QAAQ,CAACU,aAAa,EAAE;MACtD,MAAMyC,KAAK,GAAG,IAAIrB,IAAI,CAAC9B,QAAQ,CAACS,eAAe,CAAC;MAChD,MAAM2C,GAAG,GAAG,IAAItB,IAAI,CAAC9B,QAAQ,CAACU,aAAa,CAAC;MAC5C,IAAI0C,GAAG,IAAID,KAAK,EAAE;QAChBD,SAAS,CAACxC,aAAa,GAAG,uCAAuC;MACnE;IACF;IAEAU,SAAS,CAAC8B,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMwC,UAAU,GAAG;QACjB,GAAG1D,QAAQ;QACXE,WAAW,EAAEP,QAAQ,CAACK,QAAQ,CAACE,WAAW,CAAC;QAC3CC,aAAa,EAAEH,QAAQ,CAACG,aAAa,GAAGR,QAAQ,CAACK,QAAQ,CAACG,aAAa,CAAC,GAAG,IAAI;QAC/EK,QAAQ,EAAEwC,UAAU,CAAChD,QAAQ,CAACQ,QAAQ,CAAC;QACvCG,2BAA2B,EAAEhB,QAAQ,CAACK,QAAQ,CAACW,2BAA2B,CAAC;QAC3EjB,WAAW,EAAEC,QAAQ,CAACK,QAAQ,CAACN,WAAW,CAAC;QAC3Ce,eAAe,EAAE,IAAIqB,IAAI,CAAC9B,QAAQ,CAACS,eAAe,CAAC,CAACsB,WAAW,CAAC,CAAC;QACjErB,aAAa,EAAE,IAAIoB,IAAI,CAAC9B,QAAQ,CAACU,aAAa,CAAC,CAACqB,WAAW,CAAC;MAC9D,CAAC;MAED,MAAM5C,QAAQ,CAACuE,UAAU,CAAC;IAE5B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdnB,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDpB,SAAS,CAAC;QACRsB,OAAO,EAAE,EAAAiB,eAAA,GAAAnB,KAAK,CAACqB,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBrB,IAAI,cAAAsB,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI;MAC3C,CAAC,CAAC;IACJ,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,IAAI,CAACJ,SAAS,CAAC0C,MAAM,EAAE;IAChC,oBACEtE,OAAA,CAAClC,GAAG;MAACgH,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrCjF,OAAA,CAAChB,gBAAgB;QAAC8F,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCtF,OAAA,CAAChC,UAAU;QAACuH,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEhD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEtF,OAAA,CAACjC,SAAS;IAAC0H,QAAQ,EAAC,IAAI;IAACX,EAAE,EAAE;MAAEY,EAAE,EAAE;IAAE,CAAE;IAAAT,QAAA,gBAErCjF,OAAA,CAAClC,GAAG;MAACgH,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAEF,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtCjF,OAAA,CAAClC,GAAG;QAACgH,EAAE,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEX,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBAClFjF,OAAA,CAACX,UAAU;UAACyF,EAAE,EAAE;YAAEgB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,cAAc;YAAEO,EAAE,EAAE;UAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClEtF,OAAA,CAAChC,UAAU;UAACuH,OAAO,EAAC,IAAI;UAACS,SAAS,EAAC,IAAI;UAAClB,EAAE,EAAE;YAAEmB,UAAU,EAAE;UAAI,CAAE;UAAAhB,QAAA,EAC7D7E,WAAW,GAAG,mBAAmB,GAAG;QAAgB;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNtF,OAAA,CAAChC,UAAU;QAACuH,OAAO,EAAC,WAAW;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEvD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELpD,MAAM,CAACuB,OAAO,iBACbzD,OAAA,CAACjB,KAAK;MAACmH,QAAQ,EAAC,OAAO;MAACpB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,EACnC/C,MAAM,CAACuB;IAAO;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACR,eAEDtF,OAAA,CAAC9B,IAAI;MAACiI,SAAS,EAAE,CAAE;MAAAlB,QAAA,eACjBjF,OAAA,CAAC7B,WAAW;QAAC2G,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,eACxBjF,OAAA,CAAClC,GAAG;UAACkI,SAAS,EAAC,MAAM;UAAC9F,QAAQ,EAAEqE,YAAa;UAAAU,QAAA,eAC3CjF,OAAA,CAAC/B,IAAI;YAACmI,SAAS;YAACC,OAAO,EAAE,CAAE;YAAApB,QAAA,gBAEzBjF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,gBAChBjF,OAAA,CAAChC,UAAU;gBAACuH,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEI,EAAE,EAAE,CAAC;kBAAES,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBAC5EjF,OAAA,CAACV,MAAM;kBAACwF,EAAE,EAAE;oBAAEiB,EAAE,EAAE,CAAC;oBAAEP,KAAK,EAAE;kBAAe;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAElD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbtF,OAAA,CAAClB,OAAO;gBAACgG,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAACxB,WAAW;gBAACiI,SAAS;gBAAClD,KAAK,EAAE,CAAC,CAACrB,MAAM,CAACjB,WAAY;gBAAAgE,QAAA,gBACjDjF,OAAA,CAACvB,UAAU;kBAAAwG,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCtF,OAAA,CAAC1B,MAAM;kBACLsF,IAAI,EAAC,aAAa;kBAClBvB,KAAK,EAAEtB,QAAQ,CAACE,WAAY;kBAC5ByF,QAAQ,EAAEhD,iBAAkB;kBAC5BpB,KAAK,EAAC,aAAa;kBAAA2C,QAAA,gBAEnBjF,OAAA,CAACzB,QAAQ;oBAAC8D,KAAK,EAAC,EAAE;oBAAA4C,QAAA,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAChD1D,SAAS,CAAC+E,GAAG,CAACC,EAAE,iBACf5G,OAAA,CAACzB,QAAQ;oBAA0B8D,KAAK,EAAEuE,EAAE,CAACC,eAAgB;oBAAA5B,QAAA,GAC1D2B,EAAE,CAACE,iBAAiB,EAAC,IAAE,EAACF,EAAE,CAACG,gBAAgB,IAAI,QAAQ,EAAC,GAC3D;kBAAA,GAFeH,EAAE,CAACC,eAAe;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEvB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRpD,MAAM,CAACjB,WAAW,iBACjBjB,OAAA,CAAChC,UAAU;kBAACuH,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,OAAO;kBAACV,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAA/B,QAAA,EACvD/C,MAAM,CAACjB;gBAAW;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAACxB,WAAW;gBAACiI,SAAS;gBAAClD,KAAK,EAAE,CAAC,CAACrB,MAAM,CAACzB,WAAY;gBAAAwE,QAAA,gBACjDjF,OAAA,CAACvB,UAAU;kBAAAwG,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCtF,OAAA,CAAC1B,MAAM;kBACLsF,IAAI,EAAC,aAAa;kBAClBvB,KAAK,EAAEtB,QAAQ,CAACN,WAAY;kBAC5BiG,QAAQ,EAAEhD,iBAAkB;kBAC5BpB,KAAK,EAAC,YAAY;kBAAA2C,QAAA,gBAElBjF,OAAA,CAACzB,QAAQ;oBAAC8D,KAAK,EAAC,EAAE;oBAAA4C,QAAA,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAC/C2B,QAAQ,CAACN,GAAG,CAACO,QAAQ,iBACpBlH,OAAA,CAACzB,QAAQ;oBAA4B8D,KAAK,EAAE6E,QAAQ,CAACzG,WAAY;oBAAAwE,QAAA,EAC9DiC,QAAQ,CAACpG;kBAAQ,GADLoG,QAAQ,CAACzG,WAAW;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRpD,MAAM,CAACzB,WAAW,iBACjBT,OAAA,CAAChC,UAAU;kBAACuH,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,OAAO;kBAACV,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAA/B,QAAA,EACvD/C,MAAM,CAACzB;gBAAW;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAACxB,WAAW;gBAACiI,SAAS;gBAAClD,KAAK,EAAE,CAAC,CAACrB,MAAM,CAACf,aAAc;gBAAA8D,QAAA,gBACnDjF,OAAA,CAACvB,UAAU;kBAAAwG,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCtF,OAAA,CAAC1B,MAAM;kBACLsF,IAAI,EAAC,eAAe;kBACpBvB,KAAK,EAAEtB,QAAQ,CAACI,aAAc;kBAC9BuF,QAAQ,EAAEhD,iBAAkB;kBAC5BpB,KAAK,EAAC,oBAAiB;kBAAA2C,QAAA,EAEtB7C,aAAa,CAACuE,GAAG,CAAC9C,IAAI,iBACrB7D,OAAA,CAACzB,QAAQ;oBAAkB8D,KAAK,EAAEwB,IAAI,CAACxB,KAAM;oBAAA4C,QAAA,EAC1CpB,IAAI,CAACvB;kBAAK,GADEuB,IAAI,CAACxB,KAAK;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRpD,MAAM,CAACf,aAAa,iBACnBnB,OAAA,CAAChC,UAAU;kBAACuH,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,OAAO;kBAACV,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAA/B,QAAA,EACvD/C,MAAM,CAACf;gBAAa;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAACxB,WAAW;gBAACiI,SAAS;gBAAAxB,QAAA,gBACpBjF,OAAA,CAACvB,UAAU;kBAAAwG,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCtF,OAAA,CAAC1B,MAAM;kBACLsF,IAAI,EAAC,eAAe;kBACpBvB,KAAK,EAAEtB,QAAQ,CAACG,aAAc;kBAC9BwF,QAAQ,EAAEhD,iBAAkB;kBAC5BpB,KAAK,EAAC,cAAc;kBAAA2C,QAAA,gBAEpBjF,OAAA,CAACzB,QAAQ;oBAAC8D,KAAK,EAAC,EAAE;oBAAA4C,QAAA,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAC/DxD,UAAU,CAAC6E,GAAG,CAAC9C,IAAI,iBAClB7D,OAAA,CAACzB,QAAQ;oBAAyB8D,KAAK,EAAEwB,IAAI,CAACsD,YAAa;oBAAAlC,QAAA,GACxDpB,IAAI,CAACuD,eAAe,EAAC,KAAG,EAACvD,IAAI,CAACwD,gBAAgB;kBAAA,GADlCxD,IAAI,CAACsD,YAAY;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEtB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,gBAChBjF,OAAA,CAAChC,UAAU;gBAACuH,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEI,EAAE,EAAE,CAAC;kBAAE8B,EAAE,EAAE,CAAC;kBAAErB,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBACnFjF,OAAA,CAACR,WAAW;kBAACsF,EAAE,EAAE;oBAAEiB,EAAE,EAAE,CAAC;oBAAEP,KAAK,EAAE;kBAAe;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAEvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbtF,OAAA,CAAClB,OAAO;gBAACgG,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAAC3B,SAAS;gBACRoI,SAAS;gBACT7C,IAAI,EAAC,qBAAqB;gBAC1BtB,KAAK,EAAC,uBAAoB;gBAC1BD,KAAK,EAAEtB,QAAQ,CAACK,mBAAoB;gBACpCsF,QAAQ,EAAEhD,iBAAkB;gBAC5B4D,WAAW,EAAC;cAA8B;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAAC3B,SAAS;gBACRoI,SAAS;gBACT5C,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,UAAU;gBACftB,KAAK,EAAE,cAAcvB,QAAQ,CAACI,aAAa,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,EAAG;gBACjFkB,KAAK,EAAEtB,QAAQ,CAACQ,QAAS;gBACzBmF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACrB,MAAM,CAACX,QAAS;gBACzBgG,UAAU,EAAErF,MAAM,CAACX,QAAS;gBAC5BiG,UAAU,EAAE;kBAAEC,IAAI,EAAE,GAAG;kBAAEC,GAAG,EAAE;gBAAE;cAAE;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAACxB,WAAW;gBAACiI,SAAS;gBAAAxB,QAAA,gBACpBjF,OAAA,CAACvB,UAAU;kBAAAwG,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9CtF,OAAA,CAAC1B,MAAM;kBACLsF,IAAI,EAAC,0BAA0B;kBAC/BvB,KAAK,EAAEtB,QAAQ,CAACM,wBAAyB;kBACzCqF,QAAQ,EAAEhD,iBAAkB;kBAC5BpB,KAAK,EAAC,uBAAuB;kBAAA2C,QAAA,EAE5B1C,uBAAuB,CAACoE,GAAG,CAACgB,SAAS,iBACpC3H,OAAA,CAACzB,QAAQ;oBAAuB8D,KAAK,EAAEsF,SAAS,CAACtF,KAAM;oBAAA4C,QAAA,EACpD0C,SAAS,CAACrF;kBAAK,GADHqF,SAAS,CAACtF,KAAK;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAACxB,WAAW;gBAACiI,SAAS;gBAAAxB,QAAA,gBACpBjF,OAAA,CAACvB,UAAU;kBAAAwG,QAAA,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7CtF,OAAA,CAAC1B,MAAM;kBACLsF,IAAI,EAAC,YAAY;kBACjBvB,KAAK,EAAEtB,QAAQ,CAACO,UAAW;kBAC3BoF,QAAQ,EAAEhD,iBAAkB;kBAC5BpB,KAAK,EAAC,sBAAsB;kBAAA2C,QAAA,EAE3BzC,SAAS,CAACmE,GAAG,CAACiB,IAAI,iBACjB5H,OAAA,CAACzB,QAAQ;oBAAkB8D,KAAK,EAAEuF,IAAI,CAACvF,KAAM;oBAAA4C,QAAA,EAC1C2C,IAAI,CAACtF;kBAAK,GADEsF,IAAI,CAACvF,KAAK;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,gBAChBjF,OAAA,CAAChC,UAAU;gBAACuH,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEI,EAAE,EAAE,CAAC;kBAAE8B,EAAE,EAAE,CAAC;kBAAErB,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBACnFjF,OAAA,CAACT,QAAQ;kBAACuF,EAAE,EAAE;oBAAEiB,EAAE,EAAE,CAAC;oBAAEP,KAAK,EAAE;kBAAe;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbtF,OAAA,CAAClB,OAAO;gBAACgG,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAAC3B,SAAS;gBACRoI,SAAS;gBACT5C,IAAI,EAAC,gBAAgB;gBACrBD,IAAI,EAAC,iBAAiB;gBACtBtB,KAAK,EAAC,mBAAmB;gBACzBD,KAAK,EAAEtB,QAAQ,CAACS,eAAgB;gBAChCkF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACrB,MAAM,CAACV,eAAgB;gBAChC+F,UAAU,EAAErF,MAAM,CAACV,eAAgB;gBACnCqG,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAAC3B,SAAS;gBACRoI,SAAS;gBACT5C,IAAI,EAAC,gBAAgB;gBACrBD,IAAI,EAAC,eAAe;gBACpBtB,KAAK,EAAC,iBAAiB;gBACvBD,KAAK,EAAEtB,QAAQ,CAACU,aAAc;gBAC9BiF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACrB,MAAM,CAACT,aAAc;gBAC9B8F,UAAU,EAAErF,MAAM,CAACT,aAAc;gBACjCoG,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBjF,OAAA,CAAC3B,SAAS;gBACRoI,SAAS;gBACT5C,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,6BAA6B;gBAClCtB,KAAK,EAAC,oBAAoB;gBAC1BD,KAAK,EAAEtB,QAAQ,CAACW,2BAA4B;gBAC5CgF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACrB,MAAM,CAACR,2BAA4B;gBAC5C6F,UAAU,EAAErF,MAAM,CAACR,2BAA4B;gBAC/C8F,UAAU,EAAE;kBAAEE,GAAG,EAAE;gBAAE;cAAE;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,eAChBjF,OAAA,CAAC3B,SAAS;gBACRoI,SAAS;gBACTsB,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRpE,IAAI,EAAC,OAAO;gBACZtB,KAAK,EAAC,MAAM;gBACZD,KAAK,EAAEtB,QAAQ,CAACY,KAAM;gBACtB+E,QAAQ,EAAEhD,iBAAkB;gBAC5B4D,WAAW,EAAC;cAAsC;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPtF,OAAA,CAAC/B,IAAI;cAACqI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,eAChBjF,OAAA,CAAClC,GAAG;gBAACgH,EAAE,EAAE;kBAAEa,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,UAAU;kBAAEqC,GAAG,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,gBACtEjF,OAAA,CAACtB,MAAM;kBACL6G,OAAO,EAAC,UAAU;kBAClB4C,OAAO,EAAEhI,QAAS;kBAClBiI,SAAS,eAAEpI,OAAA,CAACN,MAAM;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtB+C,IAAI,EAAC,OAAO;kBAAApD,QAAA,EACb;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtF,OAAA,CAACtB,MAAM;kBACLmF,IAAI,EAAC,QAAQ;kBACb0B,OAAO,EAAC,WAAW;kBACnB+C,QAAQ,EAAEtG,OAAQ;kBAClBoG,SAAS,EAAEpG,OAAO,gBAAGhC,OAAA,CAAChB,gBAAgB;oBAACqJ,IAAI,EAAE;kBAAG;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGtF,OAAA,CAACP,IAAI;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC/D+C,IAAI,EAAC,OAAO;kBACZvD,EAAE,EAAE;oBACFyD,UAAU,EAAE,kDAAkD;oBAC9D,SAAS,EAAE;sBACTA,UAAU,EAAE;oBACd;kBACF,CAAE;kBAAAtD,QAAA,EAEDjD,OAAO,GAAG,aAAa,GAAI5B,WAAW,GAAG,mBAAmB,GAAG;gBAAgB;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACjF,EAAA,CAjeIJ,WAAW;EAAA,QACoCJ,OAAO;AAAA;AAAA2I,EAAA,GADtDvI,WAAW;AAmejB,eAAeA,WAAW;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}