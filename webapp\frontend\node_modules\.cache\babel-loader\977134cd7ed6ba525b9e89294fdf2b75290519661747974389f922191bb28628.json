{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\WorkLogsList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, TextField, Select, MenuItem, FormControl, InputLabel, Button, Paper, Chip, Avatar, Divider, Alert, CircularProgress, IconButton, Tooltip, Badge, LinearProgress, Pagination, Stack, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Assignment, Person, Schedule, Engineering, TrendingUp, FilterList, Clear, Edit, Delete, Visibility, Add, ExpandMore, Speed, Timer, Group, LocationOn, Build, Assessment, Construction } from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkLogsList = ({\n  onEdit,\n  onDelete,\n  onCreateNew\n}) => {\n  _s();\n  const {\n    selectedCantiere: authSelectedCantiere\n  } = useAuth();\n  const [workLogs, setWorkLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    activity_type: '',\n    operator_id: '',\n    start_date: '',\n    end_date: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total_count: 0\n  });\n  const [operators, setOperators] = useState([]);\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.id_cantiere) || parseInt(localStorage.getItem('selectedCantiereId'), 10) || null;\n  const currentCantiereName = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.commessa) || localStorage.getItem('selectedCantiereName') || 'Cantiere non selezionato';\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n  useEffect(() => {\n    if (currentCantiereId) {\n      loadWorkLogs();\n    }\n  }, [currentCantiereId, filters, pagination.page]);\n\n  // Carica i dati solo se c'è un cantiere attivo\n  useEffect(() => {\n    if (!currentCantiereId) {\n      setLoading(false);\n    }\n  }, [currentCantiereId]);\n  const loadInitialData = async () => {\n    try {\n      const operatorsRes = await axiosInstance.get('/responsabili');\n      setOperators(operatorsRes.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n    }\n  };\n  const loadWorkLogs = async () => {\n    if (!currentCantiereId) {\n      console.warn('Nessun cantiere attivo selezionato per i work logs');\n      setLoading(false);\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Parametri per le chiamate API - usa solo il cantiere attivo\n      const params = {\n        page: pagination.page,\n        per_page: pagination.per_page,\n        id_cantiere: currentCantiereId,\n        // Forza il cantiere attivo\n        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value !== ''))\n      };\n      console.log('Caricamento work logs per cantiere:', currentCantiereId, currentCantiereName);\n      const response = await axiosInstance.get('/v1/work-logs', {\n        params\n      });\n      setWorkLogs(response.data.work_logs || []);\n      setPagination(prev => ({\n        ...prev,\n        total_count: response.data.total_count || 0\n      }));\n    } catch (error) {\n      console.error('Errore nel caricamento work logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFilterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    })); // Reset alla prima pagina\n  };\n  const clearFilters = () => {\n    setFilters({\n      activity_type: '',\n      operator_id: '',\n      start_date: '',\n      end_date: ''\n    });\n  };\n  const formatDateTime = dateString => {\n    return new Date(dateString).toLocaleString('it-IT', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const formatDuration = minutes => {\n    if (!minutes) return '-';\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n  };\n  const getActivityIcon = activity => {\n    switch (activity) {\n      case 'Posa':\n        return '🔧';\n      case 'Collegamento':\n        return '🔌';\n      case 'Certificazione':\n        return '✅';\n      default:\n        return '📝';\n    }\n  };\n  const getConditionColor = condition => {\n    switch (condition) {\n      case 'Normale':\n        return 'bg-green-100 text-green-800';\n      case 'Spazi Ristretti':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'In Altezza':\n        return 'bg-orange-100 text-orange-800';\n      case 'Esterno':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const totalPages = Math.ceil(pagination.total_count / pagination.per_page);\n  if (loading && workLogs.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Caricamento work logs...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Se non c'è un cantiere attivo, mostra un messaggio\n  if (!currentCantiereId) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"Nessun cantiere attivo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Per visualizzare e gestire i work logs, seleziona prima un cantiere dalla dashboard principale.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Assignment, {\n            sx: {\n              fontSize: 32,\n              color: 'primary.main',\n              mr: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 700\n              },\n              children: \"Work Logs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Construction, {\n                sx: {\n                  fontSize: 20,\n                  color: 'text.secondary',\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                color: \"text.secondary\",\n                children: [\"Cantiere: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: currentCantiereName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: `ID: ${currentCantiereId}`,\n                size: \"small\",\n                color: \"primary\",\n                variant: \"outlined\",\n                sx: {\n                  ml: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: onCreateNew,\n          startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 24\n          }, this),\n          size: \"large\",\n          sx: {\n            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n            '&:hover': {\n              background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n            }\n          },\n          children: \"Nuovo Work Log\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              position: 'relative',\n              overflow: 'visible'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Totale Work Logs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: pagination.total_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Assignment, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n              color: 'white'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Quantit\\xE0 Totale\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: workLogs.reduce((sum, log) => sum + (log.quantity || 0), 0).toFixed(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"metri/unit\\xE0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n              color: 'white'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Ore-Uomo Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: workLogs.reduce((sum, log) => sum + (log.total_man_hours || 0), 0).toFixed(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"ore lavorate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Schedule, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n              color: 'white'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Produttivit\\xE0 Media\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: workLogs.length > 0 ? (workLogs.reduce((sum, log) => sum + (log.productivity_per_hour || 0), 0) / workLogs.length).toFixed(2) : '0.00'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"unit\\xE0/ora\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Speed, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n      elevation: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 23\n        }, this),\n        sx: {\n          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n          '&:hover': {\n            background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FilterList, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600\n            },\n            children: \"Filtri Avanzati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              ml: 'auto',\n              mr: 2\n            },\n            children: Object.values(filters).some(v => v !== '') && /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"Filtri Attivi\",\n              color: \"primary\",\n              size: \"small\",\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Attivit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"activity_type\",\n                value: filters.activity_type,\n                onChange: handleFilterChange,\n                label: \"Attivit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Posa\",\n                  children: \"\\uD83D\\uDD27 Posa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Collegamento\",\n                  children: \"\\uD83D\\uDD0C Collegamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Certificazione\",\n                  children: \"\\u2705 Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"operator_id\",\n                value: filters.operator_id,\n                onChange: handleFilterChange,\n                label: \"Operatore\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this), operators.map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op.id_responsabile,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Person, {\n                      sx: {\n                        mr: 1,\n                        fontSize: 16\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 25\n                    }, this), op.nome_responsabile]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 23\n                  }, this)\n                }, op.id_responsabile, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              type: \"date\",\n              name: \"start_date\",\n              label: \"Data Inizio\",\n              value: filters.start_date,\n              onChange: handleFilterChange,\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              type: \"date\",\n              name: \"end_date\",\n              label: \"Data Fine\",\n              value: filters.end_date,\n              onChange: handleFilterChange,\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                gap: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: clearFilters,\n                startIcon: /*#__PURE__*/_jsxDEV(Clear, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 30\n                }, this),\n                size: \"small\",\n                children: \"Pulisci Filtri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), workLogs.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 8,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Assignment, {\n        sx: {\n          fontSize: 80,\n          mb: 2,\n          opacity: 0.3,\n          color: 'text.secondary'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Nessun work log trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: Object.values(filters).some(v => v !== '') ? 'Nessun risultato per i filtri selezionati. Prova a modificare i criteri di ricerca.' : 'Inizia creando il tuo primo work log per monitorare la produttività.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onCreateNew,\n        startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 24\n        }, this),\n        size: \"large\",\n        sx: {\n          background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n          '&:hover': {\n            background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n          }\n        },\n        children: \"Crea il primo Work Log\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: workLogs.map(log => {\n        var _log$quantity, _log$productivity_per, _log$productivity_per2;\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                transform: 'translateY(-4px)',\n                boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n              avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: log.activity_type === 'Posa' ? 'primary.main' : log.activity_type === 'Collegamento' ? 'secondary.main' : 'success.main',\n                  width: 48,\n                  height: 48\n                },\n                children: log.activity_type === 'Posa' ? /*#__PURE__*/_jsxDEV(Engineering, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 55\n                }, this) : log.activity_type === 'Collegamento' ? /*#__PURE__*/_jsxDEV(Build, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 63\n                }, this) : /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 75\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 21\n              }, this),\n              title: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: log.activity_type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 21\n              }, this),\n              subheader: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: log.sub_activity_detail || 'Dettaglio non specificato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this),\n              action: /*#__PURE__*/_jsxDEV(Chip, {\n                label: formatDateTime(log.start_timestamp),\n                size: \"small\",\n                variant: \"outlined\",\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flexGrow: 1,\n                pt: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 1,\n                      bgcolor: 'primary.50',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 700,\n                        color: 'primary.main'\n                      },\n                      children: (_log$quantity = log.quantity) === null || _log$quantity === void 0 ? void 0 : _log$quantity.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: log.activity_type === 'Posa' ? 'metri' : 'unità'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 1,\n                      bgcolor: 'success.50',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 700,\n                        color: 'success.main'\n                      },\n                      children: formatDuration(log.duration_minutes)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"durata\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Person, {\n                    sx: {\n                      mr: 1,\n                      fontSize: 16,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Operatore #\", log.operator_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${log.number_of_operators_on_task} op`,\n                    size: \"small\",\n                    sx: {\n                      ml: 'auto'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Speed, {\n                    sx: {\n                      mr: 1,\n                      fontSize: 16,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [(_log$productivity_per = log.productivity_per_hour) === null || _log$productivity_per === void 0 ? void 0 : _log$productivity_per.toFixed(2), \" /h\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    sx: {\n                      ml: 1\n                    },\n                    children: [\"(\", (_log$productivity_per2 = log.productivity_per_person_per_hour) === null || _log$productivity_per2 === void 0 ? void 0 : _log$productivity_per2.toFixed(2), \" /h/op)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(LocationOn, {\n                    sx: {\n                      mr: 1,\n                      fontSize: 16,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [log.environmental_conditions, \" \\u2022 \", log.tools_used]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Efficienza\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [Math.min(100, log.productivity_per_person_per_hour / 100 * 100).toFixed(0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: Math.min(100, log.productivity_per_person_per_hour / 100 * 100),\n                  sx: {\n                    height: 6,\n                    borderRadius: 3,\n                    bgcolor: 'grey.200',\n                    '& .MuiLinearProgress-bar': {\n                      borderRadius: 3,\n                      background: log.productivity_per_person_per_hour > 80 ? 'linear-gradient(90deg, #4caf50, #8bc34a)' : log.productivity_per_person_per_hour > 50 ? 'linear-gradient(90deg, #ff9800, #ffc107)' : 'linear-gradient(90deg, #f44336, #ff5722)'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 19\n              }, this), log.notes && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 1,\n                  bgcolor: 'grey.50',\n                  borderRadius: 1,\n                  mb: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  sx: {\n                    fontStyle: 'italic'\n                  },\n                  children: [\"\\\"\", log.notes, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Modifica\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => onEdit(log),\n                    color: \"primary\",\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Elimina\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => onDelete(log.id),\n                    color: \"error\",\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this)\n        }, log.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 9\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 4,\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 2,\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Mostrando \", (pagination.page - 1) * pagination.per_page + 1, \"-\", Math.min(pagination.page * pagination.per_page, pagination.total_count), \" di \", pagination.total_count]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n            count: totalPages,\n            page: pagination.page,\n            onChange: (event, value) => setPagination(prev => ({\n              ...prev,\n              page: value\n            })),\n            color: \"primary\",\n            variant: \"outlined\",\n            shape: \"rounded\",\n            showFirstButton: true,\n            showLastButton: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkLogsList, \"YKBo2YJdadOrTYuEWT/5ycpd2og=\", false, function () {\n  return [useAuth];\n});\n_c = WorkLogsList;\nexport default WorkLogsList;\nvar _c;\n$RefreshReg$(_c, \"WorkLogsList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Paper", "Chip", "Avatar", "Divider", "<PERSON><PERSON>", "CircularProgress", "IconButton", "<PERSON><PERSON><PERSON>", "Badge", "LinearProgress", "Pagination", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Assignment", "Person", "Schedule", "Engineering", "TrendingUp", "FilterList", "Clear", "Edit", "Delete", "Visibility", "Add", "ExpandMore", "Speed", "Timer", "Group", "LocationOn", "Build", "Assessment", "Construction", "useAuth", "axiosInstance", "jsxDEV", "_jsxDEV", "WorkLogsList", "onEdit", "onDelete", "onCreateNew", "_s", "selected<PERSON><PERSON><PERSON>", "authSelectedCantiere", "workLogs", "setWorkLogs", "loading", "setLoading", "filters", "setFilters", "activity_type", "operator_id", "start_date", "end_date", "pagination", "setPagination", "page", "per_page", "total_count", "operators", "setOperators", "currentCantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "currentCantiereName", "commessa", "loadInitialData", "loadWorkLogs", "operatorsRes", "get", "data", "error", "console", "warn", "params", "Object", "fromEntries", "entries", "filter", "_", "value", "log", "response", "work_logs", "prev", "handleFilterChange", "e", "name", "target", "clearFilters", "formatDateTime", "dateString", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "getActivityIcon", "activity", "getConditionColor", "condition", "totalPages", "ceil", "length", "sx", "p", "textAlign", "children", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "severity", "display", "justifyContent", "alignItems", "fontSize", "mr", "component", "fontWeight", "mt", "label", "size", "ml", "onClick", "startIcon", "background", "container", "spacing", "item", "xs", "sm", "md", "elevation", "position", "overflow", "pb", "opacity", "bgcolor", "width", "height", "reduce", "sum", "quantity", "toFixed", "total_man_hours", "productivity_per_hour", "expandIcon", "values", "some", "v", "lg", "fullWidth", "onChange", "map", "op", "id_responsabile", "nome_responsabile", "type", "InputLabelProps", "shrink", "gap", "_log$quantity", "_log$productivity_per", "_log$productivity_per2", "flexDirection", "transition", "transform", "boxShadow", "avatar", "title", "subheader", "sub_activity_detail", "action", "start_timestamp", "flexGrow", "pt", "borderRadius", "duration_minutes", "number_of_operators_on_task", "productivity_per_person_per_hour", "environmental_conditions", "tools_used", "min", "notes", "fontStyle", "id", "direction", "count", "event", "shape", "showFirstButton", "showLastButton", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/WorkLogsList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Button,\n  Paper,\n  Chip,\n  Avatar,\n  Divider,\n  Alert,\n  CircularProgress,\n  IconButton,\n  Tooltip,\n  Badge,\n  LinearProgress,\n  Pagination,\n  Stack,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Assignment,\n  Person,\n  Schedule,\n  Engineering,\n  TrendingUp,\n  FilterList,\n  Clear,\n  Edit,\n  Delete,\n  Visibility,\n  Add,\n  ExpandMore,\n  Speed,\n  Timer,\n  Group,\n  LocationOn,\n  Build,\n  Assessment,\n  Construction\n} from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst WorkLogsList = ({ onEdit, onDelete, onCreateNew }) => {\n  const { selectedCantiere: authSelectedCantiere } = useAuth();\n  const [workLogs, setWorkLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    activity_type: '',\n    operator_id: '',\n    start_date: '',\n    end_date: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total_count: 0\n  });\n  const [operators, setOperators] = useState([]);\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = authSelectedCantiere?.id_cantiere ||\n                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||\n                           null;\n  const currentCantiereName = authSelectedCantiere?.commessa ||\n                             localStorage.getItem('selectedCantiereName') ||\n                             'Cantiere non selezionato';\n\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n\n  useEffect(() => {\n    if (currentCantiereId) {\n      loadWorkLogs();\n    }\n  }, [currentCantiereId, filters, pagination.page]);\n\n  // Carica i dati solo se c'è un cantiere attivo\n  useEffect(() => {\n    if (!currentCantiereId) {\n      setLoading(false);\n    }\n  }, [currentCantiereId]);\n\n  const loadInitialData = async () => {\n    try {\n      const operatorsRes = await axiosInstance.get('/responsabili');\n      setOperators(operatorsRes.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n    }\n  };\n\n  const loadWorkLogs = async () => {\n    if (!currentCantiereId) {\n      console.warn('Nessun cantiere attivo selezionato per i work logs');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // Parametri per le chiamate API - usa solo il cantiere attivo\n      const params = {\n        page: pagination.page,\n        per_page: pagination.per_page,\n        id_cantiere: currentCantiereId, // Forza il cantiere attivo\n        ...Object.fromEntries(\n          Object.entries(filters).filter(([_, value]) => value !== '')\n        )\n      };\n\n      console.log('Caricamento work logs per cantiere:', currentCantiereId, currentCantiereName);\n\n      const response = await axiosInstance.get('/v1/work-logs', { params });\n\n      setWorkLogs(response.data.work_logs || []);\n      setPagination(prev => ({\n        ...prev,\n        total_count: response.data.total_count || 0\n      }));\n\n    } catch (error) {\n      console.error('Errore nel caricamento work logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilterChange = (e) => {\n    const { name, value } = e.target;\n    setFilters(prev => ({ ...prev, [name]: value }));\n    setPagination(prev => ({ ...prev, page: 1 })); // Reset alla prima pagina\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      activity_type: '',\n      operator_id: '',\n      start_date: '',\n      end_date: ''\n    });\n  };\n\n  const formatDateTime = (dateString) => {\n    return new Date(dateString).toLocaleString('it-IT', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatDuration = (minutes) => {\n    if (!minutes) return '-';\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n  };\n\n  const getActivityIcon = (activity) => {\n    switch (activity) {\n      case 'Posa': return '🔧';\n      case 'Collegamento': return '🔌';\n      case 'Certificazione': return '✅';\n      default: return '📝';\n    }\n  };\n\n  const getConditionColor = (condition) => {\n    switch (condition) {\n      case 'Normale': return 'bg-green-100 text-green-800';\n      case 'Spazi Ristretti': return 'bg-yellow-100 text-yellow-800';\n      case 'In Altezza': return 'bg-orange-100 text-orange-800';\n      case 'Esterno': return 'bg-blue-100 text-blue-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const totalPages = Math.ceil(pagination.total_count / pagination.per_page);\n\n  if (loading && workLogs.length === 0) {\n    return (\n      <Box sx={{ p: 4, textAlign: 'center' }}>\n        <CircularProgress sx={{ mb: 2 }} />\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Caricamento work logs...\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Se non c'è un cantiere attivo, mostra un messaggio\n  if (!currentCantiereId) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\" sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ mb: 1 }}>\n            Nessun cantiere attivo selezionato\n          </Typography>\n          <Typography variant=\"body2\">\n            Per visualizzare e gestire i work logs, seleziona prima un cantiere dalla dashboard principale.\n          </Typography>\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header moderno con cantiere attivo */}\n      <Box sx={{ mb: 4 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Assignment sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />\n            <Box>\n              <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700 }}>\n                Work Logs\n              </Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Construction sx={{ fontSize: 20, color: 'text.secondary', mr: 1 }} />\n                <Typography variant=\"subtitle1\" color=\"text.secondary\">\n                  Cantiere: <strong>{currentCantiereName}</strong>\n                </Typography>\n                <Chip\n                  label={`ID: ${currentCantiereId}`}\n                  size=\"small\"\n                  color=\"primary\"\n                  variant=\"outlined\"\n                  sx={{ ml: 2 }}\n                />\n              </Box>\n            </Box>\n          </Box>\n\n          <Button\n            variant=\"contained\"\n            onClick={onCreateNew}\n            startIcon={<Add />}\n            size=\"large\"\n            sx={{\n              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n              '&:hover': {\n                background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n              }\n            }}\n          >\n            Nuovo Work Log\n          </Button>\n        </Box>\n\n        {/* KPI Cards moderne */}\n        <Grid container spacing={3}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                position: 'relative',\n                overflow: 'visible'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Totale Work Logs\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {pagination.total_count}\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <Assignment sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                color: 'white'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Quantità Totale\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {workLogs.reduce((sum, log) => sum + (log.quantity || 0), 0).toFixed(1)}\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                      metri/unità\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <TrendingUp sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                color: 'white'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Ore-Uomo Totali\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {workLogs.reduce((sum, log) => sum + (log.total_man_hours || 0), 0).toFixed(1)}\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                      ore lavorate\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <Schedule sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n                color: 'white'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Produttività Media\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {workLogs.length > 0 ?\n                        (workLogs.reduce((sum, log) => sum + (log.productivity_per_hour || 0), 0) / workLogs.length).toFixed(2)\n                        : '0.00'\n                      }\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                      unità/ora\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <Speed sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </Box>\n\n      {/* Filtri moderni */}\n      <Accordion elevation={3} sx={{ mb: 3 }}>\n        <AccordionSummary\n          expandIcon={<ExpandMore />}\n          sx={{\n            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n            '&:hover': {\n              background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n            }\n          }}\n        >\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <FilterList sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n              Filtri Avanzati\n            </Typography>\n            <Box sx={{ ml: 'auto', mr: 2 }}>\n              {Object.values(filters).some(v => v !== '') && (\n                <Chip\n                  label=\"Filtri Attivi\"\n                  color=\"primary\"\n                  size=\"small\"\n                  variant=\"outlined\"\n                />\n              )}\n            </Box>\n          </Box>\n        </AccordionSummary>\n        <AccordionDetails>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6} lg={3}>\n              <FormControl fullWidth size=\"small\">\n                <InputLabel>Attività</InputLabel>\n                <Select\n                  name=\"activity_type\"\n                  value={filters.activity_type}\n                  onChange={handleFilterChange}\n                  label=\"Attività\"\n                >\n                  <MenuItem value=\"\">Tutte</MenuItem>\n                  <MenuItem value=\"Posa\">🔧 Posa</MenuItem>\n                  <MenuItem value=\"Collegamento\">🔌 Collegamento</MenuItem>\n                  <MenuItem value=\"Certificazione\">✅ Certificazione</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6} lg={3}>\n              <FormControl fullWidth size=\"small\">\n                <InputLabel>Operatore</InputLabel>\n                <Select\n                  name=\"operator_id\"\n                  value={filters.operator_id}\n                  onChange={handleFilterChange}\n                  label=\"Operatore\"\n                >\n                  <MenuItem value=\"\">Tutti</MenuItem>\n                  {operators.map(op => (\n                    <MenuItem key={op.id_responsabile} value={op.id_responsabile}>\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <Person sx={{ mr: 1, fontSize: 16 }} />\n                        {op.nome_responsabile}\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n\n\n            <Grid item xs={12} md={6} lg={3}>\n              <TextField\n                fullWidth\n                size=\"small\"\n                type=\"date\"\n                name=\"start_date\"\n                label=\"Data Inizio\"\n                value={filters.start_date}\n                onChange={handleFilterChange}\n                InputLabelProps={{ shrink: true }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6} lg={3}>\n              <TextField\n                fullWidth\n                size=\"small\"\n                type=\"date\"\n                name=\"end_date\"\n                label=\"Data Fine\"\n                value={filters.end_date}\n                onChange={handleFilterChange}\n                InputLabelProps={{ shrink: true }}\n              />\n            </Grid>\n\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n                <Button\n                  variant=\"outlined\"\n                  onClick={clearFilters}\n                  startIcon={<Clear />}\n                  size=\"small\"\n                >\n                  Pulisci Filtri\n                </Button>\n              </Box>\n            </Grid>\n          </Grid>\n        </AccordionDetails>\n      </Accordion>\n\n      {/* Lista Work Logs con Cards moderne */}\n      {workLogs.length === 0 ? (\n        <Paper elevation={3} sx={{ p: 8, textAlign: 'center' }}>\n          <Assignment sx={{ fontSize: 80, mb: 2, opacity: 0.3, color: 'text.secondary' }} />\n          <Typography variant=\"h5\" sx={{ mb: 2, fontWeight: 600 }}>\n            Nessun work log trovato\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n            {Object.values(filters).some(v => v !== '')\n              ? 'Nessun risultato per i filtri selezionati. Prova a modificare i criteri di ricerca.'\n              : 'Inizia creando il tuo primo work log per monitorare la produttività.'\n            }\n          </Typography>\n          <Button\n            variant=\"contained\"\n            onClick={onCreateNew}\n            startIcon={<Add />}\n            size=\"large\"\n            sx={{\n              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n              '&:hover': {\n                background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n              }\n            }}\n          >\n            Crea il primo Work Log\n          </Button>\n        </Paper>\n      ) : (\n        <Grid container spacing={3}>\n          {workLogs.map((log) => (\n            <Grid item xs={12} md={6} lg={4} key={log.id}>\n              <Card\n                elevation={3}\n                sx={{\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    transform: 'translateY(-4px)',\n                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\n                  }\n                }}\n              >\n                <CardHeader\n                  avatar={\n                    <Avatar\n                      sx={{\n                        bgcolor: log.activity_type === 'Posa' ? 'primary.main' :\n                                log.activity_type === 'Collegamento' ? 'secondary.main' : 'success.main',\n                        width: 48,\n                        height: 48\n                      }}\n                    >\n                      {log.activity_type === 'Posa' ? <Engineering /> :\n                       log.activity_type === 'Collegamento' ? <Build /> : <Assessment />}\n                    </Avatar>\n                  }\n                  title={\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                      {log.activity_type}\n                    </Typography>\n                  }\n                  subheader={\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {log.sub_activity_detail || 'Dettaglio non specificato'}\n                    </Typography>\n                  }\n                  action={\n                    <Chip\n                      label={formatDateTime(log.start_timestamp)}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      color=\"primary\"\n                    />\n                  }\n                />\n\n                <CardContent sx={{ flexGrow: 1, pt: 0 }}>\n                  {/* Informazioni principali */}\n                  <Grid container spacing={2} sx={{ mb: 2 }}>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'primary.50', borderRadius: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main' }}>\n                          {log.quantity?.toFixed(1)}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {log.activity_type === 'Posa' ? 'metri' : 'unità'}\n                        </Typography>\n                      </Box>\n                    </Grid>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'success.50', borderRadius: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'success.main' }}>\n                          {formatDuration(log.duration_minutes)}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          durata\n                        </Typography>\n                      </Box>\n                    </Grid>\n                  </Grid>\n\n                  {/* Dettagli operatore e produttività */}\n                  <Box sx={{ mb: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                      <Person sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />\n                      <Typography variant=\"body2\">\n                        Operatore #{log.operator_id}\n                      </Typography>\n                      <Chip\n                        label={`${log.number_of_operators_on_task} op`}\n                        size=\"small\"\n                        sx={{ ml: 'auto' }}\n                      />\n                    </Box>\n\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                      <Speed sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />\n                      <Typography variant=\"body2\">\n                        {log.productivity_per_hour?.toFixed(2)} /h\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\" sx={{ ml: 1 }}>\n                        ({log.productivity_per_person_per_hour?.toFixed(2)} /h/op)\n                      </Typography>\n                    </Box>\n\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <LocationOn sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />\n                      <Typography variant=\"body2\">\n                        {log.environmental_conditions} • {log.tools_used}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  {/* Progress bar produttività */}\n                  <Box sx={{ mb: 2 }}>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Efficienza\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {Math.min(100, (log.productivity_per_person_per_hour / 100) * 100).toFixed(0)}%\n                      </Typography>\n                    </Box>\n                    <LinearProgress\n                      variant=\"determinate\"\n                      value={Math.min(100, (log.productivity_per_person_per_hour / 100) * 100)}\n                      sx={{\n                        height: 6,\n                        borderRadius: 3,\n                        bgcolor: 'grey.200',\n                        '& .MuiLinearProgress-bar': {\n                          borderRadius: 3,\n                          background: log.productivity_per_person_per_hour > 80\n                            ? 'linear-gradient(90deg, #4caf50, #8bc34a)'\n                            : log.productivity_per_person_per_hour > 50\n                            ? 'linear-gradient(90deg, #ff9800, #ffc107)'\n                            : 'linear-gradient(90deg, #f44336, #ff5722)'\n                        }\n                      }}\n                    />\n                  </Box>\n\n                  {/* Note se presenti */}\n                  {log.notes && (\n                    <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\" sx={{ fontStyle: 'italic' }}>\n                        \"{log.notes}\"\n                      </Typography>\n                    </Box>\n                  )}\n\n                  {/* Azioni */}\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>\n                    <Tooltip title=\"Modifica\">\n                      <IconButton\n                        onClick={() => onEdit(log)}\n                        color=\"primary\"\n                        size=\"small\"\n                      >\n                        <Edit />\n                      </IconButton>\n                    </Tooltip>\n                    <Tooltip title=\"Elimina\">\n                      <IconButton\n                        onClick={() => onDelete(log.id)}\n                        color=\"error\"\n                        size=\"small\"\n                      >\n                        <Delete />\n                      </IconButton>\n                    </Tooltip>\n                  </Box>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Paginazione moderna */}\n      {totalPages > 1 && (\n        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>\n          <Paper elevation={2} sx={{ p: 2 }}>\n            <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Mostrando {(pagination.page - 1) * pagination.per_page + 1}-\n                {Math.min(pagination.page * pagination.per_page, pagination.total_count)} di {pagination.total_count}\n              </Typography>\n\n              <Pagination\n                count={totalPages}\n                page={pagination.page}\n                onChange={(event, value) => setPagination(prev => ({ ...prev, page: value }))}\n                color=\"primary\"\n                variant=\"outlined\"\n                shape=\"rounded\"\n                showFirstButton\n                showLastButton\n              />\n            </Stack>\n          </Paper>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default WorkLogsList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,YAAY,QACP,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,gBAAgB,EAAEC;EAAqB,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC5D,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC;IACrCkE,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC;IAC3CwE,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM6E,iBAAiB,GAAG,CAAAlB,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEmB,WAAW,KAClCC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC,IACxD,IAAI;EAC7B,MAAMC,mBAAmB,GAAG,CAAAvB,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEwB,QAAQ,KAC/BH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAC5C,0BAA0B;EAErDhF,SAAS,CAAC,MAAM;IACdmF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAENnF,SAAS,CAAC,MAAM;IACd,IAAI4E,iBAAiB,EAAE;MACrBQ,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACR,iBAAiB,EAAEb,OAAO,EAAEM,UAAU,CAACE,IAAI,CAAC,CAAC;;EAEjD;EACAvE,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4E,iBAAiB,EAAE;MACtBd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACc,iBAAiB,CAAC,CAAC;EAEvB,MAAMO,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,YAAY,GAAG,MAAMpC,aAAa,CAACqC,GAAG,CAAC,eAAe,CAAC;MAC7DX,YAAY,CAACU,YAAY,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC;EAED,MAAMJ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACR,iBAAiB,EAAE;MACtBa,OAAO,CAACC,IAAI,CAAC,oDAAoD,CAAC;MAClE5B,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACFA,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM6B,MAAM,GAAG;QACbpB,IAAI,EAAEF,UAAU,CAACE,IAAI;QACrBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;QAC7BK,WAAW,EAAED,iBAAiB;QAAE;QAChC,GAAGgB,MAAM,CAACC,WAAW,CACnBD,MAAM,CAACE,OAAO,CAAC/B,OAAO,CAAC,CAACgC,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,KAAKA,KAAK,KAAK,EAAE,CAC7D;MACF,CAAC;MAEDR,OAAO,CAACS,GAAG,CAAC,qCAAqC,EAAEtB,iBAAiB,EAAEK,mBAAmB,CAAC;MAE1F,MAAMkB,QAAQ,GAAG,MAAMlD,aAAa,CAACqC,GAAG,CAAC,eAAe,EAAE;QAAEK;MAAO,CAAC,CAAC;MAErE/B,WAAW,CAACuC,QAAQ,CAACZ,IAAI,CAACa,SAAS,IAAI,EAAE,CAAC;MAC1C9B,aAAa,CAAC+B,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP5B,WAAW,EAAE0B,QAAQ,CAACZ,IAAI,CAACd,WAAW,IAAI;MAC5C,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAM;MAAEC,IAAI;MAAEP;IAAM,CAAC,GAAGM,CAAC,CAACE,MAAM;IAChCzC,UAAU,CAACqC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACG,IAAI,GAAGP;IAAM,CAAC,CAAC,CAAC;IAChD3B,aAAa,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9B,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC;EAED,MAAMmC,YAAY,GAAGA,CAAA,KAAM;IACzB1C,UAAU,CAAC;MACTC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuC,cAAc,GAAIC,UAAU,IAAK;IACrC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;MAClDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,GAAG;IACxB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAOC,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKG,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;EACtD,CAAC;EAED,MAAMC,eAAe,GAAIC,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,cAAc;QAAE,OAAO,IAAI;MAChC,KAAK,gBAAgB;QAAE,OAAO,GAAG;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIC,SAAS,IAAK;IACvC,QAAQA,SAAS;MACf,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,iBAAiB;QAAE,OAAO,+BAA+B;MAC9D,KAAK,YAAY;QAAE,OAAO,+BAA+B;MACzD,KAAK,SAAS;QAAE,OAAO,2BAA2B;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMC,UAAU,GAAGP,IAAI,CAACQ,IAAI,CAAC1D,UAAU,CAACI,WAAW,GAAGJ,UAAU,CAACG,QAAQ,CAAC;EAE1E,IAAIX,OAAO,IAAIF,QAAQ,CAACqE,MAAM,KAAK,CAAC,EAAE;IACpC,oBACE7E,OAAA,CAAClD,GAAG;MAACgI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrCjF,OAAA,CAAChC,gBAAgB;QAAC8G,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCtF,OAAA,CAAChD,UAAU;QAACuI,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEhD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC7D,iBAAiB,EAAE;IACtB,oBACEzB,OAAA,CAAClD,GAAG;MAACgI,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,eAChBjF,OAAA,CAACjC,KAAK;QAAC0H,QAAQ,EAAC,SAAS;QAACX,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBACtCjF,OAAA,CAAChD,UAAU;UAACuI,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAExC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;UAACuI,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEtF,OAAA,CAAClD,GAAG;IAACgI,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBjF,OAAA,CAAClD,GAAG;MAACgI,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjBjF,OAAA,CAAClD,GAAG;QAACgI,EAAE,EAAE;UAAEY,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEV,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBACzFjF,OAAA,CAAClD,GAAG;UAACgI,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAX,QAAA,gBACjDjF,OAAA,CAACtB,UAAU;YAACoG,EAAE,EAAE;cAAEe,QAAQ,EAAE,EAAE;cAAEL,KAAK,EAAE,cAAc;cAAEM,EAAE,EAAE;YAAE;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClEtF,OAAA,CAAClD,GAAG;YAAAmI,QAAA,gBACFjF,OAAA,CAAChD,UAAU;cAACuI,OAAO,EAAC,IAAI;cAACQ,SAAS,EAAC,IAAI;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE;cAAI,CAAE;cAAAf,QAAA,EAAC;YAEjE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtF,OAAA,CAAClD,GAAG;cAACgI,EAAE,EAAE;gBAAEY,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEK,EAAE,EAAE;cAAE,CAAE;cAAAhB,QAAA,gBACxDjF,OAAA,CAACJ,YAAY;gBAACkF,EAAE,EAAE;kBAAEe,QAAQ,EAAE,EAAE;kBAAEL,KAAK,EAAE,gBAAgB;kBAAEM,EAAE,EAAE;gBAAE;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtEtF,OAAA,CAAChD,UAAU;gBAACuI,OAAO,EAAC,WAAW;gBAACC,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,GAAC,YAC3C,eAAAjF,OAAA;kBAAAiF,QAAA,EAASnD;gBAAmB;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACbtF,OAAA,CAACpC,IAAI;gBACHsI,KAAK,EAAE,OAAOzE,iBAAiB,EAAG;gBAClC0E,IAAI,EAAC,OAAO;gBACZX,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBT,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtF,OAAA,CAACtC,MAAM;UACL6H,OAAO,EAAC,WAAW;UACnBc,OAAO,EAAEjG,WAAY;UACrBkG,SAAS,eAAEtG,OAAA,CAACZ,GAAG;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBa,IAAI,EAAC,OAAO;UACZrB,EAAE,EAAE;YACFyB,UAAU,EAAE,kDAAkD;YAC9D,SAAS,EAAE;cACTA,UAAU,EAAE;YACd;UACF,CAAE;UAAAtB,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNtF,OAAA,CAAC/C,IAAI;QAACuJ,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAxB,QAAA,gBACzBjF,OAAA,CAAC/C,IAAI;UAACyJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BjF,OAAA,CAAC9C,IAAI;YACH4J,SAAS,EAAE,CAAE;YACbhC,EAAE,EAAE;cACFyB,UAAU,EAAE,mDAAmD;cAC/Df,KAAK,EAAE,OAAO;cACduB,QAAQ,EAAE,UAAU;cACpBC,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,eAEFjF,OAAA,CAAC7C,WAAW;cAAC2H,EAAE,EAAE;gBAAEmC,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,eACzBjF,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEY,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAV,QAAA,gBAClFjF,OAAA,CAAClD,GAAG;kBAAAmI,QAAA,gBACFjF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEoC,OAAO,EAAE,GAAG;sBAAEhC,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEkB,UAAU,EAAE;oBAAI,CAAE;oBAAAf,QAAA,EAC9C/D,UAAU,CAACI;kBAAW;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNtF,OAAA,CAACnC,MAAM;kBAACiH,EAAE,EAAE;oBAAEqC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAApC,QAAA,eACtEjF,OAAA,CAACtB,UAAU;oBAACoG,EAAE,EAAE;sBAAEe,QAAQ,EAAE;oBAAG;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPtF,OAAA,CAAC/C,IAAI;UAACyJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BjF,OAAA,CAAC9C,IAAI;YACH4J,SAAS,EAAE,CAAE;YACbhC,EAAE,EAAE;cACFyB,UAAU,EAAE,mDAAmD;cAC/Df,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,eAEFjF,OAAA,CAAC7C,WAAW;cAAC2H,EAAE,EAAE;gBAAEmC,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,eACzBjF,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEY,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAV,QAAA,gBAClFjF,OAAA,CAAClD,GAAG;kBAAAmI,QAAA,gBACFjF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEoC,OAAO,EAAE,GAAG;sBAAEhC,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEkB,UAAU,EAAE;oBAAI,CAAE;oBAAAf,QAAA,EAC9CzE,QAAQ,CAAC8G,MAAM,CAAC,CAACC,GAAG,EAAExE,GAAG,KAAKwE,GAAG,IAAIxE,GAAG,CAACyE,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;kBAAC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,SAAS;oBAACT,EAAE,EAAE;sBAAEoC,OAAO,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNtF,OAAA,CAACnC,MAAM;kBAACiH,EAAE,EAAE;oBAAEqC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAApC,QAAA,eACtEjF,OAAA,CAAClB,UAAU;oBAACgG,EAAE,EAAE;sBAAEe,QAAQ,EAAE;oBAAG;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPtF,OAAA,CAAC/C,IAAI;UAACyJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BjF,OAAA,CAAC9C,IAAI;YACH4J,SAAS,EAAE,CAAE;YACbhC,EAAE,EAAE;cACFyB,UAAU,EAAE,mDAAmD;cAC/Df,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,eAEFjF,OAAA,CAAC7C,WAAW;cAAC2H,EAAE,EAAE;gBAAEmC,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,eACzBjF,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEY,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAV,QAAA,gBAClFjF,OAAA,CAAClD,GAAG;kBAAAmI,QAAA,gBACFjF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEoC,OAAO,EAAE,GAAG;sBAAEhC,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEkB,UAAU,EAAE;oBAAI,CAAE;oBAAAf,QAAA,EAC9CzE,QAAQ,CAAC8G,MAAM,CAAC,CAACC,GAAG,EAAExE,GAAG,KAAKwE,GAAG,IAAIxE,GAAG,CAAC2E,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACD,OAAO,CAAC,CAAC;kBAAC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,SAAS;oBAACT,EAAE,EAAE;sBAAEoC,OAAO,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNtF,OAAA,CAACnC,MAAM;kBAACiH,EAAE,EAAE;oBAAEqC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAApC,QAAA,eACtEjF,OAAA,CAACpB,QAAQ;oBAACkG,EAAE,EAAE;sBAAEe,QAAQ,EAAE;oBAAG;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPtF,OAAA,CAAC/C,IAAI;UAACyJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BjF,OAAA,CAAC9C,IAAI;YACH4J,SAAS,EAAE,CAAE;YACbhC,EAAE,EAAE;cACFyB,UAAU,EAAE,mDAAmD;cAC/Df,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,eAEFjF,OAAA,CAAC7C,WAAW;cAAC2H,EAAE,EAAE;gBAAEmC,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,eACzBjF,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEY,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAV,QAAA,gBAClFjF,OAAA,CAAClD,GAAG;kBAAAmI,QAAA,gBACFjF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEoC,OAAO,EAAE,GAAG;sBAAEhC,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEkB,UAAU,EAAE;oBAAI,CAAE;oBAAAf,QAAA,EAC9CzE,QAAQ,CAACqE,MAAM,GAAG,CAAC,GAClB,CAACrE,QAAQ,CAAC8G,MAAM,CAAC,CAACC,GAAG,EAAExE,GAAG,KAAKwE,GAAG,IAAIxE,GAAG,CAAC4E,qBAAqB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGnH,QAAQ,CAACqE,MAAM,EAAE4C,OAAO,CAAC,CAAC,CAAC,GACrG;kBAAM;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEA,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,SAAS;oBAACT,EAAE,EAAE;sBAAEoC,OAAO,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNtF,OAAA,CAACnC,MAAM;kBAACiH,EAAE,EAAE;oBAAEqC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAApC,QAAA,eACtEjF,OAAA,CAACV,KAAK;oBAACwF,EAAE,EAAE;sBAAEe,QAAQ,EAAE;oBAAG;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNtF,OAAA,CAACzB,SAAS;MAACuI,SAAS,EAAE,CAAE;MAAChC,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACrCjF,OAAA,CAACxB,gBAAgB;QACfoJ,UAAU,eAAE5H,OAAA,CAACX,UAAU;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BR,EAAE,EAAE;UACFyB,UAAU,EAAE,mDAAmD;UAC/D,SAAS,EAAE;YACTA,UAAU,EAAE;UACd;QACF,CAAE;QAAAtB,QAAA,eAEFjF,OAAA,CAAClD,GAAG;UAACgI,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEwB,KAAK,EAAE;UAAO,CAAE;UAAAnC,QAAA,gBAChEjF,OAAA,CAACjB,UAAU;YAAC+F,EAAE,EAAE;cAAEgB,EAAE,EAAE,CAAC;cAAEN,KAAK,EAAE;YAAe;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDtF,OAAA,CAAChD,UAAU;YAACuI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAf,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtF,OAAA,CAAClD,GAAG;YAACgI,EAAE,EAAE;cAAEsB,EAAE,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EAC5BxC,MAAM,CAACoF,MAAM,CAACjH,OAAO,CAAC,CAACkH,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,iBACzC/H,OAAA,CAACpC,IAAI;cACHsI,KAAK,EAAC,eAAe;cACrBV,KAAK,EAAC,SAAS;cACfW,IAAI,EAAC,OAAO;cACZZ,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eACnBtF,OAAA,CAACvB,gBAAgB;QAAAwG,QAAA,eACfjF,OAAA,CAAC/C,IAAI;UAACuJ,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAxB,QAAA,gBACzBjF,OAAA,CAAC/C,IAAI;YAACyJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACmB,EAAE,EAAE,CAAE;YAAA/C,QAAA,eAC9BjF,OAAA,CAACxC,WAAW;cAACyK,SAAS;cAAC9B,IAAI,EAAC,OAAO;cAAAlB,QAAA,gBACjCjF,OAAA,CAACvC,UAAU;gBAAAwH,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCtF,OAAA,CAAC1C,MAAM;gBACL+F,IAAI,EAAC,eAAe;gBACpBP,KAAK,EAAElC,OAAO,CAACE,aAAc;gBAC7BoH,QAAQ,EAAE/E,kBAAmB;gBAC7B+C,KAAK,EAAC,aAAU;gBAAAjB,QAAA,gBAEhBjF,OAAA,CAACzC,QAAQ;kBAACuF,KAAK,EAAC,EAAE;kBAAAmC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCtF,OAAA,CAACzC,QAAQ;kBAACuF,KAAK,EAAC,MAAM;kBAAAmC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACzCtF,OAAA,CAACzC,QAAQ;kBAACuF,KAAK,EAAC,cAAc;kBAAAmC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACzDtF,OAAA,CAACzC,QAAQ;kBAACuF,KAAK,EAAC,gBAAgB;kBAAAmC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPtF,OAAA,CAAC/C,IAAI;YAACyJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACmB,EAAE,EAAE,CAAE;YAAA/C,QAAA,eAC9BjF,OAAA,CAACxC,WAAW;cAACyK,SAAS;cAAC9B,IAAI,EAAC,OAAO;cAAAlB,QAAA,gBACjCjF,OAAA,CAACvC,UAAU;gBAAAwH,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCtF,OAAA,CAAC1C,MAAM;gBACL+F,IAAI,EAAC,aAAa;gBAClBP,KAAK,EAAElC,OAAO,CAACG,WAAY;gBAC3BmH,QAAQ,EAAE/E,kBAAmB;gBAC7B+C,KAAK,EAAC,WAAW;gBAAAjB,QAAA,gBAEjBjF,OAAA,CAACzC,QAAQ;kBAACuF,KAAK,EAAC,EAAE;kBAAAmC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC/D,SAAS,CAAC4G,GAAG,CAACC,EAAE,iBACfpI,OAAA,CAACzC,QAAQ;kBAA0BuF,KAAK,EAAEsF,EAAE,CAACC,eAAgB;kBAAApD,QAAA,eAC3DjF,OAAA,CAAClD,GAAG;oBAACgI,EAAE,EAAE;sBAAEY,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE;oBAAS,CAAE;oBAAAX,QAAA,gBACjDjF,OAAA,CAACrB,MAAM;sBAACmG,EAAE,EAAE;wBAAEgB,EAAE,EAAE,CAAC;wBAAED,QAAQ,EAAE;sBAAG;oBAAE;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtC8C,EAAE,CAACE,iBAAiB;kBAAA;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC,GAJO8C,EAAE,CAACC,eAAe;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKvB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAIPtF,OAAA,CAAC/C,IAAI;YAACyJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACmB,EAAE,EAAE,CAAE;YAAA/C,QAAA,eAC9BjF,OAAA,CAAC3C,SAAS;cACR4K,SAAS;cACT9B,IAAI,EAAC,OAAO;cACZoC,IAAI,EAAC,MAAM;cACXlF,IAAI,EAAC,YAAY;cACjB6C,KAAK,EAAC,aAAa;cACnBpD,KAAK,EAAElC,OAAO,CAACI,UAAW;cAC1BkH,QAAQ,EAAE/E,kBAAmB;cAC7BqF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPtF,OAAA,CAAC/C,IAAI;YAACyJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACmB,EAAE,EAAE,CAAE;YAAA/C,QAAA,eAC9BjF,OAAA,CAAC3C,SAAS;cACR4K,SAAS;cACT9B,IAAI,EAAC,OAAO;cACZoC,IAAI,EAAC,MAAM;cACXlF,IAAI,EAAC,UAAU;cACf6C,KAAK,EAAC,WAAW;cACjBpD,KAAK,EAAElC,OAAO,CAACK,QAAS;cACxBiH,QAAQ,EAAE/E,kBAAmB;cAC7BqF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPtF,OAAA,CAAC/C,IAAI;YAACyJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBjF,OAAA,CAAClD,GAAG;cAACgI,EAAE,EAAE;gBAAEY,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAE+C,GAAG,EAAE;cAAE,CAAE;cAAAzD,QAAA,eAC/DjF,OAAA,CAACtC,MAAM;gBACL6H,OAAO,EAAC,UAAU;gBAClBc,OAAO,EAAE9C,YAAa;gBACtB+C,SAAS,eAAEtG,OAAA,CAAChB,KAAK;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBa,IAAI,EAAC,OAAO;gBAAAlB,QAAA,EACb;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGX9E,QAAQ,CAACqE,MAAM,KAAK,CAAC,gBACpB7E,OAAA,CAACrC,KAAK;MAACmJ,SAAS,EAAE,CAAE;MAAChC,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrDjF,OAAA,CAACtB,UAAU;QAACoG,EAAE,EAAE;UAAEe,QAAQ,EAAE,EAAE;UAAEX,EAAE,EAAE,CAAC;UAAEgC,OAAO,EAAE,GAAG;UAAE1B,KAAK,EAAE;QAAiB;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClFtF,OAAA,CAAChD,UAAU;QAACuI,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEI,EAAE,EAAE,CAAC;UAAEc,UAAU,EAAE;QAAI,CAAE;QAAAf,QAAA,EAAC;MAEzD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;QAACuI,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,EAC9DxC,MAAM,CAACoF,MAAM,CAACjH,OAAO,CAAC,CAACkH,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,GACvC,qFAAqF,GACrF;MAAsE;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhE,CAAC,eACbtF,OAAA,CAACtC,MAAM;QACL6H,OAAO,EAAC,WAAW;QACnBc,OAAO,EAAEjG,WAAY;QACrBkG,SAAS,eAAEtG,OAAA,CAACZ,GAAG;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnBa,IAAI,EAAC,OAAO;QACZrB,EAAE,EAAE;UACFyB,UAAU,EAAE,kDAAkD;UAC9D,SAAS,EAAE;YACTA,UAAU,EAAE;UACd;QACF,CAAE;QAAAtB,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAERtF,OAAA,CAAC/C,IAAI;MAACuJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAxB,QAAA,EACxBzE,QAAQ,CAAC2H,GAAG,CAAEpF,GAAG;QAAA,IAAA4F,aAAA,EAAAC,qBAAA,EAAAC,sBAAA;QAAA,oBAChB7I,OAAA,CAAC/C,IAAI;UAACyJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAACmB,EAAE,EAAE,CAAE;UAAA/C,QAAA,eAC9BjF,OAAA,CAAC9C,IAAI;YACH4J,SAAS,EAAE,CAAE;YACbhC,EAAE,EAAE;cACFuC,MAAM,EAAE,MAAM;cACd3B,OAAO,EAAE,MAAM;cACfoD,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAAhE,QAAA,gBAEFjF,OAAA,CAAC5C,UAAU;cACT8L,MAAM,eACJlJ,OAAA,CAACnC,MAAM;gBACLiH,EAAE,EAAE;kBACFqC,OAAO,EAAEpE,GAAG,CAACjC,aAAa,KAAK,MAAM,GAAG,cAAc,GAC9CiC,GAAG,CAACjC,aAAa,KAAK,cAAc,GAAG,gBAAgB,GAAG,cAAc;kBAChFsG,KAAK,EAAE,EAAE;kBACTC,MAAM,EAAE;gBACV,CAAE;gBAAApC,QAAA,EAEDlC,GAAG,CAACjC,aAAa,KAAK,MAAM,gBAAGd,OAAA,CAACnB,WAAW;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAC9CvC,GAAG,CAACjC,aAAa,KAAK,cAAc,gBAAGd,OAAA,CAACN,KAAK;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGtF,OAAA,CAACL,UAAU;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACT;cACD6D,KAAK,eACHnJ,OAAA,CAAChD,UAAU;gBAACuI,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEkB,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAC9ClC,GAAG,CAACjC;cAAa;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACb;cACD8D,SAAS,eACPpJ,OAAA,CAAChD,UAAU;gBAACuI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,EAC/ClC,GAAG,CAACsG,mBAAmB,IAAI;cAA2B;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CACb;cACDgE,MAAM,eACJtJ,OAAA,CAACpC,IAAI;gBACHsI,KAAK,EAAE1C,cAAc,CAACT,GAAG,CAACwG,eAAe,CAAE;gBAC3CpD,IAAI,EAAC,OAAO;gBACZZ,OAAO,EAAC,UAAU;gBAClBC,KAAK,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFtF,OAAA,CAAC7C,WAAW;cAAC2H,EAAE,EAAE;gBAAE0E,QAAQ,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAxE,QAAA,gBAEtCjF,OAAA,CAAC/C,IAAI;gBAACuJ,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAC3B,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,gBACxCjF,OAAA,CAAC/C,IAAI;kBAACyJ,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA1B,QAAA,eACfjF,OAAA,CAAClD,GAAG;oBAACgI,EAAE,EAAE;sBAAEE,SAAS,EAAE,QAAQ;sBAAED,CAAC,EAAE,CAAC;sBAAEoC,OAAO,EAAE,YAAY;sBAAEuC,YAAY,EAAE;oBAAE,CAAE;oBAAAzE,QAAA,gBAC7EjF,OAAA,CAAChD,UAAU;sBAACuI,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEkB,UAAU,EAAE,GAAG;wBAAER,KAAK,EAAE;sBAAe,CAAE;sBAAAP,QAAA,GAAA0D,aAAA,GACrE5F,GAAG,CAACyE,QAAQ,cAAAmB,aAAA,uBAAZA,aAAA,CAAclB,OAAO,CAAC,CAAC;oBAAC;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACbtF,OAAA,CAAChD,UAAU;sBAACuI,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EACjDlC,GAAG,CAACjC,aAAa,KAAK,MAAM,GAAG,OAAO,GAAG;oBAAO;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACPtF,OAAA,CAAC/C,IAAI;kBAACyJ,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA1B,QAAA,eACfjF,OAAA,CAAClD,GAAG;oBAACgI,EAAE,EAAE;sBAAEE,SAAS,EAAE,QAAQ;sBAAED,CAAC,EAAE,CAAC;sBAAEoC,OAAO,EAAE,YAAY;sBAAEuC,YAAY,EAAE;oBAAE,CAAE;oBAAAzE,QAAA,gBAC7EjF,OAAA,CAAChD,UAAU;sBAACuI,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEkB,UAAU,EAAE,GAAG;wBAAER,KAAK,EAAE;sBAAe,CAAE;sBAAAP,QAAA,EACrEhB,cAAc,CAAClB,GAAG,CAAC4G,gBAAgB;oBAAC;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACbtF,OAAA,CAAChD,UAAU;sBAACuI,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGPtF,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,gBACjBjF,OAAA,CAAClD,GAAG;kBAACgI,EAAE,EAAE;oBAAEY,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEV,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,gBACxDjF,OAAA,CAACrB,MAAM;oBAACmG,EAAE,EAAE;sBAAEgB,EAAE,EAAE,CAAC;sBAAED,QAAQ,EAAE,EAAE;sBAAEL,KAAK,EAAE;oBAAiB;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChEtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,OAAO;oBAAAN,QAAA,GAAC,aACf,EAAClC,GAAG,CAAChC,WAAW;kBAAA;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACbtF,OAAA,CAACpC,IAAI;oBACHsI,KAAK,EAAE,GAAGnD,GAAG,CAAC6G,2BAA2B,KAAM;oBAC/CzD,IAAI,EAAC,OAAO;oBACZrB,EAAE,EAAE;sBAAEsB,EAAE,EAAE;oBAAO;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENtF,OAAA,CAAClD,GAAG;kBAACgI,EAAE,EAAE;oBAAEY,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEV,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,gBACxDjF,OAAA,CAACV,KAAK;oBAACwF,EAAE,EAAE;sBAAEgB,EAAE,EAAE,CAAC;sBAAED,QAAQ,EAAE,EAAE;sBAAEL,KAAK,EAAE;oBAAiB;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/DtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,OAAO;oBAAAN,QAAA,IAAA2D,qBAAA,GACxB7F,GAAG,CAAC4E,qBAAqB,cAAAiB,qBAAA,uBAAzBA,qBAAA,CAA2BnB,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAACV,EAAE,EAAE;sBAAEsB,EAAE,EAAE;oBAAE,CAAE;oBAAAnB,QAAA,GAAC,GACjE,GAAA4D,sBAAA,GAAC9F,GAAG,CAAC8G,gCAAgC,cAAAhB,sBAAA,uBAApCA,sBAAA,CAAsCpB,OAAO,CAAC,CAAC,CAAC,EAAC,SACrD;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENtF,OAAA,CAAClD,GAAG;kBAACgI,EAAE,EAAE;oBAAEY,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAX,QAAA,gBACjDjF,OAAA,CAACP,UAAU;oBAACqF,EAAE,EAAE;sBAAEgB,EAAE,EAAE,CAAC;sBAAED,QAAQ,EAAE,EAAE;sBAAEL,KAAK,EAAE;oBAAiB;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpEtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,OAAO;oBAAAN,QAAA,GACxBlC,GAAG,CAAC+G,wBAAwB,EAAC,UAAG,EAAC/G,GAAG,CAACgH,UAAU;kBAAA;oBAAA5E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtF,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,gBACjBjF,OAAA,CAAClD,GAAG;kBAACgI,EAAE,EAAE;oBAAEY,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAET,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,gBACnEjF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbtF,OAAA,CAAChD,UAAU;oBAACuI,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,GACjDb,IAAI,CAAC4F,GAAG,CAAC,GAAG,EAAGjH,GAAG,CAAC8G,gCAAgC,GAAG,GAAG,GAAI,GAAG,CAAC,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAC,GAChF;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNtF,OAAA,CAAC5B,cAAc;kBACbmH,OAAO,EAAC,aAAa;kBACrBzC,KAAK,EAAEsB,IAAI,CAAC4F,GAAG,CAAC,GAAG,EAAGjH,GAAG,CAAC8G,gCAAgC,GAAG,GAAG,GAAI,GAAG,CAAE;kBACzE/E,EAAE,EAAE;oBACFuC,MAAM,EAAE,CAAC;oBACTqC,YAAY,EAAE,CAAC;oBACfvC,OAAO,EAAE,UAAU;oBACnB,0BAA0B,EAAE;sBAC1BuC,YAAY,EAAE,CAAC;sBACfnD,UAAU,EAAExD,GAAG,CAAC8G,gCAAgC,GAAG,EAAE,GACjD,0CAA0C,GAC1C9G,GAAG,CAAC8G,gCAAgC,GAAG,EAAE,GACzC,0CAA0C,GAC1C;oBACN;kBACF;gBAAE;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGLvC,GAAG,CAACkH,KAAK,iBACRjK,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEoC,OAAO,EAAE,SAAS;kBAAEuC,YAAY,EAAE,CAAC;kBAAExE,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,eAC5DjF,OAAA,CAAChD,UAAU;kBAACuI,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,gBAAgB;kBAACV,EAAE,EAAE;oBAAEoF,SAAS,EAAE;kBAAS,CAAE;kBAAAjF,QAAA,GAAC,IAC/E,EAAClC,GAAG,CAACkH,KAAK,EAAC,IACd;gBAAA;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,eAGDtF,OAAA,CAAClD,GAAG;gBAACgI,EAAE,EAAE;kBAAEY,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,UAAU;kBAAE+C,GAAG,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,gBAC/DjF,OAAA,CAAC9B,OAAO;kBAACiL,KAAK,EAAC,UAAU;kBAAAlE,QAAA,eACvBjF,OAAA,CAAC/B,UAAU;oBACToI,OAAO,EAAEA,CAAA,KAAMnG,MAAM,CAAC6C,GAAG,CAAE;oBAC3ByC,KAAK,EAAC,SAAS;oBACfW,IAAI,EAAC,OAAO;oBAAAlB,QAAA,eAEZjF,OAAA,CAACf,IAAI;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACVtF,OAAA,CAAC9B,OAAO;kBAACiL,KAAK,EAAC,SAAS;kBAAAlE,QAAA,eACtBjF,OAAA,CAAC/B,UAAU;oBACToI,OAAO,EAAEA,CAAA,KAAMlG,QAAQ,CAAC4C,GAAG,CAACoH,EAAE,CAAE;oBAChC3E,KAAK,EAAC,OAAO;oBACbW,IAAI,EAAC,OAAO;oBAAAlB,QAAA,eAEZjF,OAAA,CAACd,MAAM;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GArK6BvC,GAAG,CAACoH,EAAE;UAAAhF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsKtC,CAAC;MAAA,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,EAGAX,UAAU,GAAG,CAAC,iBACb3E,OAAA,CAAClD,GAAG;MAACgI,EAAE,EAAE;QAAEmB,EAAE,EAAE,CAAC;QAAEP,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAV,QAAA,eAC5DjF,OAAA,CAACrC,KAAK;QAACmJ,SAAS,EAAE,CAAE;QAAChC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,eAChCjF,OAAA,CAAC1B,KAAK;UAAC8L,SAAS,EAAC,KAAK;UAAC3D,OAAO,EAAE,CAAE;UAACb,UAAU,EAAC,QAAQ;UAAAX,QAAA,gBACpDjF,OAAA,CAAChD,UAAU;YAACuI,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAP,QAAA,GAAC,YACvC,EAAC,CAAC/D,UAAU,CAACE,IAAI,GAAG,CAAC,IAAIF,UAAU,CAACG,QAAQ,GAAG,CAAC,EAAC,GAC3D,EAAC+C,IAAI,CAAC4F,GAAG,CAAC9I,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACG,QAAQ,EAAEH,UAAU,CAACI,WAAW,CAAC,EAAC,MAAI,EAACJ,UAAU,CAACI,WAAW;UAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEbtF,OAAA,CAAC3B,UAAU;YACTgM,KAAK,EAAE1F,UAAW;YAClBvD,IAAI,EAAEF,UAAU,CAACE,IAAK;YACtB8G,QAAQ,EAAEA,CAACoC,KAAK,EAAExH,KAAK,KAAK3B,aAAa,CAAC+B,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE9B,IAAI,EAAE0B;YAAM,CAAC,CAAC,CAAE;YAC9E0C,KAAK,EAAC,SAAS;YACfD,OAAO,EAAC,UAAU;YAClBgF,KAAK,EAAC,SAAS;YACfC,eAAe;YACfC,cAAc;UAAA;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CAlqBIJ,YAAY;EAAA,QACmCJ,OAAO;AAAA;AAAA6K,EAAA,GADtDzK,YAAY;AAoqBlB,eAAeA,YAAY;AAAC,IAAAyK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}