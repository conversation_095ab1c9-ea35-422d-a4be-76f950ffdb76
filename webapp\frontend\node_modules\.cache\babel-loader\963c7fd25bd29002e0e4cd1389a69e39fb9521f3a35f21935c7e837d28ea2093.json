{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\WorkLogForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, TextField, Select, MenuItem, FormControl, InputLabel, Button, Paper, Chip, Avatar, Divider, Alert, CircularProgress, Stepper, Step, StepLabel, StepContent } from '@mui/material';\nimport { Assignment, Person, Schedule, Engineering, Save, Cancel, CheckCircle, Construction } from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkLogForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    operator_id: '',\n    cable_type_id: '',\n    activity_type: 'Posa',\n    sub_activity_detail: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    quantity: '',\n    start_timestamp: '',\n    end_timestamp: '',\n    number_of_operators_on_task: 1,\n    notes: '',\n    id_cantiere: ''\n  });\n  const [operators, setOperators] = useState([]);\n  const [cableTypes, setCableTypes] = useState([]);\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [{\n    value: 'Posa',\n    label: 'Posa'\n  }, {\n    value: 'Collegamento',\n    label: 'Collegamento'\n  }, {\n    value: 'Certificazione',\n    label: 'Certificazione'\n  }];\n  const environmentalConditions = [{\n    value: 'Normale',\n    label: 'Normale'\n  }, {\n    value: 'Spazi Ristretti',\n    label: 'Spazi Ristretti'\n  }, {\n    value: 'In Altezza',\n    label: 'In Altezza'\n  }, {\n    value: 'Esterno',\n    label: 'Esterno'\n  }];\n  const toolsUsed = [{\n    value: 'Manuale',\n    label: 'Manuale'\n  }, {\n    value: 'Automatico',\n    label: 'Automatico'\n  }];\n  useEffect(() => {\n    loadInitialData();\n    if (initialData) {\n      setFormData({\n        ...initialData,\n        start_timestamp: formatDateTimeLocal(initialData.start_timestamp),\n        end_timestamp: formatDateTimeLocal(initialData.end_timestamp)\n      });\n    }\n  }, [initialData]);\n  const formatDateTimeLocal = dateString => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toISOString().slice(0, 16);\n  };\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n\n      // Carica operatori, tipi di cavo e cantieri in parallelo\n      const [operatorsRes, cableTypesRes, cantieriRes] = await Promise.all([axiosInstance.get('/responsabili'), axiosInstance.get('/admin/tipologie-cavi'), axiosInstance.get('/cantieri')]);\n      setOperators(operatorsRes.data || []);\n      setCableTypes(cableTypesRes.data || []);\n      setCantieri(cantieriRes.data || []);\n\n      // Se c'è solo un cantiere, selezionalo automaticamente\n      if (cantieriRes.data && cantieriRes.data.length === 1) {\n        setFormData(prev => ({\n          ...prev,\n          id_cantiere: cantieriRes.data[0].id_cantiere\n        }));\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n      setErrors({\n        general: 'Errore nel caricamento dei dati'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n\n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.operator_id) newErrors.operator_id = 'Operatore richiesto';\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Quantità deve essere maggiore di 0';\n    if (!formData.start_timestamp) newErrors.start_timestamp = 'Data/ora inizio richiesta';\n    if (!formData.end_timestamp) newErrors.end_timestamp = 'Data/ora fine richiesta';\n    if (!formData.id_cantiere) newErrors.id_cantiere = 'Cantiere richiesto';\n    if (!formData.number_of_operators_on_task || formData.number_of_operators_on_task < 1) {\n      newErrors.number_of_operators_on_task = 'Numero operatori deve essere almeno 1';\n    }\n\n    // Valida che end_timestamp sia dopo start_timestamp\n    if (formData.start_timestamp && formData.end_timestamp) {\n      const start = new Date(formData.start_timestamp);\n      const end = new Date(formData.end_timestamp);\n      if (end <= start) {\n        newErrors.end_timestamp = 'Data/ora fine deve essere dopo inizio';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Prepara i dati per l'invio\n      const submitData = {\n        ...formData,\n        operator_id: parseInt(formData.operator_id),\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        quantity: parseFloat(formData.quantity),\n        number_of_operators_on_task: parseInt(formData.number_of_operators_on_task),\n        id_cantiere: parseInt(formData.id_cantiere),\n        start_timestamp: new Date(formData.start_timestamp).toISOString(),\n        end_timestamp: new Date(formData.end_timestamp).toISOString()\n      };\n      await onSubmit(submitData);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore nell\\'invio del form:', error);\n      setErrors({\n        general: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore nell\\'invio del work log'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading && !operators.length) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Caricamento dati...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Assignment, {\n          sx: {\n            fontSize: 40,\n            color: 'primary.main',\n            mr: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          sx: {\n            fontWeight: 700\n          },\n          children: initialData ? 'Modifica Work Log' : 'Nuovo Work Log'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        color: \"text.secondary\",\n        children: \"Registra i dettagli del lavoro svolto per il monitoraggio della produttivit\\xE0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 3,\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Person, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), \"Informazioni Base\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.operator_id,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Operatore *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"operator_id\",\n                  value: formData.operator_id,\n                  onChange: handleInputChange,\n                  label: \"Operatore *\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: \"Seleziona operatore\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this), operators.map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: op.id_responsabile,\n                    children: [op.nome_responsabile, \" (\", op.experience_level || 'Senior', \")\"]\n                  }, op.id_responsabile, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), errors.operator_id && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: errors.operator_id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.id_cantiere,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Cantiere *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"id_cantiere\",\n                  value: formData.id_cantiere,\n                  onChange: handleInputChange,\n                  label: \"Cantiere *\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: \"Seleziona cantiere\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 21\n                  }, this), cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: cantiere.id_cantiere,\n                    children: cantiere.commessa\n                  }, cantiere.id_cantiere, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), errors.id_cantiere && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: errors.id_cantiere\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.activity_type,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Tipo Attivit\\xE0 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"activity_type\",\n                  value: formData.activity_type,\n                  onChange: handleInputChange,\n                  label: \"Tipo Attivit\\xE0 *\",\n                  children: activityTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type.value,\n                    children: type.label\n                  }, type.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), errors.activity_type && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: errors.activity_type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Tipo di Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"cable_type_id\",\n                  value: formData.cable_type_id,\n                  onChange: handleInputChange,\n                  label: \"Tipo di Cavo\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: \"Seleziona tipo di cavo (opzionale)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 21\n                  }, this), cableTypes.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type.id_tipologia,\n                    children: [type.codice_prodotto, \" - \", type.nome_commerciale]\n                  }, type.id_tipologia, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  mt: 2,\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Engineering, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this), \"Dettagli del Lavoro\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                name: \"sub_activity_detail\",\n                label: \"Dettaglio Attivit\\xE0\",\n                value: formData.sub_activity_detail,\n                onChange: handleInputChange,\n                placeholder: \"es. Posa in canalina a vista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                name: \"quantity\",\n                label: `Quantità * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`,\n                value: formData.quantity,\n                onChange: handleInputChange,\n                error: !!errors.quantity,\n                helperText: errors.quantity,\n                inputProps: {\n                  step: 0.1,\n                  min: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Condizioni Ambientali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"environmental_conditions\",\n                  value: formData.environmental_conditions,\n                  onChange: handleInputChange,\n                  label: \"Condizioni Ambientali\",\n                  children: environmentalConditions.map(condition => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: condition.value,\n                    children: condition.label\n                  }, condition.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Strumenti Utilizzati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"tools_used\",\n                  value: formData.tools_used,\n                  onChange: handleInputChange,\n                  label: \"Strumenti Utilizzati\",\n                  children: toolsUsed.map(tool => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: tool.value,\n                    children: tool.label\n                  }, tool.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  mt: 2,\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Schedule, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 19\n                }, this), \"Tempi di Lavoro\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"datetime-local\",\n                name: \"start_timestamp\",\n                label: \"Data/Ora Inizio *\",\n                value: formData.start_timestamp,\n                onChange: handleInputChange,\n                error: !!errors.start_timestamp,\n                helperText: errors.start_timestamp,\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"datetime-local\",\n                name: \"end_timestamp\",\n                label: \"Data/Ora Fine *\",\n                value: formData.end_timestamp,\n                onChange: handleInputChange,\n                error: !!errors.end_timestamp,\n                helperText: errors.end_timestamp,\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                type: \"number\",\n                name: \"number_of_operators_on_task\",\n                label: \"Numero Operatori *\",\n                value: formData.number_of_operators_on_task,\n                onChange: handleInputChange,\n                error: !!errors.number_of_operators_on_task,\n                helperText: errors.number_of_operators_on_task,\n                inputProps: {\n                  min: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                multiline: true,\n                rows: 3,\n                name: \"notes\",\n                label: \"Note\",\n                value: formData.notes,\n                onChange: handleInputChange,\n                placeholder: \"Note aggiuntive sul lavoro svolto...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: 2,\n                  pt: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: onCancel,\n                  startIcon: /*#__PURE__*/_jsxDEV(Cancel, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 32\n                  }, this),\n                  size: \"large\",\n                  children: \"Annulla\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"contained\",\n                  disabled: loading,\n                  startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 42\n                  }, this) : /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 75\n                  }, this),\n                  size: \"large\",\n                  sx: {\n                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n                    '&:hover': {\n                      background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n                    }\n                  },\n                  children: loading ? 'Salvando...' : initialData ? 'Aggiorna Work Log' : 'Crea Work Log'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkLogForm, \"hJ+MEyMZs3QRaBnrGuytC3pSiuU=\");\n_c = WorkLogForm;\nexport default WorkLogForm;\nvar _c;\n$RefreshReg$(_c, \"WorkLogForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Paper", "Chip", "Avatar", "Divider", "<PERSON><PERSON>", "CircularProgress", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Assignment", "Person", "Schedule", "Engineering", "Save", "Cancel", "CheckCircle", "Construction", "useAuth", "axiosInstance", "jsxDEV", "_jsxDEV", "WorkLogForm", "onSubmit", "onCancel", "initialData", "_s", "formData", "setFormData", "operator_id", "cable_type_id", "activity_type", "sub_activity_detail", "environmental_conditions", "tools_used", "quantity", "start_timestamp", "end_timestamp", "number_of_operators_on_task", "notes", "id_cantiere", "operators", "setOperators", "cableTypes", "setCableTypes", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "errors", "setErrors", "activityTypes", "value", "label", "environmentalConditions", "toolsUsed", "loadInitialData", "formatDateTimeLocal", "dateString", "date", "Date", "toISOString", "slice", "operatorsRes", "cableTypesRes", "cantieriRes", "Promise", "all", "get", "data", "length", "prev", "error", "console", "general", "handleInputChange", "e", "name", "type", "target", "parseFloat", "validateForm", "newErrors", "start", "end", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "parseInt", "_error$response", "_error$response$data", "response", "detail", "sx", "p", "textAlign", "children", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "max<PERSON><PERSON><PERSON>", "py", "display", "justifyContent", "alignItems", "fontSize", "mr", "component", "fontWeight", "severity", "elevation", "container", "spacing", "item", "xs", "md", "fullWidth", "onChange", "map", "op", "id_responsabile", "nome_responsabile", "experience_level", "mt", "cantiere", "commessa", "id_tipologia", "codice_prodotto", "nome_commerciale", "placeholder", "helperText", "inputProps", "step", "min", "condition", "tool", "InputLabelProps", "shrink", "multiline", "rows", "gap", "pt", "onClick", "startIcon", "size", "disabled", "background", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/WorkLogForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Button,\n  Paper,\n  Chip,\n  Avatar,\n  Divider,\n  Alert,\n  CircularProgress,\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON>l,\n  StepContent\n} from '@mui/material';\nimport {\n  Assignment,\n  Person,\n  Schedule,\n  Engineering,\n  Save,\n  Cancel,\n  CheckCircle,\n  Construction\n} from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst WorkLogForm = ({ onSubmit, onCancel, initialData = null }) => {\n  const [formData, setFormData] = useState({\n    operator_id: '',\n    cable_type_id: '',\n    activity_type: 'Posa',\n    sub_activity_detail: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    quantity: '',\n    start_timestamp: '',\n    end_timestamp: '',\n    number_of_operators_on_task: 1,\n    notes: '',\n    id_cantiere: ''\n  });\n\n  const [operators, setOperators] = useState([]);\n  const [cableTypes, setCableTypes] = useState([]);\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [\n    { value: 'Posa', label: 'Posa' },\n    { value: 'Collegamento', label: 'Collegamento' },\n    { value: 'Certificazione', label: 'Certificazione' }\n  ];\n\n  const environmentalConditions = [\n    { value: 'Normale', label: 'Normale' },\n    { value: 'Spazi Ristretti', label: 'Spazi Ristretti' },\n    { value: 'In Altezza', label: 'In Altezza' },\n    { value: 'Esterno', label: 'Esterno' }\n  ];\n\n  const toolsUsed = [\n    { value: 'Manuale', label: 'Manuale' },\n    { value: 'Automatico', label: 'Automatico' }\n  ];\n\n  useEffect(() => {\n    loadInitialData();\n    if (initialData) {\n      setFormData({\n        ...initialData,\n        start_timestamp: formatDateTimeLocal(initialData.start_timestamp),\n        end_timestamp: formatDateTimeLocal(initialData.end_timestamp)\n      });\n    }\n  }, [initialData]);\n\n  const formatDateTimeLocal = (dateString) => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toISOString().slice(0, 16);\n  };\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      \n      // Carica operatori, tipi di cavo e cantieri in parallelo\n      const [operatorsRes, cableTypesRes, cantieriRes] = await Promise.all([\n        axiosInstance.get('/responsabili'),\n        axiosInstance.get('/admin/tipologie-cavi'),\n        axiosInstance.get('/cantieri')\n      ]);\n\n      setOperators(operatorsRes.data || []);\n      setCableTypes(cableTypesRes.data || []);\n      setCantieri(cantieriRes.data || []);\n\n      // Se c'è solo un cantiere, selezionalo automaticamente\n      if (cantieriRes.data && cantieriRes.data.length === 1) {\n        setFormData(prev => ({ ...prev, id_cantiere: cantieriRes.data[0].id_cantiere }));\n      }\n\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n      setErrors({ general: 'Errore nel caricamento dei dati' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n    \n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: null }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.operator_id) newErrors.operator_id = 'Operatore richiesto';\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Quantità deve essere maggiore di 0';\n    if (!formData.start_timestamp) newErrors.start_timestamp = 'Data/ora inizio richiesta';\n    if (!formData.end_timestamp) newErrors.end_timestamp = 'Data/ora fine richiesta';\n    if (!formData.id_cantiere) newErrors.id_cantiere = 'Cantiere richiesto';\n    if (!formData.number_of_operators_on_task || formData.number_of_operators_on_task < 1) {\n      newErrors.number_of_operators_on_task = 'Numero operatori deve essere almeno 1';\n    }\n\n    // Valida che end_timestamp sia dopo start_timestamp\n    if (formData.start_timestamp && formData.end_timestamp) {\n      const start = new Date(formData.start_timestamp);\n      const end = new Date(formData.end_timestamp);\n      if (end <= start) {\n        newErrors.end_timestamp = 'Data/ora fine deve essere dopo inizio';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      // Prepara i dati per l'invio\n      const submitData = {\n        ...formData,\n        operator_id: parseInt(formData.operator_id),\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        quantity: parseFloat(formData.quantity),\n        number_of_operators_on_task: parseInt(formData.number_of_operators_on_task),\n        id_cantiere: parseInt(formData.id_cantiere),\n        start_timestamp: new Date(formData.start_timestamp).toISOString(),\n        end_timestamp: new Date(formData.end_timestamp).toISOString()\n      };\n\n      await onSubmit(submitData);\n      \n    } catch (error) {\n      console.error('Errore nell\\'invio del form:', error);\n      setErrors({ \n        general: error.response?.data?.detail || 'Errore nell\\'invio del work log' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading && !operators.length) {\n    return (\n      <Box sx={{ p: 4, textAlign: 'center' }}>\n        <CircularProgress sx={{ mb: 2 }} />\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Caricamento dati...\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 4, textAlign: 'center' }}>\n        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>\n          <Assignment sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />\n          <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700 }}>\n            {initialData ? 'Modifica Work Log' : 'Nuovo Work Log'}\n          </Typography>\n        </Box>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\">\n          Registra i dettagli del lavoro svolto per il monitoraggio della produttività\n        </Typography>\n      </Box>\n\n      {errors.general && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {errors.general}\n        </Alert>\n      )}\n\n      <Card elevation={3}>\n        <CardContent sx={{ p: 4 }}>\n          <Box component=\"form\" onSubmit={handleSubmit}>\n            <Grid container spacing={3}>\n              {/* Sezione Informazioni Base */}\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>\n                  <Person sx={{ mr: 1, color: 'primary.main' }} />\n                  Informazioni Base\n                </Typography>\n                <Divider sx={{ mb: 3 }} />\n              </Grid>\n\n              {/* Operatore */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.operator_id}>\n                  <InputLabel>Operatore *</InputLabel>\n                  <Select\n                    name=\"operator_id\"\n                    value={formData.operator_id}\n                    onChange={handleInputChange}\n                    label=\"Operatore *\"\n                  >\n                    <MenuItem value=\"\">Seleziona operatore</MenuItem>\n                    {operators.map(op => (\n                      <MenuItem key={op.id_responsabile} value={op.id_responsabile}>\n                        {op.nome_responsabile} ({op.experience_level || 'Senior'})\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.operator_id && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1 }}>\n                      {errors.operator_id}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Cantiere */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.id_cantiere}>\n                  <InputLabel>Cantiere *</InputLabel>\n                  <Select\n                    name=\"id_cantiere\"\n                    value={formData.id_cantiere}\n                    onChange={handleInputChange}\n                    label=\"Cantiere *\"\n                  >\n                    <MenuItem value=\"\">Seleziona cantiere</MenuItem>\n                    {cantieri.map(cantiere => (\n                      <MenuItem key={cantiere.id_cantiere} value={cantiere.id_cantiere}>\n                        {cantiere.commessa}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.id_cantiere && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1 }}>\n                      {errors.id_cantiere}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Tipo di Attività */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.activity_type}>\n                  <InputLabel>Tipo Attività *</InputLabel>\n                  <Select\n                    name=\"activity_type\"\n                    value={formData.activity_type}\n                    onChange={handleInputChange}\n                    label=\"Tipo Attività *\"\n                  >\n                    {activityTypes.map(type => (\n                      <MenuItem key={type.value} value={type.value}>\n                        {type.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.activity_type && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1 }}>\n                      {errors.activity_type}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Tipo di Cavo */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Tipo di Cavo</InputLabel>\n                  <Select\n                    name=\"cable_type_id\"\n                    value={formData.cable_type_id}\n                    onChange={handleInputChange}\n                    label=\"Tipo di Cavo\"\n                  >\n                    <MenuItem value=\"\">Seleziona tipo di cavo (opzionale)</MenuItem>\n                    {cableTypes.map(type => (\n                      <MenuItem key={type.id_tipologia} value={type.id_tipologia}>\n                        {type.codice_prodotto} - {type.nome_commerciale}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              {/* Sezione Dettagli Lavoro */}\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, mt: 2, display: 'flex', alignItems: 'center' }}>\n                  <Engineering sx={{ mr: 1, color: 'primary.main' }} />\n                  Dettagli del Lavoro\n                </Typography>\n                <Divider sx={{ mb: 3 }} />\n              </Grid>\n\n              {/* Dettaglio Sub-Attività */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  name=\"sub_activity_detail\"\n                  label=\"Dettaglio Attività\"\n                  value={formData.sub_activity_detail}\n                  onChange={handleInputChange}\n                  placeholder=\"es. Posa in canalina a vista\"\n                />\n              </Grid>\n\n              {/* Quantità */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"number\"\n                  name=\"quantity\"\n                  label={`Quantità * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`}\n                  value={formData.quantity}\n                  onChange={handleInputChange}\n                  error={!!errors.quantity}\n                  helperText={errors.quantity}\n                  inputProps={{ step: 0.1, min: 0 }}\n                />\n              </Grid>\n\n              {/* Condizioni Ambientali */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Condizioni Ambientali</InputLabel>\n                  <Select\n                    name=\"environmental_conditions\"\n                    value={formData.environmental_conditions}\n                    onChange={handleInputChange}\n                    label=\"Condizioni Ambientali\"\n                  >\n                    {environmentalConditions.map(condition => (\n                      <MenuItem key={condition.value} value={condition.value}>\n                        {condition.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              {/* Strumenti Utilizzati */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Strumenti Utilizzati</InputLabel>\n                  <Select\n                    name=\"tools_used\"\n                    value={formData.tools_used}\n                    onChange={handleInputChange}\n                    label=\"Strumenti Utilizzati\"\n                  >\n                    {toolsUsed.map(tool => (\n                      <MenuItem key={tool.value} value={tool.value}>\n                        {tool.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              {/* Sezione Tempi */}\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, mt: 2, display: 'flex', alignItems: 'center' }}>\n                  <Schedule sx={{ mr: 1, color: 'primary.main' }} />\n                  Tempi di Lavoro\n                </Typography>\n                <Divider sx={{ mb: 3 }} />\n              </Grid>\n\n              {/* Data/Ora Inizio */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"datetime-local\"\n                  name=\"start_timestamp\"\n                  label=\"Data/Ora Inizio *\"\n                  value={formData.start_timestamp}\n                  onChange={handleInputChange}\n                  error={!!errors.start_timestamp}\n                  helperText={errors.start_timestamp}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              {/* Data/Ora Fine */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"datetime-local\"\n                  name=\"end_timestamp\"\n                  label=\"Data/Ora Fine *\"\n                  value={formData.end_timestamp}\n                  onChange={handleInputChange}\n                  error={!!errors.end_timestamp}\n                  helperText={errors.end_timestamp}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              {/* Numero Operatori */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  type=\"number\"\n                  name=\"number_of_operators_on_task\"\n                  label=\"Numero Operatori *\"\n                  value={formData.number_of_operators_on_task}\n                  onChange={handleInputChange}\n                  error={!!errors.number_of_operators_on_task}\n                  helperText={errors.number_of_operators_on_task}\n                  inputProps={{ min: 1 }}\n                />\n              </Grid>\n\n              {/* Note */}\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  multiline\n                  rows={3}\n                  name=\"notes\"\n                  label=\"Note\"\n                  value={formData.notes}\n                  onChange={handleInputChange}\n                  placeholder=\"Note aggiuntive sul lavoro svolto...\"\n                />\n              </Grid>\n\n              {/* Pulsanti */}\n              <Grid item xs={12}>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, pt: 3 }}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={onCancel}\n                    startIcon={<Cancel />}\n                    size=\"large\"\n                  >\n                    Annulla\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"contained\"\n                    disabled={loading}\n                    startIcon={loading ? <CircularProgress size={20} /> : <Save />}\n                    size=\"large\"\n                    sx={{\n                      background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n                      '&:hover': {\n                        background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n                      }\n                    }}\n                  >\n                    {loading ? 'Salvando...' : (initialData ? 'Aggiorna Work Log' : 'Crea Work Log')}\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </Box>\n        </CardContent>\n      </Card>\n    </Container>\n  );\n};\n\nexport default WorkLogForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,WAAW,QACN,eAAe;AACtB,SACEC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,YAAY,QACP,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,MAAM;IACrBC,mBAAmB,EAAE,EAAE;IACvBC,wBAAwB,EAAE,SAAS;IACnCC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE,EAAE;IACjBC,2BAA2B,EAAE,CAAC;IAC9BC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMkE,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,EAChD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACrD;EAED,MAAMC,uBAAuB,GAAG,CAC9B;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACvC;EAED,MAAME,SAAS,GAAG,CAChB;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,CAC7C;EAEDnE,SAAS,CAAC,MAAM;IACdsE,eAAe,CAAC,CAAC;IACjB,IAAI/B,WAAW,EAAE;MACfG,WAAW,CAAC;QACV,GAAGH,WAAW;QACdW,eAAe,EAAEqB,mBAAmB,CAAChC,WAAW,CAACW,eAAe,CAAC;QACjEC,aAAa,EAAEoB,mBAAmB,CAAChC,WAAW,CAACY,aAAa;MAC9D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;EAEjB,MAAMgC,mBAAmB,GAAIC,UAAU,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;EAED,MAAMN,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACe,YAAY,EAAEC,aAAa,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnEhD,aAAa,CAACiD,GAAG,CAAC,eAAe,CAAC,EAClCjD,aAAa,CAACiD,GAAG,CAAC,uBAAuB,CAAC,EAC1CjD,aAAa,CAACiD,GAAG,CAAC,WAAW,CAAC,CAC/B,CAAC;MAEF1B,YAAY,CAACqB,YAAY,CAACM,IAAI,IAAI,EAAE,CAAC;MACrCzB,aAAa,CAACoB,aAAa,CAACK,IAAI,IAAI,EAAE,CAAC;MACvCvB,WAAW,CAACmB,WAAW,CAACI,IAAI,IAAI,EAAE,CAAC;;MAEnC;MACA,IAAIJ,WAAW,CAACI,IAAI,IAAIJ,WAAW,CAACI,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;QACrD1C,WAAW,CAAC2C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE/B,WAAW,EAAEyB,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC7B;QAAY,CAAC,CAAC,CAAC;MAClF;IAEF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DtB,SAAS,CAAC;QAAEwB,OAAO,EAAE;MAAkC,CAAC,CAAC;IAC3D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEzB,KAAK;MAAE0B;IAAK,CAAC,GAAGF,CAAC,CAACG,MAAM;IACtCnD,WAAW,CAAC2C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACM,IAAI,GAAGC,IAAI,KAAK,QAAQ,GAAGE,UAAU,CAAC5B,KAAK,CAAC,IAAI,EAAE,GAAGA;IACxD,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIH,MAAM,CAAC4B,IAAI,CAAC,EAAE;MAChB3B,SAAS,CAACqB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACM,IAAI,GAAG;MAAK,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACvD,QAAQ,CAACE,WAAW,EAAEqD,SAAS,CAACrD,WAAW,GAAG,qBAAqB;IACxE,IAAI,CAACF,QAAQ,CAACI,aAAa,EAAEmD,SAAS,CAACnD,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACJ,QAAQ,CAACQ,QAAQ,IAAIR,QAAQ,CAACQ,QAAQ,IAAI,CAAC,EAAE+C,SAAS,CAAC/C,QAAQ,GAAG,oCAAoC;IAC3G,IAAI,CAACR,QAAQ,CAACS,eAAe,EAAE8C,SAAS,CAAC9C,eAAe,GAAG,2BAA2B;IACtF,IAAI,CAACT,QAAQ,CAACU,aAAa,EAAE6C,SAAS,CAAC7C,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACV,QAAQ,CAACa,WAAW,EAAE0C,SAAS,CAAC1C,WAAW,GAAG,oBAAoB;IACvE,IAAI,CAACb,QAAQ,CAACW,2BAA2B,IAAIX,QAAQ,CAACW,2BAA2B,GAAG,CAAC,EAAE;MACrF4C,SAAS,CAAC5C,2BAA2B,GAAG,uCAAuC;IACjF;;IAEA;IACA,IAAIX,QAAQ,CAACS,eAAe,IAAIT,QAAQ,CAACU,aAAa,EAAE;MACtD,MAAM8C,KAAK,GAAG,IAAIvB,IAAI,CAACjC,QAAQ,CAACS,eAAe,CAAC;MAChD,MAAMgD,GAAG,GAAG,IAAIxB,IAAI,CAACjC,QAAQ,CAACU,aAAa,CAAC;MAC5C,IAAI+C,GAAG,IAAID,KAAK,EAAE;QAChBD,SAAS,CAAC7C,aAAa,GAAG,uCAAuC;MACnE;IACF;IAEAa,SAAS,CAACgC,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACZ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACFjC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMyC,UAAU,GAAG;QACjB,GAAG9D,QAAQ;QACXE,WAAW,EAAE6D,QAAQ,CAAC/D,QAAQ,CAACE,WAAW,CAAC;QAC3CC,aAAa,EAAEH,QAAQ,CAACG,aAAa,GAAG4D,QAAQ,CAAC/D,QAAQ,CAACG,aAAa,CAAC,GAAG,IAAI;QAC/EK,QAAQ,EAAE6C,UAAU,CAACrD,QAAQ,CAACQ,QAAQ,CAAC;QACvCG,2BAA2B,EAAEoD,QAAQ,CAAC/D,QAAQ,CAACW,2BAA2B,CAAC;QAC3EE,WAAW,EAAEkD,QAAQ,CAAC/D,QAAQ,CAACa,WAAW,CAAC;QAC3CJ,eAAe,EAAE,IAAIwB,IAAI,CAACjC,QAAQ,CAACS,eAAe,CAAC,CAACyB,WAAW,CAAC,CAAC;QACjExB,aAAa,EAAE,IAAIuB,IAAI,CAACjC,QAAQ,CAACU,aAAa,CAAC,CAACwB,WAAW,CAAC;MAC9D,CAAC;MAED,MAAMtC,QAAQ,CAACkE,UAAU,CAAC;IAE5B,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdnB,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDtB,SAAS,CAAC;QACRwB,OAAO,EAAE,EAAAiB,eAAA,GAAAnB,KAAK,CAACqB,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBtB,IAAI,cAAAuB,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI;MAC3C,CAAC,CAAC;IACJ,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,IAAI,CAACN,SAAS,CAAC6B,MAAM,EAAE;IAChC,oBACEjD,OAAA,CAAClC,GAAG;MAAC4G,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrC7E,OAAA,CAAChB,gBAAgB;QAAC0F,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnClF,OAAA,CAAChC,UAAU;QAACmH,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEhD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACElF,OAAA,CAACjC,SAAS;IAACsH,QAAQ,EAAC,IAAI;IAACX,EAAE,EAAE;MAAEY,EAAE,EAAE;IAAE,CAAE;IAAAT,QAAA,gBAErC7E,OAAA,CAAClC,GAAG;MAAC4G,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAEF,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtC7E,OAAA,CAAClC,GAAG;QAAC4G,EAAE,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEX,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBAClF7E,OAAA,CAACX,UAAU;UAACqF,EAAE,EAAE;YAAEgB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,cAAc;YAAEO,EAAE,EAAE;UAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClElF,OAAA,CAAChC,UAAU;UAACmH,OAAO,EAAC,IAAI;UAACS,SAAS,EAAC,IAAI;UAAClB,EAAE,EAAE;YAAEmB,UAAU,EAAE;UAAI,CAAE;UAAAhB,QAAA,EAC7DzE,WAAW,GAAG,mBAAmB,GAAG;QAAgB;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNlF,OAAA,CAAChC,UAAU;QAACmH,OAAO,EAAC,WAAW;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEvD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELtD,MAAM,CAACyB,OAAO,iBACbrD,OAAA,CAACjB,KAAK;MAAC+G,QAAQ,EAAC,OAAO;MAACpB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,EACnCjD,MAAM,CAACyB;IAAO;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACR,eAEDlF,OAAA,CAAC9B,IAAI;MAAC6H,SAAS,EAAE,CAAE;MAAAlB,QAAA,eACjB7E,OAAA,CAAC7B,WAAW;QAACuG,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,eACxB7E,OAAA,CAAClC,GAAG;UAAC8H,SAAS,EAAC,MAAM;UAAC1F,QAAQ,EAAEgE,YAAa;UAAAW,QAAA,eAC3C7E,OAAA,CAAC/B,IAAI;YAAC+H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAApB,QAAA,gBAEzB7E,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,gBAChB7E,OAAA,CAAChC,UAAU;gBAACmH,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEI,EAAE,EAAE,CAAC;kBAAES,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBAC5E7E,OAAA,CAACV,MAAM;kBAACoF,EAAE,EAAE;oBAAEiB,EAAE,EAAE,CAAC;oBAAEP,KAAK,EAAE;kBAAe;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAElD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA,CAAClB,OAAO;gBAAC4F,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAACxB,WAAW;gBAAC6H,SAAS;gBAAClD,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACpB,WAAY;gBAAAqE,QAAA,gBACjD7E,OAAA,CAACvB,UAAU;kBAAAoG,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpClF,OAAA,CAAC1B,MAAM;kBACLkF,IAAI,EAAC,aAAa;kBAClBzB,KAAK,EAAEzB,QAAQ,CAACE,WAAY;kBAC5B8F,QAAQ,EAAEhD,iBAAkB;kBAC5BtB,KAAK,EAAC,aAAa;kBAAA6C,QAAA,gBAEnB7E,OAAA,CAACzB,QAAQ;oBAACwD,KAAK,EAAC,EAAE;oBAAA8C,QAAA,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAChD9D,SAAS,CAACmF,GAAG,CAACC,EAAE,iBACfxG,OAAA,CAACzB,QAAQ;oBAA0BwD,KAAK,EAAEyE,EAAE,CAACC,eAAgB;oBAAA5B,QAAA,GAC1D2B,EAAE,CAACE,iBAAiB,EAAC,IAAE,EAACF,EAAE,CAACG,gBAAgB,IAAI,QAAQ,EAAC,GAC3D;kBAAA,GAFeH,EAAE,CAACC,eAAe;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEvB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRtD,MAAM,CAACpB,WAAW,iBACjBR,OAAA,CAAChC,UAAU;kBAACmH,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,OAAO;kBAACV,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAA/B,QAAA,EACvDjD,MAAM,CAACpB;gBAAW;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAACxB,WAAW;gBAAC6H,SAAS;gBAAClD,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACT,WAAY;gBAAA0D,QAAA,gBACjD7E,OAAA,CAACvB,UAAU;kBAAAoG,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnClF,OAAA,CAAC1B,MAAM;kBACLkF,IAAI,EAAC,aAAa;kBAClBzB,KAAK,EAAEzB,QAAQ,CAACa,WAAY;kBAC5BmF,QAAQ,EAAEhD,iBAAkB;kBAC5BtB,KAAK,EAAC,YAAY;kBAAA6C,QAAA,gBAElB7E,OAAA,CAACzB,QAAQ;oBAACwD,KAAK,EAAC,EAAE;oBAAA8C,QAAA,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAC/C1D,QAAQ,CAAC+E,GAAG,CAACM,QAAQ,iBACpB7G,OAAA,CAACzB,QAAQ;oBAA4BwD,KAAK,EAAE8E,QAAQ,CAAC1F,WAAY;oBAAA0D,QAAA,EAC9DgC,QAAQ,CAACC;kBAAQ,GADLD,QAAQ,CAAC1F,WAAW;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRtD,MAAM,CAACT,WAAW,iBACjBnB,OAAA,CAAChC,UAAU;kBAACmH,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,OAAO;kBAACV,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAA/B,QAAA,EACvDjD,MAAM,CAACT;gBAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAACxB,WAAW;gBAAC6H,SAAS;gBAAClD,KAAK,EAAE,CAAC,CAACvB,MAAM,CAAClB,aAAc;gBAAAmE,QAAA,gBACnD7E,OAAA,CAACvB,UAAU;kBAAAoG,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxClF,OAAA,CAAC1B,MAAM;kBACLkF,IAAI,EAAC,eAAe;kBACpBzB,KAAK,EAAEzB,QAAQ,CAACI,aAAc;kBAC9B4F,QAAQ,EAAEhD,iBAAkB;kBAC5BtB,KAAK,EAAC,oBAAiB;kBAAA6C,QAAA,EAEtB/C,aAAa,CAACyE,GAAG,CAAC9C,IAAI,iBACrBzD,OAAA,CAACzB,QAAQ;oBAAkBwD,KAAK,EAAE0B,IAAI,CAAC1B,KAAM;oBAAA8C,QAAA,EAC1CpB,IAAI,CAACzB;kBAAK,GADEyB,IAAI,CAAC1B,KAAK;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRtD,MAAM,CAAClB,aAAa,iBACnBV,OAAA,CAAChC,UAAU;kBAACmH,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,OAAO;kBAACV,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAA/B,QAAA,EACvDjD,MAAM,CAAClB;gBAAa;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAACxB,WAAW;gBAAC6H,SAAS;gBAAAxB,QAAA,gBACpB7E,OAAA,CAACvB,UAAU;kBAAAoG,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrClF,OAAA,CAAC1B,MAAM;kBACLkF,IAAI,EAAC,eAAe;kBACpBzB,KAAK,EAAEzB,QAAQ,CAACG,aAAc;kBAC9B6F,QAAQ,EAAEhD,iBAAkB;kBAC5BtB,KAAK,EAAC,cAAc;kBAAA6C,QAAA,gBAEpB7E,OAAA,CAACzB,QAAQ;oBAACwD,KAAK,EAAC,EAAE;oBAAA8C,QAAA,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EAC/D5D,UAAU,CAACiF,GAAG,CAAC9C,IAAI,iBAClBzD,OAAA,CAACzB,QAAQ;oBAAyBwD,KAAK,EAAE0B,IAAI,CAACsD,YAAa;oBAAAlC,QAAA,GACxDpB,IAAI,CAACuD,eAAe,EAAC,KAAG,EAACvD,IAAI,CAACwD,gBAAgB;kBAAA,GADlCxD,IAAI,CAACsD,YAAY;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEtB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,gBAChB7E,OAAA,CAAChC,UAAU;gBAACmH,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEI,EAAE,EAAE,CAAC;kBAAE8B,EAAE,EAAE,CAAC;kBAAErB,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBACnF7E,OAAA,CAACR,WAAW;kBAACkF,EAAE,EAAE;oBAAEiB,EAAE,EAAE,CAAC;oBAAEP,KAAK,EAAE;kBAAe;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAEvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA,CAAClB,OAAO;gBAAC4F,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAAC3B,SAAS;gBACRgI,SAAS;gBACT7C,IAAI,EAAC,qBAAqB;gBAC1BxB,KAAK,EAAC,uBAAoB;gBAC1BD,KAAK,EAAEzB,QAAQ,CAACK,mBAAoB;gBACpC2F,QAAQ,EAAEhD,iBAAkB;gBAC5B4D,WAAW,EAAC;cAA8B;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAAC3B,SAAS;gBACRgI,SAAS;gBACT5C,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,UAAU;gBACfxB,KAAK,EAAE,cAAc1B,QAAQ,CAACI,aAAa,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,EAAG;gBACjFqB,KAAK,EAAEzB,QAAQ,CAACQ,QAAS;gBACzBwF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACd,QAAS;gBACzBqG,UAAU,EAAEvF,MAAM,CAACd,QAAS;gBAC5BsG,UAAU,EAAE;kBAAEC,IAAI,EAAE,GAAG;kBAAEC,GAAG,EAAE;gBAAE;cAAE;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAACxB,WAAW;gBAAC6H,SAAS;gBAAAxB,QAAA,gBACpB7E,OAAA,CAACvB,UAAU;kBAAAoG,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9ClF,OAAA,CAAC1B,MAAM;kBACLkF,IAAI,EAAC,0BAA0B;kBAC/BzB,KAAK,EAAEzB,QAAQ,CAACM,wBAAyB;kBACzC0F,QAAQ,EAAEhD,iBAAkB;kBAC5BtB,KAAK,EAAC,uBAAuB;kBAAA6C,QAAA,EAE5B5C,uBAAuB,CAACsE,GAAG,CAACgB,SAAS,iBACpCvH,OAAA,CAACzB,QAAQ;oBAAuBwD,KAAK,EAAEwF,SAAS,CAACxF,KAAM;oBAAA8C,QAAA,EACpD0C,SAAS,CAACvF;kBAAK,GADHuF,SAAS,CAACxF,KAAK;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAACxB,WAAW;gBAAC6H,SAAS;gBAAAxB,QAAA,gBACpB7E,OAAA,CAACvB,UAAU;kBAAAoG,QAAA,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7ClF,OAAA,CAAC1B,MAAM;kBACLkF,IAAI,EAAC,YAAY;kBACjBzB,KAAK,EAAEzB,QAAQ,CAACO,UAAW;kBAC3ByF,QAAQ,EAAEhD,iBAAkB;kBAC5BtB,KAAK,EAAC,sBAAsB;kBAAA6C,QAAA,EAE3B3C,SAAS,CAACqE,GAAG,CAACiB,IAAI,iBACjBxH,OAAA,CAACzB,QAAQ;oBAAkBwD,KAAK,EAAEyF,IAAI,CAACzF,KAAM;oBAAA8C,QAAA,EAC1C2C,IAAI,CAACxF;kBAAK,GADEwF,IAAI,CAACzF,KAAK;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,gBAChB7E,OAAA,CAAChC,UAAU;gBAACmH,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEI,EAAE,EAAE,CAAC;kBAAE8B,EAAE,EAAE,CAAC;kBAAErB,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBACnF7E,OAAA,CAACT,QAAQ;kBAACmF,EAAE,EAAE;oBAAEiB,EAAE,EAAE,CAAC;oBAAEP,KAAK,EAAE;kBAAe;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA,CAAClB,OAAO;gBAAC4F,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAAC3B,SAAS;gBACRgI,SAAS;gBACT5C,IAAI,EAAC,gBAAgB;gBACrBD,IAAI,EAAC,iBAAiB;gBACtBxB,KAAK,EAAC,mBAAmB;gBACzBD,KAAK,EAAEzB,QAAQ,CAACS,eAAgB;gBAChCuF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACb,eAAgB;gBAChCoG,UAAU,EAAEvF,MAAM,CAACb,eAAgB;gBACnC0G,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAAC3B,SAAS;gBACRgI,SAAS;gBACT5C,IAAI,EAAC,gBAAgB;gBACrBD,IAAI,EAAC,eAAe;gBACpBxB,KAAK,EAAC,iBAAiB;gBACvBD,KAAK,EAAEzB,QAAQ,CAACU,aAAc;gBAC9BsF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACZ,aAAc;gBAC9BmG,UAAU,EAAEvF,MAAM,CAACZ,aAAc;gBACjCyG,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvB7E,OAAA,CAAC3B,SAAS;gBACRgI,SAAS;gBACT5C,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,6BAA6B;gBAClCxB,KAAK,EAAC,oBAAoB;gBAC1BD,KAAK,EAAEzB,QAAQ,CAACW,2BAA4B;gBAC5CqF,QAAQ,EAAEhD,iBAAkB;gBAC5BH,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACX,2BAA4B;gBAC5CkG,UAAU,EAAEvF,MAAM,CAACX,2BAA4B;gBAC/CmG,UAAU,EAAE;kBAAEE,GAAG,EAAE;gBAAE;cAAE;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,eAChB7E,OAAA,CAAC3B,SAAS;gBACRgI,SAAS;gBACTsB,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRpE,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,MAAM;gBACZD,KAAK,EAAEzB,QAAQ,CAACY,KAAM;gBACtBoF,QAAQ,EAAEhD,iBAAkB;gBAC5B4D,WAAW,EAAC;cAAsC;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPlF,OAAA,CAAC/B,IAAI;cAACiI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,eAChB7E,OAAA,CAAClC,GAAG;gBAAC4G,EAAE,EAAE;kBAAEa,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,UAAU;kBAAEqC,GAAG,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,gBACtE7E,OAAA,CAACtB,MAAM;kBACLyG,OAAO,EAAC,UAAU;kBAClB4C,OAAO,EAAE5H,QAAS;kBAClB6H,SAAS,eAAEhI,OAAA,CAACN,MAAM;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtB+C,IAAI,EAAC,OAAO;kBAAApD,QAAA,EACb;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlF,OAAA,CAACtB,MAAM;kBACL+E,IAAI,EAAC,QAAQ;kBACb0B,OAAO,EAAC,WAAW;kBACnB+C,QAAQ,EAAExG,OAAQ;kBAClBsG,SAAS,EAAEtG,OAAO,gBAAG1B,OAAA,CAAChB,gBAAgB;oBAACiJ,IAAI,EAAE;kBAAG;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGlF,OAAA,CAACP,IAAI;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC/D+C,IAAI,EAAC,OAAO;kBACZvD,EAAE,EAAE;oBACFyD,UAAU,EAAE,kDAAkD;oBAC9D,SAAS,EAAE;sBACTA,UAAU,EAAE;oBACd;kBACF,CAAE;kBAAAtD,QAAA,EAEDnD,OAAO,GAAG,aAAa,GAAItB,WAAW,GAAG,mBAAmB,GAAG;gBAAgB;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAAC7E,EAAA,CA1dIJ,WAAW;AAAAmI,EAAA,GAAXnI,WAAW;AA4djB,eAAeA,WAAW;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}