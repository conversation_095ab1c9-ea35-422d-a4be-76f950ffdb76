{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, Select, MenuItem, FormControl, InputLabel, TextField, Chip, Avatar, LinearProgress, Divider, Paper, IconButton, Tooltip as MuiTooltip, Alert } from '@mui/material';\nimport { TrendingUp, Schedule, Engineering, Speed, Assessment, FilterList, Refresh, Download, Construction } from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\n// Registra i componenti Chart.js\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement);\nconst Dashboard = () => {\n  _s();\n  var _statistics$totalQuan, _statistics$totalManH, _statistics$averagePr;\n  const {\n    selectedCantiere: authSelectedCantiere\n  } = useAuth();\n  const [workLogs, setWorkLogs] = useState([]);\n  const [statistics, setStatistics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [dateRange, setDateRange] = useState({\n    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    // 30 giorni fa\n    end: new Date().toISOString().split('T')[0] // oggi\n  });\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.id_cantiere) || parseInt(localStorage.getItem('selectedCantiereId'), 10) || null;\n  const currentCantiereName = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.commessa) || localStorage.getItem('selectedCantiereName') || 'Cantiere non selezionato';\n  useEffect(() => {\n    if (currentCantiereId) {\n      loadDashboardData();\n    }\n  }, [currentCantiereId, dateRange]);\n\n  // Carica i dati solo se c'è un cantiere attivo\n  useEffect(() => {\n    if (!currentCantiereId) {\n      setLoading(false);\n    }\n  }, [currentCantiereId]);\n  const loadDashboardData = async () => {\n    if (!currentCantiereId) {\n      console.warn('Nessun cantiere attivo selezionato per la produttività');\n      setLoading(false);\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Parametri per le chiamate API - usa solo il cantiere attivo\n      const params = {\n        id_cantiere: currentCantiereId,\n        start_date: dateRange.start,\n        end_date: dateRange.end,\n        per_page: 100\n      };\n      console.log('Caricamento dati produttività per cantiere:', currentCantiereId, currentCantiereName);\n\n      // Carica work logs per il cantiere attivo\n      const workLogsRes = await axiosInstance.get('/v1/work-logs', {\n        params\n      });\n      setWorkLogs(workLogsRes.data.work_logs || []);\n\n      // Calcola statistiche\n      calculateStatistics(workLogsRes.data.work_logs || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati dashboard:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const calculateStatistics = logs => {\n    const stats = {\n      totalLogs: logs.length,\n      totalQuantity: 0,\n      totalManHours: 0,\n      averageProductivity: 0,\n      byActivity: {},\n      byOperator: {},\n      byConditions: {},\n      dailyTrend: {},\n      productivityTrend: []\n    };\n    logs.forEach(log => {\n      // Totali\n      stats.totalQuantity += log.quantity || 0;\n      stats.totalManHours += log.total_man_hours || 0;\n\n      // Per attività\n      if (!stats.byActivity[log.activity_type]) {\n        stats.byActivity[log.activity_type] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byActivity[log.activity_type].count++;\n      stats.byActivity[log.activity_type].quantity += log.quantity || 0;\n      stats.byActivity[log.activity_type].manHours += log.total_man_hours || 0;\n\n      // Per operatore\n      const operatorKey = `${log.operator_id}`;\n      if (!stats.byOperator[operatorKey]) {\n        stats.byOperator[operatorKey] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byOperator[operatorKey].count++;\n      stats.byOperator[operatorKey].quantity += log.quantity || 0;\n      stats.byOperator[operatorKey].manHours += log.total_man_hours || 0;\n\n      // Per condizioni ambientali\n      if (!stats.byConditions[log.environmental_conditions]) {\n        stats.byConditions[log.environmental_conditions] = {\n          count: 0,\n          quantity: 0,\n          productivity: 0\n        };\n      }\n      stats.byConditions[log.environmental_conditions].count++;\n      stats.byConditions[log.environmental_conditions].quantity += log.quantity || 0;\n\n      // Trend giornaliero\n      const date = new Date(log.start_timestamp).toISOString().split('T')[0];\n      if (!stats.dailyTrend[date]) {\n        stats.dailyTrend[date] = {\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.dailyTrend[date].quantity += log.quantity || 0;\n      stats.dailyTrend[date].manHours += log.total_man_hours || 0;\n    });\n\n    // Calcola produttività medie\n    stats.averageProductivity = stats.totalManHours > 0 ? stats.totalQuantity / stats.totalManHours : 0;\n    Object.keys(stats.byActivity).forEach(activity => {\n      const data = stats.byActivity[activity];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.byOperator).forEach(operator => {\n      const data = stats.byOperator[operator];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.byConditions).forEach(condition => {\n      const data = stats.byConditions[condition];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.dailyTrend).forEach(date => {\n      const data = stats.dailyTrend[date];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    setStatistics(stats);\n  };\n\n  // Configurazioni grafici\n  const productivityByActivityChart = {\n    labels: Object.keys(statistics.byActivity || {}),\n    datasets: [{\n      label: 'Produttività (unità/ora)',\n      data: Object.values(statistics.byActivity || {}).map(d => d.productivity.toFixed(2)),\n      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n      borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],\n      borderWidth: 1\n    }]\n  };\n  const dailyTrendChart = {\n    labels: Object.keys(statistics.dailyTrend || {}).sort(),\n    datasets: [{\n      label: 'Quantità Giornaliera',\n      data: Object.keys(statistics.dailyTrend || {}).sort().map(date => statistics.dailyTrend[date].quantity),\n      borderColor: '#3B82F6',\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      tension: 0.1\n    }, {\n      label: 'Produttività Giornaliera',\n      data: Object.keys(statistics.dailyTrend || {}).sort().map(date => statistics.dailyTrend[date].productivity),\n      borderColor: '#10B981',\n      backgroundColor: 'rgba(16, 185, 129, 0.1)',\n      tension: 0.1,\n      yAxisID: 'y1'\n    }]\n  };\n  const conditionsChart = {\n    labels: Object.keys(statistics.byConditions || {}),\n    datasets: [{\n      data: Object.values(statistics.byConditions || {}).map(d => d.count),\n      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n      borderWidth: 2\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true\n      },\n      y1: {\n        type: 'linear',\n        display: true,\n        position: 'right',\n        grid: {\n          drawOnChartArea: false\n        }\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Caricamento dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Se non c'è un cantiere attivo, mostra un messaggio\n  if (!currentCantiereId) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"Nessun cantiere attivo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Per visualizzare i dati di produttivit\\xE0, seleziona prima un cantiere dalla dashboard principale.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Assessment, {\n            sx: {\n              fontSize: 32,\n              color: 'primary.main',\n              mr: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 700\n              },\n              children: \"Dashboard Produttivit\\xE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Construction, {\n                sx: {\n                  fontSize: 20,\n                  color: 'text.secondary',\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                color: \"text.secondary\",\n                children: [\"Cantiere: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: currentCantiereName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: `ID: ${currentCantiereId}`,\n                size: \"small\",\n                color: \"primary\",\n                variant: \"outlined\",\n                sx: {\n                  ml: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(MuiTooltip, {\n            title: \"Aggiorna dati\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: loadDashboardData,\n              color: \"primary\",\n              children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MuiTooltip, {\n            title: \"Esporta report\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              children: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 1,\n        sx: {\n          p: 2,\n          bgcolor: 'grey.50'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FilterList, {\n            sx: {\n              color: 'text.secondary'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            color: \"text.secondary\",\n            sx: {\n              mr: 2\n            },\n            children: \"Periodo di analisi:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            type: \"date\",\n            label: \"Data inizio\",\n            size: \"small\",\n            value: dateRange.start,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              start: e.target.value\n            })),\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            type: \"date\",\n            label: \"Data fine\",\n            size: \"small\",\n            value: dateRange.end,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              end: e.target.value\n            })),\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            position: 'relative',\n            overflow: 'visible'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Work Logs Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: statistics.totalLogs\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(Engineering, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Quantit\\xE0 Totale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: (_statistics$totalQuan = statistics.totalQuantity) === null || _statistics$totalQuan === void 0 ? void 0 : _statistics$totalQuan.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"metri/unit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Ore-Uomo Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: (_statistics$totalManH = statistics.totalManHours) === null || _statistics$totalManH === void 0 ? void 0 : _statistics$totalManH.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"ore lavorate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(Schedule, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          sx: {\n            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8,\n                    mb: 1\n                  },\n                  children: \"Produttivit\\xE0 Media\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: (_statistics$averagePr = statistics.averageProductivity) === null || _statistics$averagePr === void 0 ? void 0 : _statistics$averagePr.toFixed(2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"unit\\xE0/ora\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(Speed, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Produttivit\\xE0 per Attivit\\xE0\",\n            subheader: \"Confronto performance tra diverse tipologie di lavoro\",\n            avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: 'primary.main'\n              },\n              children: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: Object.keys(statistics.byActivity || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: 400\n              },\n              children: /*#__PURE__*/_jsxDEV(Bar, {\n                data: productivityByActivityChart,\n                options: {\n                  ...chartOptions,\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 8,\n                color: 'text.secondary'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Assessment, {\n                sx: {\n                  fontSize: 64,\n                  mb: 2,\n                  opacity: 0.3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"Nessun dato disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Aggiungi dei work logs per visualizzare le statistiche\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          elevation: 3,\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Condizioni Ambientali\",\n            subheader: \"Distribuzione dei lavori per ambiente\",\n            avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: 'secondary.main'\n              },\n              children: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: Object.keys(statistics.byConditions || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: 300,\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Doughnut, {\n                data: conditionsChart,\n                options: {\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      position: 'bottom'\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                py: 4,\n                color: 'text.secondary'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Assessment, {\n                sx: {\n                  fontSize: 48,\n                  mb: 1,\n                  opacity: 0.3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Nessun dato disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        title: \"Trend Giornaliero\",\n        subheader: \"Andamento della produttivit\\xE0 nel tempo\",\n        avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: 'success.main'\n          },\n          children: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        children: Object.keys(statistics.dailyTrend || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            height: 400\n          },\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: dailyTrendChart,\n            options: {\n              ...chartOptions,\n              responsive: true,\n              maintainAspectRatio: false,\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Quantità'\n                  }\n                },\n                y1: {\n                  type: 'linear',\n                  display: true,\n                  position: 'right',\n                  title: {\n                    display: true,\n                    text: 'Produttività'\n                  },\n                  grid: {\n                    drawOnChartArea: false\n                  }\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            py: 8,\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n            sx: {\n              fontSize: 64,\n              mb: 2,\n              opacity: 0.3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Nessun trend disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"I dati del trend appariranno dopo alcuni giorni di lavoro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 589,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 3,\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        title: \"Dettagli per Attivit\\xE0\",\n        subheader: \"Analisi dettagliata delle performance per tipologia di lavoro\",\n        avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: 'info.main'\n          },\n          children: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 0\n        },\n        children: Object.keys(statistics.byActivity || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            overflow: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            component: \"table\",\n            sx: {\n              width: '100%',\n              borderCollapse: 'collapse'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              component: \"thead\",\n              sx: {\n                bgcolor: 'grey.50'\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                component: \"tr\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Attivit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Work Logs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Quantit\\xE0 Totale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Ore-Uomo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"th\",\n                  sx: {\n                    p: 2,\n                    textAlign: 'left',\n                    fontWeight: 600,\n                    color: 'text.secondary'\n                  },\n                  children: \"Produttivit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              component: \"tbody\",\n              children: Object.entries(statistics.byActivity || {}).map(([activity, data], index) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"tr\",\n                sx: {\n                  '&:hover': {\n                    bgcolor: 'grey.50'\n                  },\n                  borderBottom: index < Object.keys(statistics.byActivity).length - 1 ? '1px solid' : 'none',\n                  borderColor: 'divider'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: activity,\n                    color: \"primary\",\n                    variant: \"outlined\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2,\n                    fontWeight: 500\n                  },\n                  children: data.count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: data.quantity.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: data.manHours.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"td\",\n                  sx: {\n                    p: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600\n                      },\n                      children: data.productivity.toFixed(2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"unit\\xE0/ora\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 23\n                }, this)]\n              }, activity, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            py: 8,\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Assessment, {\n            sx: {\n              fontSize: 64,\n              mb: 2,\n              opacity: 0.3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Nessun dettaglio disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"I dettagli appariranno dopo aver registrato dei work logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"LHIDU2BKsFsKxtFz3m+XXdsKuuk=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Line", "Bar", "Doughnut", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Select", "MenuItem", "FormControl", "InputLabel", "TextField", "Chip", "Avatar", "LinearProgress", "Divider", "Paper", "IconButton", "MuiTooltip", "<PERSON><PERSON>", "TrendingUp", "Schedule", "Engineering", "Speed", "Assessment", "FilterList", "Refresh", "Download", "Construction", "useAuth", "axiosInstance", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "_statistics$totalQuan", "_statistics$totalManH", "_statistics$averagePr", "selected<PERSON><PERSON><PERSON>", "authSelectedCantiere", "workLogs", "setWorkLogs", "statistics", "setStatistics", "loading", "setLoading", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "now", "toISOString", "split", "end", "currentCantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "currentCantiereName", "commessa", "loadDashboardData", "console", "warn", "params", "start_date", "end_date", "per_page", "log", "workLogsRes", "get", "data", "work_logs", "calculateStatistics", "error", "logs", "stats", "totalLogs", "length", "totalQuantity", "totalManHours", "averageProductivity", "byActivity", "byOperator", "byConditions", "dailyTrend", "productivityTrend", "for<PERSON>ach", "quantity", "total_man_hours", "activity_type", "count", "manHours", "productivity", "operatorKey", "operator_id", "environmental_conditions", "date", "start_timestamp", "Object", "keys", "activity", "operator", "condition", "productivityByActivityChart", "labels", "datasets", "label", "values", "map", "d", "toFixed", "backgroundColor", "borderColor", "borderWidth", "dailyTrendChart", "sort", "tension", "yAxisID", "<PERSON><PERSON><PERSON>", "chartOptions", "responsive", "plugins", "legend", "position", "scales", "y", "beginAtZero", "y1", "type", "display", "grid", "drawOnChartArea", "sx", "p", "textAlign", "children", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "severity", "justifyContent", "alignItems", "fontSize", "mr", "component", "fontWeight", "mt", "size", "ml", "gap", "title", "onClick", "elevation", "bgcolor", "flexWrap", "value", "onChange", "e", "prev", "target", "InputLabelProps", "shrink", "container", "spacing", "item", "xs", "sm", "md", "background", "overflow", "pb", "opacity", "width", "height", "lg", "subheader", "avatar", "options", "maintainAspectRatio", "py", "text", "borderCollapse", "entries", "index", "borderBottom", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  TextField,\n  Chip,\n  Avatar,\n  LinearProgress,\n  Divider,\n  Paper,\n  IconButton,\n  Tooltip as MuiTooltip,\n  Alert\n} from '@mui/material';\nimport {\n  TrendingUp,\n  Schedule,\n  Engineering,\n  Speed,\n  Assessment,\n  FilterList,\n  Refresh,\n  Download,\n  Construction\n} from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\n// Registra i componenti Chart.js\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\nconst Dashboard = () => {\n  const { selectedCantiere: authSelectedCantiere } = useAuth();\n  const [workLogs, setWorkLogs] = useState([]);\n  const [statistics, setStatistics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [dateRange, setDateRange] = useState({\n    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 giorni fa\n    end: new Date().toISOString().split('T')[0] // oggi\n  });\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = authSelectedCantiere?.id_cantiere ||\n                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||\n                           null;\n  const currentCantiereName = authSelectedCantiere?.commessa ||\n                             localStorage.getItem('selectedCantiereName') ||\n                             'Cantiere non selezionato';\n\n  useEffect(() => {\n    if (currentCantiereId) {\n      loadDashboardData();\n    }\n  }, [currentCantiereId, dateRange]);\n\n  // Carica i dati solo se c'è un cantiere attivo\n  useEffect(() => {\n    if (!currentCantiereId) {\n      setLoading(false);\n    }\n  }, [currentCantiereId]);\n\n  const loadDashboardData = async () => {\n    if (!currentCantiereId) {\n      console.warn('Nessun cantiere attivo selezionato per la produttività');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // Parametri per le chiamate API - usa solo il cantiere attivo\n      const params = {\n        id_cantiere: currentCantiereId,\n        start_date: dateRange.start,\n        end_date: dateRange.end,\n        per_page: 100\n      };\n\n      console.log('Caricamento dati produttività per cantiere:', currentCantiereId, currentCantiereName);\n\n      // Carica work logs per il cantiere attivo\n      const workLogsRes = await axiosInstance.get('/v1/work-logs', { params });\n      setWorkLogs(workLogsRes.data.work_logs || []);\n\n      // Calcola statistiche\n      calculateStatistics(workLogsRes.data.work_logs || []);\n\n    } catch (error) {\n      console.error('Errore nel caricamento dati dashboard:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateStatistics = (logs) => {\n    const stats = {\n      totalLogs: logs.length,\n      totalQuantity: 0,\n      totalManHours: 0,\n      averageProductivity: 0,\n      byActivity: {},\n      byOperator: {},\n      byConditions: {},\n      dailyTrend: {},\n      productivityTrend: []\n    };\n\n    logs.forEach(log => {\n      // Totali\n      stats.totalQuantity += log.quantity || 0;\n      stats.totalManHours += log.total_man_hours || 0;\n\n      // Per attività\n      if (!stats.byActivity[log.activity_type]) {\n        stats.byActivity[log.activity_type] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byActivity[log.activity_type].count++;\n      stats.byActivity[log.activity_type].quantity += log.quantity || 0;\n      stats.byActivity[log.activity_type].manHours += log.total_man_hours || 0;\n\n      // Per operatore\n      const operatorKey = `${log.operator_id}`;\n      if (!stats.byOperator[operatorKey]) {\n        stats.byOperator[operatorKey] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byOperator[operatorKey].count++;\n      stats.byOperator[operatorKey].quantity += log.quantity || 0;\n      stats.byOperator[operatorKey].manHours += log.total_man_hours || 0;\n\n      // Per condizioni ambientali\n      if (!stats.byConditions[log.environmental_conditions]) {\n        stats.byConditions[log.environmental_conditions] = {\n          count: 0,\n          quantity: 0,\n          productivity: 0\n        };\n      }\n      stats.byConditions[log.environmental_conditions].count++;\n      stats.byConditions[log.environmental_conditions].quantity += log.quantity || 0;\n\n      // Trend giornaliero\n      const date = new Date(log.start_timestamp).toISOString().split('T')[0];\n      if (!stats.dailyTrend[date]) {\n        stats.dailyTrend[date] = {\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.dailyTrend[date].quantity += log.quantity || 0;\n      stats.dailyTrend[date].manHours += log.total_man_hours || 0;\n    });\n\n    // Calcola produttività medie\n    stats.averageProductivity = stats.totalManHours > 0 ? stats.totalQuantity / stats.totalManHours : 0;\n\n    Object.keys(stats.byActivity).forEach(activity => {\n      const data = stats.byActivity[activity];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.byOperator).forEach(operator => {\n      const data = stats.byOperator[operator];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.byConditions).forEach(condition => {\n      const data = stats.byConditions[condition];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.dailyTrend).forEach(date => {\n      const data = stats.dailyTrend[date];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    setStatistics(stats);\n  };\n\n  // Configurazioni grafici\n  const productivityByActivityChart = {\n    labels: Object.keys(statistics.byActivity || {}),\n    datasets: [\n      {\n        label: 'Produttività (unità/ora)',\n        data: Object.values(statistics.byActivity || {}).map(d => d.productivity.toFixed(2)),\n        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n        borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const dailyTrendChart = {\n    labels: Object.keys(statistics.dailyTrend || {}).sort(),\n    datasets: [\n      {\n        label: 'Quantità Giornaliera',\n        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => \n          statistics.dailyTrend[date].quantity\n        ),\n        borderColor: '#3B82F6',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        tension: 0.1,\n      },\n      {\n        label: 'Produttività Giornaliera',\n        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => \n          statistics.dailyTrend[date].productivity\n        ),\n        borderColor: '#10B981',\n        backgroundColor: 'rgba(16, 185, 129, 0.1)',\n        tension: 0.1,\n        yAxisID: 'y1',\n      },\n    ],\n  };\n\n  const conditionsChart = {\n    labels: Object.keys(statistics.byConditions || {}),\n    datasets: [\n      {\n        data: Object.values(statistics.byConditions || {}).map(d => d.count),\n        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n      },\n      y1: {\n        type: 'linear',\n        display: true,\n        position: 'right',\n        grid: {\n          drawOnChartArea: false,\n        },\n      },\n    },\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ p: 4, textAlign: 'center' }}>\n        <LinearProgress sx={{ mb: 2 }} />\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Caricamento dashboard...\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Se non c'è un cantiere attivo, mostra un messaggio\n  if (!currentCantiereId) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\" sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ mb: 1 }}>\n            Nessun cantiere attivo selezionato\n          </Typography>\n          <Typography variant=\"body2\">\n            Per visualizzare i dati di produttività, seleziona prima un cantiere dalla dashboard principale.\n          </Typography>\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header con cantiere attivo */}\n      <Box sx={{ mb: 4 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Assessment sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />\n            <Box>\n              <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700 }}>\n                Dashboard Produttività\n              </Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Construction sx={{ fontSize: 20, color: 'text.secondary', mr: 1 }} />\n                <Typography variant=\"subtitle1\" color=\"text.secondary\">\n                  Cantiere: <strong>{currentCantiereName}</strong>\n                </Typography>\n                <Chip\n                  label={`ID: ${currentCantiereId}`}\n                  size=\"small\"\n                  color=\"primary\"\n                  variant=\"outlined\"\n                  sx={{ ml: 2 }}\n                />\n              </Box>\n            </Box>\n          </Box>\n\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            <MuiTooltip title=\"Aggiorna dati\">\n              <IconButton onClick={loadDashboardData} color=\"primary\">\n                <Refresh />\n              </IconButton>\n            </MuiTooltip>\n            <MuiTooltip title=\"Esporta report\">\n              <IconButton color=\"primary\">\n                <Download />\n              </IconButton>\n            </MuiTooltip>\n          </Box>\n        </Box>\n\n        {/* Filtri temporali */}\n        <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>\n            <FilterList sx={{ color: 'text.secondary' }} />\n            <Typography variant=\"subtitle2\" color=\"text.secondary\" sx={{ mr: 2 }}>\n              Periodo di analisi:\n            </Typography>\n\n            <TextField\n              type=\"date\"\n              label=\"Data inizio\"\n              size=\"small\"\n              value={dateRange.start}\n              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n              InputLabelProps={{ shrink: true }}\n            />\n\n            <TextField\n              type=\"date\"\n              label=\"Data fine\"\n              size=\"small\"\n              value={dateRange.end}\n              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n              InputLabelProps={{ shrink: true }}\n            />\n          </Box>\n        </Paper>\n      </Box>\n\n      {/* KPI Cards moderne */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              position: 'relative',\n              overflow: 'visible'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Work Logs Totali\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.totalLogs}\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <Engineering sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n              color: 'white'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Quantità Totale\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.totalQuantity?.toFixed(1)}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    metri/unità\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <TrendingUp sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n              color: 'white'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Ore-Uomo Totali\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.totalManHours?.toFixed(1)}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    ore lavorate\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <Schedule sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            elevation={3}\n            sx={{\n              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n              color: 'white'\n            }}\n          >\n            <CardContent sx={{ pb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                    Produttività Media\n                  </Typography>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                    {statistics.averageProductivity?.toFixed(2)}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    unità/ora\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <Speed sx={{ fontSize: 28 }} />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Grafici moderni */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        {/* Produttività per Attività */}\n        <Grid item xs={12} lg={8}>\n          <Card elevation={3}>\n            <CardHeader\n              title=\"Produttività per Attività\"\n              subheader=\"Confronto performance tra diverse tipologie di lavoro\"\n              avatar={\n                <Avatar sx={{ bgcolor: 'primary.main' }}>\n                  <TrendingUp />\n                </Avatar>\n              }\n            />\n            <CardContent>\n              {Object.keys(statistics.byActivity || {}).length > 0 ? (\n                <Box sx={{ height: 400 }}>\n                  <Bar data={productivityByActivityChart} options={{\n                    ...chartOptions,\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        display: false\n                      }\n                    }\n                  }} />\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>\n                  <Assessment sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />\n                  <Typography variant=\"h6\">Nessun dato disponibile</Typography>\n                  <Typography variant=\"body2\">\n                    Aggiungi dei work logs per visualizzare le statistiche\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Distribuzione Condizioni Ambientali */}\n        <Grid item xs={12} lg={4}>\n          <Card elevation={3}>\n            <CardHeader\n              title=\"Condizioni Ambientali\"\n              subheader=\"Distribuzione dei lavori per ambiente\"\n              avatar={\n                <Avatar sx={{ bgcolor: 'secondary.main' }}>\n                  <Assessment />\n                </Avatar>\n              }\n            />\n            <CardContent>\n              {Object.keys(statistics.byConditions || {}).length > 0 ? (\n                <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>\n                  <Doughnut data={conditionsChart} options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'bottom'\n                      }\n                    }\n                  }} />\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>\n                  <Assessment sx={{ fontSize: 48, mb: 1, opacity: 0.3 }} />\n                  <Typography variant=\"body2\">Nessun dato disponibile</Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Trend Giornaliero */}\n      <Card elevation={3} sx={{ mb: 4 }}>\n        <CardHeader\n          title=\"Trend Giornaliero\"\n          subheader=\"Andamento della produttività nel tempo\"\n          avatar={\n            <Avatar sx={{ bgcolor: 'success.main' }}>\n              <TrendingUp />\n            </Avatar>\n          }\n        />\n        <CardContent>\n          {Object.keys(statistics.dailyTrend || {}).length > 0 ? (\n            <Box sx={{ height: 400 }}>\n              <Line data={dailyTrendChart} options={{\n                ...chartOptions,\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    title: {\n                      display: true,\n                      text: 'Quantità'\n                    }\n                  },\n                  y1: {\n                    type: 'linear',\n                    display: true,\n                    position: 'right',\n                    title: {\n                      display: true,\n                      text: 'Produttività'\n                    },\n                    grid: {\n                      drawOnChartArea: false,\n                    },\n                  },\n                }\n              }} />\n            </Box>\n          ) : (\n            <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>\n              <TrendingUp sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />\n              <Typography variant=\"h6\">Nessun trend disponibile</Typography>\n              <Typography variant=\"body2\">\n                I dati del trend appariranno dopo alcuni giorni di lavoro\n              </Typography>\n            </Box>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Tabella Dettagli per Attività */}\n      <Card elevation={3}>\n        <CardHeader\n          title=\"Dettagli per Attività\"\n          subheader=\"Analisi dettagliata delle performance per tipologia di lavoro\"\n          avatar={\n            <Avatar sx={{ bgcolor: 'info.main' }}>\n              <Assessment />\n            </Avatar>\n          }\n        />\n        <CardContent sx={{ p: 0 }}>\n          {Object.keys(statistics.byActivity || {}).length > 0 ? (\n            <Box sx={{ overflow: 'auto' }}>\n              <Box component=\"table\" sx={{ width: '100%', borderCollapse: 'collapse' }}>\n                <Box component=\"thead\" sx={{ bgcolor: 'grey.50' }}>\n                  <Box component=\"tr\">\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Attività\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Work Logs\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Quantità Totale\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Ore-Uomo\n                    </Box>\n                    <Box component=\"th\" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>\n                      Produttività\n                    </Box>\n                  </Box>\n                </Box>\n                <Box component=\"tbody\">\n                  {Object.entries(statistics.byActivity || {}).map(([activity, data], index) => (\n                    <Box\n                      component=\"tr\"\n                      key={activity}\n                      sx={{\n                        '&:hover': { bgcolor: 'grey.50' },\n                        borderBottom: index < Object.keys(statistics.byActivity).length - 1 ? '1px solid' : 'none',\n                        borderColor: 'divider'\n                      }}\n                    >\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        <Chip\n                          label={activity}\n                          color=\"primary\"\n                          variant=\"outlined\"\n                          size=\"small\"\n                        />\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2, fontWeight: 500 }}>\n                        {data.count}\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        {data.quantity.toFixed(1)}\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        {data.manHours.toFixed(1)}\n                      </Box>\n                      <Box component=\"td\" sx={{ p: 2 }}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                            {data.productivity.toFixed(2)}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            unità/ora\n                          </Typography>\n                        </Box>\n                      </Box>\n                    </Box>\n                  ))}\n                </Box>\n              </Box>\n            </Box>\n          ) : (\n            <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>\n              <Assessment sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />\n              <Typography variant=\"h6\">Nessun dettaglio disponibile</Typography>\n              <Typography variant=\"body2\">\n                I dettagli appariranno dopo aver registrato dei work logs\n              </Typography>\n            </Box>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AACrD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVpB,OAAO,IAAIqB,UAAU,EACrBC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,YAAY,QACP,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,aAAa,MAAM,4BAA4B;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA1C,OAAO,CAAC2C,QAAQ,CACd1C,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMmC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACtB,MAAM;IAAEC,gBAAgB,EAAEC;EAAqB,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC5D,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC;IACzCiE,KAAK,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IACpFC,GAAG,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC;;EAEF;EACA,MAAME,iBAAiB,GAAG,CAAAf,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgB,WAAW,KAClCC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC,IACxD,IAAI;EAC7B,MAAMC,mBAAmB,GAAG,CAAApB,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEqB,QAAQ,KAC/BH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAC5C,0BAA0B;EAErD1E,SAAS,CAAC,MAAM;IACd,IAAIsE,iBAAiB,EAAE;MACrBO,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,iBAAiB,EAAER,SAAS,CAAC,CAAC;;EAElC;EACA9D,SAAS,CAAC,MAAM;IACd,IAAI,CAACsE,iBAAiB,EAAE;MACtBT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACS,iBAAiB,CAAC,CAAC;EAEvB,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACP,iBAAiB,EAAE;MACtBQ,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;MACtElB,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACFA,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMmB,MAAM,GAAG;QACbT,WAAW,EAAED,iBAAiB;QAC9BW,UAAU,EAAEnB,SAAS,CAACE,KAAK;QAC3BkB,QAAQ,EAAEpB,SAAS,CAACO,GAAG;QACvBc,QAAQ,EAAE;MACZ,CAAC;MAEDL,OAAO,CAACM,GAAG,CAAC,6CAA6C,EAAEd,iBAAiB,EAAEK,mBAAmB,CAAC;;MAElG;MACA,MAAMU,WAAW,GAAG,MAAMxC,aAAa,CAACyC,GAAG,CAAC,eAAe,EAAE;QAAEN;MAAO,CAAC,CAAC;MACxEvB,WAAW,CAAC4B,WAAW,CAACE,IAAI,CAACC,SAAS,IAAI,EAAE,CAAC;;MAE7C;MACAC,mBAAmB,CAACJ,WAAW,CAACE,IAAI,CAACC,SAAS,IAAI,EAAE,CAAC;IAEvD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,mBAAmB,GAAIE,IAAI,IAAK;IACpC,MAAMC,KAAK,GAAG;MACZC,SAAS,EAAEF,IAAI,CAACG,MAAM;MACtBC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,mBAAmB,EAAE,CAAC;MACtBC,UAAU,EAAE,CAAC,CAAC;MACdC,UAAU,EAAE,CAAC,CAAC;MACdC,YAAY,EAAE,CAAC,CAAC;MAChBC,UAAU,EAAE,CAAC,CAAC;MACdC,iBAAiB,EAAE;IACrB,CAAC;IAEDX,IAAI,CAACY,OAAO,CAACnB,GAAG,IAAI;MAClB;MACAQ,KAAK,CAACG,aAAa,IAAIX,GAAG,CAACoB,QAAQ,IAAI,CAAC;MACxCZ,KAAK,CAACI,aAAa,IAAIZ,GAAG,CAACqB,eAAe,IAAI,CAAC;;MAE/C;MACA,IAAI,CAACb,KAAK,CAACM,UAAU,CAACd,GAAG,CAACsB,aAAa,CAAC,EAAE;QACxCd,KAAK,CAACM,UAAU,CAACd,GAAG,CAACsB,aAAa,CAAC,GAAG;UACpCC,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACM,UAAU,CAACd,GAAG,CAACsB,aAAa,CAAC,CAACC,KAAK,EAAE;MAC3Cf,KAAK,CAACM,UAAU,CAACd,GAAG,CAACsB,aAAa,CAAC,CAACF,QAAQ,IAAIpB,GAAG,CAACoB,QAAQ,IAAI,CAAC;MACjEZ,KAAK,CAACM,UAAU,CAACd,GAAG,CAACsB,aAAa,CAAC,CAACE,QAAQ,IAAIxB,GAAG,CAACqB,eAAe,IAAI,CAAC;;MAExE;MACA,MAAMK,WAAW,GAAG,GAAG1B,GAAG,CAAC2B,WAAW,EAAE;MACxC,IAAI,CAACnB,KAAK,CAACO,UAAU,CAACW,WAAW,CAAC,EAAE;QAClClB,KAAK,CAACO,UAAU,CAACW,WAAW,CAAC,GAAG;UAC9BH,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACO,UAAU,CAACW,WAAW,CAAC,CAACH,KAAK,EAAE;MACrCf,KAAK,CAACO,UAAU,CAACW,WAAW,CAAC,CAACN,QAAQ,IAAIpB,GAAG,CAACoB,QAAQ,IAAI,CAAC;MAC3DZ,KAAK,CAACO,UAAU,CAACW,WAAW,CAAC,CAACF,QAAQ,IAAIxB,GAAG,CAACqB,eAAe,IAAI,CAAC;;MAElE;MACA,IAAI,CAACb,KAAK,CAACQ,YAAY,CAAChB,GAAG,CAAC4B,wBAAwB,CAAC,EAAE;QACrDpB,KAAK,CAACQ,YAAY,CAAChB,GAAG,CAAC4B,wBAAwB,CAAC,GAAG;UACjDL,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXK,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACQ,YAAY,CAAChB,GAAG,CAAC4B,wBAAwB,CAAC,CAACL,KAAK,EAAE;MACxDf,KAAK,CAACQ,YAAY,CAAChB,GAAG,CAAC4B,wBAAwB,CAAC,CAACR,QAAQ,IAAIpB,GAAG,CAACoB,QAAQ,IAAI,CAAC;;MAE9E;MACA,MAAMS,IAAI,GAAG,IAAIhD,IAAI,CAACmB,GAAG,CAAC8B,eAAe,CAAC,CAAC/C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtE,IAAI,CAACwB,KAAK,CAACS,UAAU,CAACY,IAAI,CAAC,EAAE;QAC3BrB,KAAK,CAACS,UAAU,CAACY,IAAI,CAAC,GAAG;UACvBT,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACS,UAAU,CAACY,IAAI,CAAC,CAACT,QAAQ,IAAIpB,GAAG,CAACoB,QAAQ,IAAI,CAAC;MACpDZ,KAAK,CAACS,UAAU,CAACY,IAAI,CAAC,CAACL,QAAQ,IAAIxB,GAAG,CAACqB,eAAe,IAAI,CAAC;IAC7D,CAAC,CAAC;;IAEF;IACAb,KAAK,CAACK,mBAAmB,GAAGL,KAAK,CAACI,aAAa,GAAG,CAAC,GAAGJ,KAAK,CAACG,aAAa,GAAGH,KAAK,CAACI,aAAa,GAAG,CAAC;IAEnGmB,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACM,UAAU,CAAC,CAACK,OAAO,CAACc,QAAQ,IAAI;MAChD,MAAM9B,IAAI,GAAGK,KAAK,CAACM,UAAU,CAACmB,QAAQ,CAAC;MACvC9B,IAAI,CAACsB,YAAY,GAAGtB,IAAI,CAACqB,QAAQ,GAAG,CAAC,GAAGrB,IAAI,CAACiB,QAAQ,GAAGjB,IAAI,CAACqB,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACO,UAAU,CAAC,CAACI,OAAO,CAACe,QAAQ,IAAI;MAChD,MAAM/B,IAAI,GAAGK,KAAK,CAACO,UAAU,CAACmB,QAAQ,CAAC;MACvC/B,IAAI,CAACsB,YAAY,GAAGtB,IAAI,CAACqB,QAAQ,GAAG,CAAC,GAAGrB,IAAI,CAACiB,QAAQ,GAAGjB,IAAI,CAACqB,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACQ,YAAY,CAAC,CAACG,OAAO,CAACgB,SAAS,IAAI;MACnD,MAAMhC,IAAI,GAAGK,KAAK,CAACQ,YAAY,CAACmB,SAAS,CAAC;MAC1ChC,IAAI,CAACsB,YAAY,GAAGtB,IAAI,CAACqB,QAAQ,GAAG,CAAC,GAAGrB,IAAI,CAACiB,QAAQ,GAAGjB,IAAI,CAACqB,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACS,UAAU,CAAC,CAACE,OAAO,CAACU,IAAI,IAAI;MAC5C,MAAM1B,IAAI,GAAGK,KAAK,CAACS,UAAU,CAACY,IAAI,CAAC;MACnC1B,IAAI,CAACsB,YAAY,GAAGtB,IAAI,CAACqB,QAAQ,GAAG,CAAC,GAAGrB,IAAI,CAACiB,QAAQ,GAAGjB,IAAI,CAACqB,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFjD,aAAa,CAACiC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAM4B,2BAA2B,GAAG;IAClCC,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACwC,UAAU,IAAI,CAAC,CAAC,CAAC;IAChDwB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,0BAA0B;MACjCpC,IAAI,EAAE4B,MAAM,CAACS,MAAM,CAAClE,UAAU,CAACwC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,YAAY,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC;MACpFC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC7DC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACzDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBV,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC2C,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC+B,IAAI,CAAC,CAAC;IACvDV,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,sBAAsB;MAC7BpC,IAAI,EAAE4B,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC2C,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC+B,IAAI,CAAC,CAAC,CAACP,GAAG,CAACZ,IAAI,IAC5DvD,UAAU,CAAC2C,UAAU,CAACY,IAAI,CAAC,CAACT,QAC9B,CAAC;MACDyB,WAAW,EAAE,SAAS;MACtBD,eAAe,EAAE,yBAAyB;MAC1CK,OAAO,EAAE;IACX,CAAC,EACD;MACEV,KAAK,EAAE,0BAA0B;MACjCpC,IAAI,EAAE4B,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC2C,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC+B,IAAI,CAAC,CAAC,CAACP,GAAG,CAACZ,IAAI,IAC5DvD,UAAU,CAAC2C,UAAU,CAACY,IAAI,CAAC,CAACJ,YAC9B,CAAC;MACDoB,WAAW,EAAE,SAAS;MACtBD,eAAe,EAAE,yBAAyB;MAC1CK,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBd,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,YAAY,IAAI,CAAC,CAAC,CAAC;IAClDsB,QAAQ,EAAE,CACR;MACEnC,IAAI,EAAE4B,MAAM,CAACS,MAAM,CAAClE,UAAU,CAAC0C,YAAY,IAAI,CAAC,CAAC,CAAC,CAACyB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,KAAK,CAAC;MACpEqB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC7DE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMM,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE;MACf,CAAC;MACDC,EAAE,EAAE;QACFC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,IAAI;QACbN,QAAQ,EAAE,OAAO;QACjBO,IAAI,EAAE;UACJC,eAAe,EAAE;QACnB;MACF;IACF;EACF,CAAC;EAED,IAAIxF,OAAO,EAAE;IACX,oBACEb,OAAA,CAAChC,GAAG;MAACsI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrCzG,OAAA,CAAClB,cAAc;QAACwH,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjC9G,OAAA,CAAC9B,UAAU;QAAC6I,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEhD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;;EAEA;EACA,IAAI,CAACvF,iBAAiB,EAAE;IACtB,oBACEvB,OAAA,CAAChC,GAAG;MAACsI,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,eAChBzG,OAAA,CAACb,KAAK;QAAC8H,QAAQ,EAAC,SAAS;QAACX,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBACtCzG,OAAA,CAAC9B,UAAU;UAAC6I,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAExC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9G,OAAA,CAAC9B,UAAU;UAAC6I,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACE9G,OAAA,CAAChC,GAAG;IAACsI,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBzG,OAAA,CAAChC,GAAG;MAACsI,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjBzG,OAAA,CAAChC,GAAG;QAACsI,EAAE,EAAE;UAAEH,OAAO,EAAE,MAAM;UAAEe,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAET,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBACzFzG,OAAA,CAAChC,GAAG;UAACsI,EAAE,EAAE;YAAEH,OAAO,EAAE,MAAM;YAAEgB,UAAU,EAAE;UAAS,CAAE;UAAAV,QAAA,gBACjDzG,OAAA,CAACR,UAAU;YAAC8G,EAAE,EAAE;cAAEc,QAAQ,EAAE,EAAE;cAAEJ,KAAK,EAAE,cAAc;cAAEK,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClE9G,OAAA,CAAChC,GAAG;YAAAyI,QAAA,gBACFzG,OAAA,CAAC9B,UAAU;cAAC6I,OAAO,EAAC,IAAI;cAACO,SAAS,EAAC,IAAI;cAAChB,EAAE,EAAE;gBAAEiB,UAAU,EAAE;cAAI,CAAE;cAAAd,QAAA,EAAC;YAEjE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9G,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEgB,UAAU,EAAE,QAAQ;gBAAEK,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,gBACxDzG,OAAA,CAACJ,YAAY;gBAAC0G,EAAE,EAAE;kBAAEc,QAAQ,EAAE,EAAE;kBAAEJ,KAAK,EAAE,gBAAgB;kBAAEK,EAAE,EAAE;gBAAE;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtE9G,OAAA,CAAC9B,UAAU;gBAAC6I,OAAO,EAAC,WAAW;gBAACC,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,GAAC,YAC3C,eAAAzG,OAAA;kBAAAyG,QAAA,EAAS7E;gBAAmB;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACb9G,OAAA,CAACpB,IAAI;gBACHgG,KAAK,EAAE,OAAOrD,iBAAiB,EAAG;gBAClCkG,IAAI,EAAC,OAAO;gBACZT,KAAK,EAAC,SAAS;gBACfD,OAAO,EAAC,UAAU;gBAClBT,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9G,OAAA,CAAChC,GAAG;UAACsI,EAAE,EAAE;YAAEH,OAAO,EAAE,MAAM;YAAEwB,GAAG,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBACnCzG,OAAA,CAACd,UAAU;YAAC0I,KAAK,EAAC,eAAe;YAAAnB,QAAA,eAC/BzG,OAAA,CAACf,UAAU;cAAC4I,OAAO,EAAE/F,iBAAkB;cAACkF,KAAK,EAAC,SAAS;cAAAP,QAAA,eACrDzG,OAAA,CAACN,OAAO;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACb9G,OAAA,CAACd,UAAU;YAAC0I,KAAK,EAAC,gBAAgB;YAAAnB,QAAA,eAChCzG,OAAA,CAACf,UAAU;cAAC+H,KAAK,EAAC,SAAS;cAAAP,QAAA,eACzBzG,OAAA,CAACL,QAAQ;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9G,OAAA,CAAChB,KAAK;QAAC8I,SAAS,EAAE,CAAE;QAACxB,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEwB,OAAO,EAAE;QAAU,CAAE;QAAAtB,QAAA,eACpDzG,OAAA,CAAChC,GAAG;UAACsI,EAAE,EAAE;YAAEH,OAAO,EAAE,MAAM;YAAEgB,UAAU,EAAE,QAAQ;YAAEQ,GAAG,EAAE,CAAC;YAAEK,QAAQ,EAAE;UAAO,CAAE;UAAAvB,QAAA,gBAC3EzG,OAAA,CAACP,UAAU;YAAC6G,EAAE,EAAE;cAAEU,KAAK,EAAE;YAAiB;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C9G,OAAA,CAAC9B,UAAU;YAAC6I,OAAO,EAAC,WAAW;YAACC,KAAK,EAAC,gBAAgB;YAACV,EAAE,EAAE;cAAEe,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb9G,OAAA,CAACrB,SAAS;YACRuH,IAAI,EAAC,MAAM;YACXtB,KAAK,EAAC,aAAa;YACnB6C,IAAI,EAAC,OAAO;YACZQ,KAAK,EAAElH,SAAS,CAACE,KAAM;YACvBiH,QAAQ,EAAGC,CAAC,IAAKnH,YAAY,CAACoH,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEnH,KAAK,EAAEkH,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAC5EK,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEF9G,OAAA,CAACrB,SAAS;YACRuH,IAAI,EAAC,MAAM;YACXtB,KAAK,EAAC,WAAW;YACjB6C,IAAI,EAAC,OAAO;YACZQ,KAAK,EAAElH,SAAS,CAACO,GAAI;YACrB4G,QAAQ,EAAGC,CAAC,IAAKnH,YAAY,CAACoH,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE9G,GAAG,EAAE6G,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAC1EK,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN9G,OAAA,CAAC7B,IAAI;MAACqK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACnC,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACxCzG,OAAA,CAAC7B,IAAI;QAACuK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BzG,OAAA,CAAC5B,IAAI;UACH0J,SAAS,EAAE,CAAE;UACbxB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE,OAAO;YACdnB,QAAQ,EAAE,UAAU;YACpBkD,QAAQ,EAAE;UACZ,CAAE;UAAAtC,QAAA,eAEFzG,OAAA,CAAC3B,WAAW;YAACiI,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBzG,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEgB,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAT,QAAA,gBAClFzG,OAAA,CAAChC,GAAG;gBAAAyI,QAAA,gBACFzG,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9G,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEiB,UAAU,EAAE;kBAAI,CAAE;kBAAAd,QAAA,EAC9C9F,UAAU,CAACmC;gBAAS;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN9G,OAAA,CAACnB,MAAM;gBAACyH,EAAE,EAAE;kBAAEyB,OAAO,EAAE,uBAAuB;kBAAEmB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEzG,OAAA,CAACV,WAAW;kBAACgH,EAAE,EAAE;oBAAEc,QAAQ,EAAE;kBAAG;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9G,OAAA,CAAC7B,IAAI;QAACuK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BzG,OAAA,CAAC5B,IAAI;UACH0J,SAAS,EAAE,CAAE;UACbxB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,eAEFzG,OAAA,CAAC3B,WAAW;YAACiI,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBzG,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEgB,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAT,QAAA,gBAClFzG,OAAA,CAAChC,GAAG;gBAAAyI,QAAA,gBACFzG,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9G,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEiB,UAAU,EAAE;kBAAI,CAAE;kBAAAd,QAAA,GAAArG,qBAAA,GAC9CO,UAAU,CAACqC,aAAa,cAAA5C,qBAAA,uBAAxBA,qBAAA,CAA0B4E,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACb9G,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE;kBAAI,CAAE;kBAAAxC,QAAA,EAAC;gBAEpD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN9G,OAAA,CAACnB,MAAM;gBAACyH,EAAE,EAAE;kBAAEyB,OAAO,EAAE,uBAAuB;kBAAEmB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEzG,OAAA,CAACZ,UAAU;kBAACkH,EAAE,EAAE;oBAAEc,QAAQ,EAAE;kBAAG;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9G,OAAA,CAAC7B,IAAI;QAACuK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BzG,OAAA,CAAC5B,IAAI;UACH0J,SAAS,EAAE,CAAE;UACbxB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,eAEFzG,OAAA,CAAC3B,WAAW;YAACiI,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBzG,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEgB,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAT,QAAA,gBAClFzG,OAAA,CAAChC,GAAG;gBAAAyI,QAAA,gBACFzG,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9G,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEiB,UAAU,EAAE;kBAAI,CAAE;kBAAAd,QAAA,GAAApG,qBAAA,GAC9CM,UAAU,CAACsC,aAAa,cAAA5C,qBAAA,uBAAxBA,qBAAA,CAA0B2E,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACb9G,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE;kBAAI,CAAE;kBAAAxC,QAAA,EAAC;gBAEpD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN9G,OAAA,CAACnB,MAAM;gBAACyH,EAAE,EAAE;kBAAEyB,OAAO,EAAE,uBAAuB;kBAAEmB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEzG,OAAA,CAACX,QAAQ;kBAACiH,EAAE,EAAE;oBAAEc,QAAQ,EAAE;kBAAG;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9G,OAAA,CAAC7B,IAAI;QAACuK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9BzG,OAAA,CAAC5B,IAAI;UACH0J,SAAS,EAAE,CAAE;UACbxB,EAAE,EAAE;YACFwC,UAAU,EAAE,mDAAmD;YAC/D9B,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,eAEFzG,OAAA,CAAC3B,WAAW;YAACiI,EAAE,EAAE;cAAE0C,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,eACzBzG,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAEH,OAAO,EAAE,MAAM;gBAAEgB,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAT,QAAA,gBAClFzG,OAAA,CAAChC,GAAG;gBAAAyI,QAAA,gBACFzG,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE,GAAG;oBAAEvC,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,EAAC;gBAEzD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9G,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEiB,UAAU,EAAE;kBAAI,CAAE;kBAAAd,QAAA,GAAAnG,qBAAA,GAC9CK,UAAU,CAACuC,mBAAmB,cAAA5C,qBAAA,uBAA9BA,qBAAA,CAAgC0E,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACb9G,OAAA,CAAC9B,UAAU;kBAAC6I,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAE2C,OAAO,EAAE;kBAAI,CAAE;kBAAAxC,QAAA,EAAC;gBAEpD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN9G,OAAA,CAACnB,MAAM;gBAACyH,EAAE,EAAE;kBAAEyB,OAAO,EAAE,uBAAuB;kBAAEmB,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eACtEzG,OAAA,CAACT,KAAK;kBAAC+G,EAAE,EAAE;oBAAEc,QAAQ,EAAE;kBAAG;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP9G,OAAA,CAAC7B,IAAI;MAACqK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACnC,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAExCzG,OAAA,CAAC7B,IAAI;QAACuK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACvBzG,OAAA,CAAC5B,IAAI;UAAC0J,SAAS,EAAE,CAAE;UAAArB,QAAA,gBACjBzG,OAAA,CAAC1B,UAAU;YACTsJ,KAAK,EAAC,iCAA2B;YACjCyB,SAAS,EAAC,uDAAuD;YACjEC,MAAM,eACJtJ,OAAA,CAACnB,MAAM;cAACyH,EAAE,EAAE;gBAAEyB,OAAO,EAAE;cAAe,CAAE;cAAAtB,QAAA,eACtCzG,OAAA,CAACZ,UAAU;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9G,OAAA,CAAC3B,WAAW;YAAAoI,QAAA,EACTrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACwC,UAAU,IAAI,CAAC,CAAC,CAAC,CAACJ,MAAM,GAAG,CAAC,gBAClD/C,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAE6C,MAAM,EAAE;cAAI,CAAE;cAAA1C,QAAA,eACvBzG,OAAA,CAAC7C,GAAG;gBAACqF,IAAI,EAAEiC,2BAA4B;gBAAC8E,OAAO,EAAE;kBAC/C,GAAG9D,YAAY;kBACfC,UAAU,EAAE,IAAI;kBAChB8D,mBAAmB,EAAE,KAAK;kBAC1B7D,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNO,OAAO,EAAE;oBACX;kBACF;gBACF;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,gBAEN9G,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAEE,SAAS,EAAE,QAAQ;gBAAEiD,EAAE,EAAE,CAAC;gBAAEzC,KAAK,EAAE;cAAiB,CAAE;cAAAP,QAAA,gBAC/DzG,OAAA,CAACR,UAAU;gBAAC8G,EAAE,EAAE;kBAAEc,QAAQ,EAAE,EAAE;kBAAEV,EAAE,EAAE,CAAC;kBAAEuC,OAAO,EAAE;gBAAI;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzD9G,OAAA,CAAC9B,UAAU;gBAAC6I,OAAO,EAAC,IAAI;gBAAAN,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7D9G,OAAA,CAAC9B,UAAU;gBAAC6I,OAAO,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAE5B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP9G,OAAA,CAAC7B,IAAI;QAACuK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACvBzG,OAAA,CAAC5B,IAAI;UAAC0J,SAAS,EAAE,CAAE;UAAArB,QAAA,gBACjBzG,OAAA,CAAC1B,UAAU;YACTsJ,KAAK,EAAC,uBAAuB;YAC7ByB,SAAS,EAAC,uCAAuC;YACjDC,MAAM,eACJtJ,OAAA,CAACnB,MAAM;cAACyH,EAAE,EAAE;gBAAEyB,OAAO,EAAE;cAAiB,CAAE;cAAAtB,QAAA,eACxCzG,OAAA,CAACR,UAAU;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9G,OAAA,CAAC3B,WAAW;YAAAoI,QAAA,EACTrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,YAAY,IAAI,CAAC,CAAC,CAAC,CAACN,MAAM,GAAG,CAAC,gBACpD/C,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAE6C,MAAM,EAAE,GAAG;gBAAEhD,OAAO,EAAE,MAAM;gBAAEe,cAAc,EAAE;cAAS,CAAE;cAAAT,QAAA,eAClEzG,OAAA,CAAC5C,QAAQ;gBAACoF,IAAI,EAAEgD,eAAgB;gBAAC+D,OAAO,EAAE;kBACxC7D,UAAU,EAAE,IAAI;kBAChB8D,mBAAmB,EAAE,KAAK;kBAC1B7D,OAAO,EAAE;oBACPC,MAAM,EAAE;sBACNC,QAAQ,EAAE;oBACZ;kBACF;gBACF;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,gBAEN9G,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBAAEE,SAAS,EAAE,QAAQ;gBAAEiD,EAAE,EAAE,CAAC;gBAAEzC,KAAK,EAAE;cAAiB,CAAE;cAAAP,QAAA,gBAC/DzG,OAAA,CAACR,UAAU;gBAAC8G,EAAE,EAAE;kBAAEc,QAAQ,EAAE,EAAE;kBAAEV,EAAE,EAAE,CAAC;kBAAEuC,OAAO,EAAE;gBAAI;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzD9G,OAAA,CAAC9B,UAAU;gBAAC6I,OAAO,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP9G,OAAA,CAAC5B,IAAI;MAAC0J,SAAS,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAChCzG,OAAA,CAAC1B,UAAU;QACTsJ,KAAK,EAAC,mBAAmB;QACzByB,SAAS,EAAC,2CAAwC;QAClDC,MAAM,eACJtJ,OAAA,CAACnB,MAAM;UAACyH,EAAE,EAAE;YAAEyB,OAAO,EAAE;UAAe,CAAE;UAAAtB,QAAA,eACtCzG,OAAA,CAACZ,UAAU;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACF9G,OAAA,CAAC3B,WAAW;QAAAoI,QAAA,EACTrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC2C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACP,MAAM,GAAG,CAAC,gBAClD/C,OAAA,CAAChC,GAAG;UAACsI,EAAE,EAAE;YAAE6C,MAAM,EAAE;UAAI,CAAE;UAAA1C,QAAA,eACvBzG,OAAA,CAAC9C,IAAI;YAACsF,IAAI,EAAE4C,eAAgB;YAACmE,OAAO,EAAE;cACpC,GAAG9D,YAAY;cACfC,UAAU,EAAE,IAAI;cAChB8D,mBAAmB,EAAE,KAAK;cAC1B1D,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjB4B,KAAK,EAAE;oBACLzB,OAAO,EAAE,IAAI;oBACbuD,IAAI,EAAE;kBACR;gBACF,CAAC;gBACDzD,EAAE,EAAE;kBACFC,IAAI,EAAE,QAAQ;kBACdC,OAAO,EAAE,IAAI;kBACbN,QAAQ,EAAE,OAAO;kBACjB+B,KAAK,EAAE;oBACLzB,OAAO,EAAE,IAAI;oBACbuD,IAAI,EAAE;kBACR,CAAC;kBACDtD,IAAI,EAAE;oBACJC,eAAe,EAAE;kBACnB;gBACF;cACF;YACF;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,gBAEN9G,OAAA,CAAChC,GAAG;UAACsI,EAAE,EAAE;YAAEE,SAAS,EAAE,QAAQ;YAAEiD,EAAE,EAAE,CAAC;YAAEzC,KAAK,EAAE;UAAiB,CAAE;UAAAP,QAAA,gBAC/DzG,OAAA,CAACZ,UAAU;YAACkH,EAAE,EAAE;cAAEc,QAAQ,EAAE,EAAE;cAAEV,EAAE,EAAE,CAAC;cAAEuC,OAAO,EAAE;YAAI;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzD9G,OAAA,CAAC9B,UAAU;YAAC6I,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9D9G,OAAA,CAAC9B,UAAU;YAAC6I,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP9G,OAAA,CAAC5B,IAAI;MAAC0J,SAAS,EAAE,CAAE;MAAArB,QAAA,gBACjBzG,OAAA,CAAC1B,UAAU;QACTsJ,KAAK,EAAC,0BAAuB;QAC7ByB,SAAS,EAAC,+DAA+D;QACzEC,MAAM,eACJtJ,OAAA,CAACnB,MAAM;UAACyH,EAAE,EAAE;YAAEyB,OAAO,EAAE;UAAY,CAAE;UAAAtB,QAAA,eACnCzG,OAAA,CAACR,UAAU;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACF9G,OAAA,CAAC3B,WAAW;QAACiI,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,EACvBrC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACwC,UAAU,IAAI,CAAC,CAAC,CAAC,CAACJ,MAAM,GAAG,CAAC,gBAClD/C,OAAA,CAAChC,GAAG;UAACsI,EAAE,EAAE;YAAEyC,QAAQ,EAAE;UAAO,CAAE;UAAAtC,QAAA,eAC5BzG,OAAA,CAAChC,GAAG;YAACsJ,SAAS,EAAC,OAAO;YAAChB,EAAE,EAAE;cAAE4C,KAAK,EAAE,MAAM;cAAES,cAAc,EAAE;YAAW,CAAE;YAAAlD,QAAA,gBACvEzG,OAAA,CAAChC,GAAG;cAACsJ,SAAS,EAAC,OAAO;cAAChB,EAAE,EAAE;gBAAEyB,OAAO,EAAE;cAAU,CAAE;cAAAtB,QAAA,eAChDzG,OAAA,CAAChC,GAAG;gBAACsJ,SAAS,EAAC,IAAI;gBAAAb,QAAA,gBACjBzG,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEe,UAAU,EAAE,GAAG;oBAAEP,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEe,UAAU,EAAE,GAAG;oBAAEP,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEe,UAAU,EAAE,GAAG;oBAAEP,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEe,UAAU,EAAE,GAAG;oBAAEP,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEC,SAAS,EAAE,MAAM;oBAAEe,UAAU,EAAE,GAAG;oBAAEP,KAAK,EAAE;kBAAiB,CAAE;kBAAAP,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9G,OAAA,CAAChC,GAAG;cAACsJ,SAAS,EAAC,OAAO;cAAAb,QAAA,EACnBrC,MAAM,CAACwF,OAAO,CAACjJ,UAAU,CAACwC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAAC,CAACR,QAAQ,EAAE9B,IAAI,CAAC,EAAEqH,KAAK,kBACvE7J,OAAA,CAAChC,GAAG;gBACFsJ,SAAS,EAAC,IAAI;gBAEdhB,EAAE,EAAE;kBACF,SAAS,EAAE;oBAAEyB,OAAO,EAAE;kBAAU,CAAC;kBACjC+B,YAAY,EAAED,KAAK,GAAGzF,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACwC,UAAU,CAAC,CAACJ,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM;kBAC1FmC,WAAW,EAAE;gBACf,CAAE;gBAAAuB,QAAA,gBAEFzG,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,eAC/BzG,OAAA,CAACpB,IAAI;oBACHgG,KAAK,EAAEN,QAAS;oBAChB0C,KAAK,EAAC,SAAS;oBACfD,OAAO,EAAC,UAAU;oBAClBU,IAAI,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEgB,UAAU,EAAE;kBAAI,CAAE;kBAAAd,QAAA,EAC/CjE,IAAI,CAACoB;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,EAC9BjE,IAAI,CAACiB,QAAQ,CAACuB,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,EAC9BjE,IAAI,CAACqB,QAAQ,CAACmB,OAAO,CAAC,CAAC;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN9G,OAAA,CAAChC,GAAG;kBAACsJ,SAAS,EAAC,IAAI;kBAAChB,EAAE,EAAE;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAAAE,QAAA,eAC/BzG,OAAA,CAAChC,GAAG;oBAACsI,EAAE,EAAE;sBAAEH,OAAO,EAAE,MAAM;sBAAEgB,UAAU,EAAE,QAAQ;sBAAEQ,GAAG,EAAE;oBAAE,CAAE;oBAAAlB,QAAA,gBACzDzG,OAAA,CAAC9B,UAAU;sBAAC6I,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEiB,UAAU,EAAE;sBAAI,CAAE;sBAAAd,QAAA,EACjDjE,IAAI,CAACsB,YAAY,CAACkB,OAAO,CAAC,CAAC;oBAAC;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACb9G,OAAA,CAAC9B,UAAU;sBAAC6I,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAjCDxC,QAAQ;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkCV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN9G,OAAA,CAAChC,GAAG;UAACsI,EAAE,EAAE;YAAEE,SAAS,EAAE,QAAQ;YAAEiD,EAAE,EAAE,CAAC;YAAEzC,KAAK,EAAE;UAAiB,CAAE;UAAAP,QAAA,gBAC/DzG,OAAA,CAACR,UAAU;YAAC8G,EAAE,EAAE;cAAEc,QAAQ,EAAE,EAAE;cAAEV,EAAE,EAAE,CAAC;cAAEuC,OAAO,EAAE;YAAI;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzD9G,OAAA,CAAC9B,UAAU;YAAC6I,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClE9G,OAAA,CAAC9B,UAAU;YAAC6I,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3G,EAAA,CA3pBID,SAAS;EAAA,QACsCL,OAAO;AAAA;AAAAkK,EAAA,GADtD7J,SAAS;AA6pBf,eAAeA,SAAS;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}