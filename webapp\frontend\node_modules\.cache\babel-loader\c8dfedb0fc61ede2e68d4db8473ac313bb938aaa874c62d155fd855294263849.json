{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\WorkLogsList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, TextField, Select, MenuItem, FormControl, InputLabel, Button, Paper, Chip, Avatar, Divider, Alert, CircularProgress, IconButton, Tooltip, Badge, LinearProgress, Pagination, Stack, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Assignment, Person, Schedule, Engineering, TrendingUp, FilterList, Clear, Edit, Delete, Visibility, Add, ExpandMore, Speed, Timer, Group, LocationOn, Build, Assessment, Construction } from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkLogsList = ({\n  onEdit,\n  onDelete,\n  onCreateNew\n}) => {\n  _s();\n  const {\n    selectedCantiere: authSelectedCantiere\n  } = useAuth();\n  const [workLogs, setWorkLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    activity_type: '',\n    operator_id: '',\n    start_date: '',\n    end_date: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total_count: 0\n  });\n  const [operators, setOperators] = useState([]);\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.id_cantiere) || parseInt(localStorage.getItem('selectedCantiereId'), 10) || null;\n  const currentCantiereName = (authSelectedCantiere === null || authSelectedCantiere === void 0 ? void 0 : authSelectedCantiere.commessa) || localStorage.getItem('selectedCantiereName') || 'Cantiere non selezionato';\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n  useEffect(() => {\n    loadWorkLogs();\n  }, [filters, pagination.page]);\n  const loadInitialData = async () => {\n    try {\n      const [operatorsRes, cantieriRes] = await Promise.all([axiosInstance.get('/responsabili'), axiosInstance.get('/cantieri')]);\n      setOperators(operatorsRes.data || []);\n      setCantieri(cantieriRes.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n    }\n  };\n  const loadWorkLogs = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: pagination.page,\n        per_page: pagination.per_page,\n        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value !== ''))\n      };\n      const response = await axiosInstance.get('/v1/work-logs', {\n        params\n      });\n      setWorkLogs(response.data.work_logs || []);\n      setPagination(prev => ({\n        ...prev,\n        total_count: response.data.total_count || 0\n      }));\n    } catch (error) {\n      console.error('Errore nel caricamento work logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFilterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    })); // Reset alla prima pagina\n  };\n  const clearFilters = () => {\n    setFilters({\n      activity_type: '',\n      operator_id: '',\n      id_cantiere: '',\n      start_date: '',\n      end_date: ''\n    });\n  };\n  const formatDateTime = dateString => {\n    return new Date(dateString).toLocaleString('it-IT', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const formatDuration = minutes => {\n    if (!minutes) return '-';\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n  };\n  const getActivityIcon = activity => {\n    switch (activity) {\n      case 'Posa':\n        return '🔧';\n      case 'Collegamento':\n        return '🔌';\n      case 'Certificazione':\n        return '✅';\n      default:\n        return '📝';\n    }\n  };\n  const getConditionColor = condition => {\n    switch (condition) {\n      case 'Normale':\n        return 'bg-green-100 text-green-800';\n      case 'Spazi Ristretti':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'In Altezza':\n        return 'bg-orange-100 text-orange-800';\n      case 'Esterno':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const totalPages = Math.ceil(pagination.total_count / pagination.per_page);\n  if (loading && workLogs.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Caricamento work logs...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Assignment, {\n            sx: {\n              fontSize: 32,\n              color: 'primary.main',\n              mr: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            sx: {\n              fontWeight: 700\n            },\n            children: \"Work Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: onCreateNew,\n          startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 24\n          }, this),\n          size: \"large\",\n          sx: {\n            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n            '&:hover': {\n              background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n            }\n          },\n          children: \"Nuovo Work Log\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              position: 'relative',\n              overflow: 'visible'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Totale Work Logs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: pagination.total_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Assignment, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n              color: 'white'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Quantit\\xE0 Totale\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: workLogs.reduce((sum, log) => sum + (log.quantity || 0), 0).toFixed(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"metri/unit\\xE0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n              color: 'white'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Ore-Uomo Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: workLogs.reduce((sum, log) => sum + (log.total_man_hours || 0), 0).toFixed(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"ore lavorate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Schedule, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n              color: 'white'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                pb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      opacity: 0.8,\n                      mb: 1\n                    },\n                    children: \"Produttivit\\xE0 Media\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700\n                    },\n                    children: workLogs.length > 0 ? (workLogs.reduce((sum, log) => sum + (log.productivity_per_hour || 0), 0) / workLogs.length).toFixed(2) : '0.00'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      opacity: 0.8\n                    },\n                    children: \"unit\\xE0/ora\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    bgcolor: 'rgba(255,255,255,0.2)',\n                    width: 56,\n                    height: 56\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Speed, {\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n      elevation: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 23\n        }, this),\n        sx: {\n          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n          '&:hover': {\n            background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FilterList, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600\n            },\n            children: \"Filtri Avanzati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              ml: 'auto',\n              mr: 2\n            },\n            children: Object.values(filters).some(v => v !== '') && /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"Filtri Attivi\",\n              color: \"primary\",\n              size: \"small\",\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 2.4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Attivit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"activity_type\",\n                value: filters.activity_type,\n                onChange: handleFilterChange,\n                label: \"Attivit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Posa\",\n                  children: \"\\uD83D\\uDD27 Posa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Collegamento\",\n                  children: \"\\uD83D\\uDD0C Collegamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Certificazione\",\n                  children: \"\\u2705 Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 2.4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"operator_id\",\n                value: filters.operator_id,\n                onChange: handleFilterChange,\n                label: \"Operatore\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this), operators.map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op.id_responsabile,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Person, {\n                      sx: {\n                        mr: 1,\n                        fontSize: 16\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this), op.nome_responsabile]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this)\n                }, op.id_responsabile, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 2.4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Cantiere\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"id_cantiere\",\n                value: filters.id_cantiere,\n                onChange: handleFilterChange,\n                label: \"Cantiere\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this), cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: cantiere.id_cantiere,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(LocationOn, {\n                      sx: {\n                        mr: 1,\n                        fontSize: 16\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 25\n                    }, this), cantiere.commessa]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 23\n                  }, this)\n                }, cantiere.id_cantiere, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 2.4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              type: \"date\",\n              name: \"start_date\",\n              label: \"Data Inizio\",\n              value: filters.start_date,\n              onChange: handleFilterChange,\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 2.4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              type: \"date\",\n              name: \"end_date\",\n              label: \"Data Fine\",\n              value: filters.end_date,\n              onChange: handleFilterChange,\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                gap: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: clearFilters,\n                startIcon: /*#__PURE__*/_jsxDEV(Clear, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 30\n                }, this),\n                size: \"small\",\n                children: \"Pulisci Filtri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), workLogs.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 8,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Assignment, {\n        sx: {\n          fontSize: 80,\n          mb: 2,\n          opacity: 0.3,\n          color: 'text.secondary'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Nessun work log trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: Object.values(filters).some(v => v !== '') ? 'Nessun risultato per i filtri selezionati. Prova a modificare i criteri di ricerca.' : 'Inizia creando il tuo primo work log per monitorare la produttività.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: onCreateNew,\n        startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 24\n        }, this),\n        size: \"large\",\n        sx: {\n          background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n          '&:hover': {\n            background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n          }\n        },\n        children: \"Crea il primo Work Log\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: workLogs.map(log => {\n        var _log$quantity, _log$productivity_per, _log$productivity_per2;\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                transform: 'translateY(-4px)',\n                boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n              avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: log.activity_type === 'Posa' ? 'primary.main' : log.activity_type === 'Collegamento' ? 'secondary.main' : 'success.main',\n                  width: 48,\n                  height: 48\n                },\n                children: log.activity_type === 'Posa' ? /*#__PURE__*/_jsxDEV(Engineering, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 55\n                }, this) : log.activity_type === 'Collegamento' ? /*#__PURE__*/_jsxDEV(Build, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 63\n                }, this) : /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 75\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this),\n              title: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: log.activity_type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 21\n              }, this),\n              subheader: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: log.sub_activity_detail || 'Dettaglio non specificato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 21\n              }, this),\n              action: /*#__PURE__*/_jsxDEV(Chip, {\n                label: formatDateTime(log.start_timestamp),\n                size: \"small\",\n                variant: \"outlined\",\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flexGrow: 1,\n                pt: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 1,\n                      bgcolor: 'primary.50',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 700,\n                        color: 'primary.main'\n                      },\n                      children: (_log$quantity = log.quantity) === null || _log$quantity === void 0 ? void 0 : _log$quantity.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: log.activity_type === 'Posa' ? 'metri' : 'unità'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 1,\n                      bgcolor: 'success.50',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 700,\n                        color: 'success.main'\n                      },\n                      children: formatDuration(log.duration_minutes)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: \"durata\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Person, {\n                    sx: {\n                      mr: 1,\n                      fontSize: 16,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Operatore #\", log.operator_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${log.number_of_operators_on_task} op`,\n                    size: \"small\",\n                    sx: {\n                      ml: 'auto'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Speed, {\n                    sx: {\n                      mr: 1,\n                      fontSize: 16,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [(_log$productivity_per = log.productivity_per_hour) === null || _log$productivity_per === void 0 ? void 0 : _log$productivity_per.toFixed(2), \" /h\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    sx: {\n                      ml: 1\n                    },\n                    children: [\"(\", (_log$productivity_per2 = log.productivity_per_person_per_hour) === null || _log$productivity_per2 === void 0 ? void 0 : _log$productivity_per2.toFixed(2), \" /h/op)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(LocationOn, {\n                    sx: {\n                      mr: 1,\n                      fontSize: 16,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [log.environmental_conditions, \" \\u2022 \", log.tools_used]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Efficienza\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [Math.min(100, log.productivity_per_person_per_hour / 100 * 100).toFixed(0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: Math.min(100, log.productivity_per_person_per_hour / 100 * 100),\n                  sx: {\n                    height: 6,\n                    borderRadius: 3,\n                    bgcolor: 'grey.200',\n                    '& .MuiLinearProgress-bar': {\n                      borderRadius: 3,\n                      background: log.productivity_per_person_per_hour > 80 ? 'linear-gradient(90deg, #4caf50, #8bc34a)' : log.productivity_per_person_per_hour > 50 ? 'linear-gradient(90deg, #ff9800, #ffc107)' : 'linear-gradient(90deg, #f44336, #ff5722)'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this), log.notes && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 1,\n                  bgcolor: 'grey.50',\n                  borderRadius: 1,\n                  mb: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  sx: {\n                    fontStyle: 'italic'\n                  },\n                  children: [\"\\\"\", log.notes, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Modifica\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => onEdit(log),\n                    color: \"primary\",\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 660,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Elimina\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => onDelete(log.id),\n                    color: \"error\",\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this)\n        }, log.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 9\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 4,\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 2,\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [\"Mostrando \", (pagination.page - 1) * pagination.per_page + 1, \"-\", Math.min(pagination.page * pagination.per_page, pagination.total_count), \" di \", pagination.total_count]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n            count: totalPages,\n            page: pagination.page,\n            onChange: (event, value) => setPagination(prev => ({\n              ...prev,\n              page: value\n            })),\n            color: \"primary\",\n            variant: \"outlined\",\n            shape: \"rounded\",\n            showFirstButton: true,\n            showLastButton: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkLogsList, \"hCzroVEVKyIuti7BWJ3TsFhmR6M=\", false, function () {\n  return [useAuth];\n});\n_c = WorkLogsList;\nexport default WorkLogsList;\nvar _c;\n$RefreshReg$(_c, \"WorkLogsList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Paper", "Chip", "Avatar", "Divider", "<PERSON><PERSON>", "CircularProgress", "IconButton", "<PERSON><PERSON><PERSON>", "Badge", "LinearProgress", "Pagination", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Assignment", "Person", "Schedule", "Engineering", "TrendingUp", "FilterList", "Clear", "Edit", "Delete", "Visibility", "Add", "ExpandMore", "Speed", "Timer", "Group", "LocationOn", "Build", "Assessment", "Construction", "useAuth", "axiosInstance", "jsxDEV", "_jsxDEV", "WorkLogsList", "onEdit", "onDelete", "onCreateNew", "_s", "selected<PERSON><PERSON><PERSON>", "authSelectedCantiere", "workLogs", "setWorkLogs", "loading", "setLoading", "filters", "setFilters", "activity_type", "operator_id", "start_date", "end_date", "pagination", "setPagination", "page", "per_page", "total_count", "operators", "setOperators", "currentCantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "currentCantiereName", "commessa", "loadInitialData", "loadWorkLogs", "operatorsRes", "cantieriRes", "Promise", "all", "get", "data", "set<PERSON><PERSON><PERSON>", "error", "console", "params", "Object", "fromEntries", "entries", "filter", "_", "value", "response", "work_logs", "prev", "handleFilterChange", "e", "name", "target", "clearFilters", "formatDateTime", "dateString", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "getActivityIcon", "activity", "getConditionColor", "condition", "totalPages", "ceil", "length", "sx", "p", "textAlign", "children", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "display", "justifyContent", "alignItems", "fontSize", "mr", "component", "fontWeight", "onClick", "startIcon", "size", "background", "container", "spacing", "item", "xs", "sm", "md", "elevation", "position", "overflow", "pb", "opacity", "bgcolor", "width", "height", "reduce", "sum", "log", "quantity", "toFixed", "total_man_hours", "productivity_per_hour", "expandIcon", "ml", "values", "some", "v", "label", "lg", "fullWidth", "onChange", "map", "op", "id_responsabile", "nome_responsabile", "cantieri", "cantiere", "type", "InputLabelProps", "shrink", "gap", "_log$quantity", "_log$productivity_per", "_log$productivity_per2", "flexDirection", "transition", "transform", "boxShadow", "avatar", "title", "subheader", "sub_activity_detail", "action", "start_timestamp", "flexGrow", "pt", "borderRadius", "duration_minutes", "number_of_operators_on_task", "productivity_per_person_per_hour", "environmental_conditions", "tools_used", "min", "notes", "fontStyle", "id", "mt", "direction", "count", "event", "shape", "showFirstButton", "showLastButton", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/WorkLogsList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Button,\n  Paper,\n  Chip,\n  Avatar,\n  Divider,\n  Alert,\n  CircularProgress,\n  IconButton,\n  Tooltip,\n  Badge,\n  LinearProgress,\n  Pagination,\n  Stack,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Assignment,\n  Person,\n  Schedule,\n  Engineering,\n  TrendingUp,\n  FilterList,\n  Clear,\n  Edit,\n  Delete,\n  Visibility,\n  Add,\n  ExpandMore,\n  Speed,\n  Timer,\n  Group,\n  LocationOn,\n  Build,\n  Assessment,\n  Construction\n} from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst WorkLogsList = ({ onEdit, onDelete, onCreateNew }) => {\n  const { selectedCantiere: authSelectedCantiere } = useAuth();\n  const [workLogs, setWorkLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    activity_type: '',\n    operator_id: '',\n    start_date: '',\n    end_date: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total_count: 0\n  });\n  const [operators, setOperators] = useState([]);\n\n  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage\n  const currentCantiereId = authSelectedCantiere?.id_cantiere ||\n                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||\n                           null;\n  const currentCantiereName = authSelectedCantiere?.commessa ||\n                             localStorage.getItem('selectedCantiereName') ||\n                             'Cantiere non selezionato';\n\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n\n  useEffect(() => {\n    loadWorkLogs();\n  }, [filters, pagination.page]);\n\n  const loadInitialData = async () => {\n    try {\n      const [operatorsRes, cantieriRes] = await Promise.all([\n        axiosInstance.get('/responsabili'),\n        axiosInstance.get('/cantieri')\n      ]);\n\n      setOperators(operatorsRes.data || []);\n      setCantieri(cantieriRes.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n    }\n  };\n\n  const loadWorkLogs = async () => {\n    try {\n      setLoading(true);\n      \n      const params = {\n        page: pagination.page,\n        per_page: pagination.per_page,\n        ...Object.fromEntries(\n          Object.entries(filters).filter(([_, value]) => value !== '')\n        )\n      };\n\n      const response = await axiosInstance.get('/v1/work-logs', { params });\n      \n      setWorkLogs(response.data.work_logs || []);\n      setPagination(prev => ({\n        ...prev,\n        total_count: response.data.total_count || 0\n      }));\n\n    } catch (error) {\n      console.error('Errore nel caricamento work logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilterChange = (e) => {\n    const { name, value } = e.target;\n    setFilters(prev => ({ ...prev, [name]: value }));\n    setPagination(prev => ({ ...prev, page: 1 })); // Reset alla prima pagina\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      activity_type: '',\n      operator_id: '',\n      id_cantiere: '',\n      start_date: '',\n      end_date: ''\n    });\n  };\n\n  const formatDateTime = (dateString) => {\n    return new Date(dateString).toLocaleString('it-IT', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatDuration = (minutes) => {\n    if (!minutes) return '-';\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n  };\n\n  const getActivityIcon = (activity) => {\n    switch (activity) {\n      case 'Posa': return '🔧';\n      case 'Collegamento': return '🔌';\n      case 'Certificazione': return '✅';\n      default: return '📝';\n    }\n  };\n\n  const getConditionColor = (condition) => {\n    switch (condition) {\n      case 'Normale': return 'bg-green-100 text-green-800';\n      case 'Spazi Ristretti': return 'bg-yellow-100 text-yellow-800';\n      case 'In Altezza': return 'bg-orange-100 text-orange-800';\n      case 'Esterno': return 'bg-blue-100 text-blue-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const totalPages = Math.ceil(pagination.total_count / pagination.per_page);\n\n  if (loading && workLogs.length === 0) {\n    return (\n      <Box sx={{ p: 4, textAlign: 'center' }}>\n        <CircularProgress sx={{ mb: 2 }} />\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Caricamento work logs...\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header moderno con statistiche */}\n      <Box sx={{ mb: 4 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Assignment sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />\n            <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700 }}>\n              Work Logs\n            </Typography>\n          </Box>\n\n          <Button\n            variant=\"contained\"\n            onClick={onCreateNew}\n            startIcon={<Add />}\n            size=\"large\"\n            sx={{\n              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n              '&:hover': {\n                background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n              }\n            }}\n          >\n            Nuovo Work Log\n          </Button>\n        </Box>\n\n        {/* KPI Cards moderne */}\n        <Grid container spacing={3}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                position: 'relative',\n                overflow: 'visible'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Totale Work Logs\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {pagination.total_count}\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <Assignment sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                color: 'white'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Quantità Totale\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {workLogs.reduce((sum, log) => sum + (log.quantity || 0), 0).toFixed(1)}\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                      metri/unità\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <TrendingUp sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                color: 'white'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Ore-Uomo Totali\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {workLogs.reduce((sum, log) => sum + (log.total_man_hours || 0), 0).toFixed(1)}\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                      ore lavorate\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <Schedule sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Card\n              elevation={3}\n              sx={{\n                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',\n                color: 'white'\n              }}\n            >\n              <CardContent sx={{ pb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 1 }}>\n                      Produttività Media\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n                      {workLogs.length > 0 ?\n                        (workLogs.reduce((sum, log) => sum + (log.productivity_per_hour || 0), 0) / workLogs.length).toFixed(2)\n                        : '0.00'\n                      }\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                      unità/ora\n                    </Typography>\n                  </Box>\n                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                    <Speed sx={{ fontSize: 28 }} />\n                  </Avatar>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </Box>\n\n      {/* Filtri moderni */}\n      <Accordion elevation={3} sx={{ mb: 3 }}>\n        <AccordionSummary\n          expandIcon={<ExpandMore />}\n          sx={{\n            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n            '&:hover': {\n              background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n            }\n          }}\n        >\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <FilterList sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n              Filtri Avanzati\n            </Typography>\n            <Box sx={{ ml: 'auto', mr: 2 }}>\n              {Object.values(filters).some(v => v !== '') && (\n                <Chip\n                  label=\"Filtri Attivi\"\n                  color=\"primary\"\n                  size=\"small\"\n                  variant=\"outlined\"\n                />\n              )}\n            </Box>\n          </Box>\n        </AccordionSummary>\n        <AccordionDetails>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6} lg={2.4}>\n              <FormControl fullWidth size=\"small\">\n                <InputLabel>Attività</InputLabel>\n                <Select\n                  name=\"activity_type\"\n                  value={filters.activity_type}\n                  onChange={handleFilterChange}\n                  label=\"Attività\"\n                >\n                  <MenuItem value=\"\">Tutte</MenuItem>\n                  <MenuItem value=\"Posa\">🔧 Posa</MenuItem>\n                  <MenuItem value=\"Collegamento\">🔌 Collegamento</MenuItem>\n                  <MenuItem value=\"Certificazione\">✅ Certificazione</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6} lg={2.4}>\n              <FormControl fullWidth size=\"small\">\n                <InputLabel>Operatore</InputLabel>\n                <Select\n                  name=\"operator_id\"\n                  value={filters.operator_id}\n                  onChange={handleFilterChange}\n                  label=\"Operatore\"\n                >\n                  <MenuItem value=\"\">Tutti</MenuItem>\n                  {operators.map(op => (\n                    <MenuItem key={op.id_responsabile} value={op.id_responsabile}>\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <Person sx={{ mr: 1, fontSize: 16 }} />\n                        {op.nome_responsabile}\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6} lg={2.4}>\n              <FormControl fullWidth size=\"small\">\n                <InputLabel>Cantiere</InputLabel>\n                <Select\n                  name=\"id_cantiere\"\n                  value={filters.id_cantiere}\n                  onChange={handleFilterChange}\n                  label=\"Cantiere\"\n                >\n                  <MenuItem value=\"\">Tutti</MenuItem>\n                  {cantieri.map(cantiere => (\n                    <MenuItem key={cantiere.id_cantiere} value={cantiere.id_cantiere}>\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <LocationOn sx={{ mr: 1, fontSize: 16 }} />\n                        {cantiere.commessa}\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6} lg={2.4}>\n              <TextField\n                fullWidth\n                size=\"small\"\n                type=\"date\"\n                name=\"start_date\"\n                label=\"Data Inizio\"\n                value={filters.start_date}\n                onChange={handleFilterChange}\n                InputLabelProps={{ shrink: true }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6} lg={2.4}>\n              <TextField\n                fullWidth\n                size=\"small\"\n                type=\"date\"\n                name=\"end_date\"\n                label=\"Data Fine\"\n                value={filters.end_date}\n                onChange={handleFilterChange}\n                InputLabelProps={{ shrink: true }}\n              />\n            </Grid>\n\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n                <Button\n                  variant=\"outlined\"\n                  onClick={clearFilters}\n                  startIcon={<Clear />}\n                  size=\"small\"\n                >\n                  Pulisci Filtri\n                </Button>\n              </Box>\n            </Grid>\n          </Grid>\n        </AccordionDetails>\n      </Accordion>\n\n      {/* Lista Work Logs con Cards moderne */}\n      {workLogs.length === 0 ? (\n        <Paper elevation={3} sx={{ p: 8, textAlign: 'center' }}>\n          <Assignment sx={{ fontSize: 80, mb: 2, opacity: 0.3, color: 'text.secondary' }} />\n          <Typography variant=\"h5\" sx={{ mb: 2, fontWeight: 600 }}>\n            Nessun work log trovato\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n            {Object.values(filters).some(v => v !== '')\n              ? 'Nessun risultato per i filtri selezionati. Prova a modificare i criteri di ricerca.'\n              : 'Inizia creando il tuo primo work log per monitorare la produttività.'\n            }\n          </Typography>\n          <Button\n            variant=\"contained\"\n            onClick={onCreateNew}\n            startIcon={<Add />}\n            size=\"large\"\n            sx={{\n              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n              '&:hover': {\n                background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n              }\n            }}\n          >\n            Crea il primo Work Log\n          </Button>\n        </Paper>\n      ) : (\n        <Grid container spacing={3}>\n          {workLogs.map((log) => (\n            <Grid item xs={12} md={6} lg={4} key={log.id}>\n              <Card\n                elevation={3}\n                sx={{\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    transform: 'translateY(-4px)',\n                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\n                  }\n                }}\n              >\n                <CardHeader\n                  avatar={\n                    <Avatar\n                      sx={{\n                        bgcolor: log.activity_type === 'Posa' ? 'primary.main' :\n                                log.activity_type === 'Collegamento' ? 'secondary.main' : 'success.main',\n                        width: 48,\n                        height: 48\n                      }}\n                    >\n                      {log.activity_type === 'Posa' ? <Engineering /> :\n                       log.activity_type === 'Collegamento' ? <Build /> : <Assessment />}\n                    </Avatar>\n                  }\n                  title={\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                      {log.activity_type}\n                    </Typography>\n                  }\n                  subheader={\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {log.sub_activity_detail || 'Dettaglio non specificato'}\n                    </Typography>\n                  }\n                  action={\n                    <Chip\n                      label={formatDateTime(log.start_timestamp)}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      color=\"primary\"\n                    />\n                  }\n                />\n\n                <CardContent sx={{ flexGrow: 1, pt: 0 }}>\n                  {/* Informazioni principali */}\n                  <Grid container spacing={2} sx={{ mb: 2 }}>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'primary.50', borderRadius: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main' }}>\n                          {log.quantity?.toFixed(1)}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {log.activity_type === 'Posa' ? 'metri' : 'unità'}\n                        </Typography>\n                      </Box>\n                    </Grid>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'success.50', borderRadius: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'success.main' }}>\n                          {formatDuration(log.duration_minutes)}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          durata\n                        </Typography>\n                      </Box>\n                    </Grid>\n                  </Grid>\n\n                  {/* Dettagli operatore e produttività */}\n                  <Box sx={{ mb: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                      <Person sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />\n                      <Typography variant=\"body2\">\n                        Operatore #{log.operator_id}\n                      </Typography>\n                      <Chip\n                        label={`${log.number_of_operators_on_task} op`}\n                        size=\"small\"\n                        sx={{ ml: 'auto' }}\n                      />\n                    </Box>\n\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                      <Speed sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />\n                      <Typography variant=\"body2\">\n                        {log.productivity_per_hour?.toFixed(2)} /h\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\" sx={{ ml: 1 }}>\n                        ({log.productivity_per_person_per_hour?.toFixed(2)} /h/op)\n                      </Typography>\n                    </Box>\n\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <LocationOn sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />\n                      <Typography variant=\"body2\">\n                        {log.environmental_conditions} • {log.tools_used}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  {/* Progress bar produttività */}\n                  <Box sx={{ mb: 2 }}>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Efficienza\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {Math.min(100, (log.productivity_per_person_per_hour / 100) * 100).toFixed(0)}%\n                      </Typography>\n                    </Box>\n                    <LinearProgress\n                      variant=\"determinate\"\n                      value={Math.min(100, (log.productivity_per_person_per_hour / 100) * 100)}\n                      sx={{\n                        height: 6,\n                        borderRadius: 3,\n                        bgcolor: 'grey.200',\n                        '& .MuiLinearProgress-bar': {\n                          borderRadius: 3,\n                          background: log.productivity_per_person_per_hour > 80\n                            ? 'linear-gradient(90deg, #4caf50, #8bc34a)'\n                            : log.productivity_per_person_per_hour > 50\n                            ? 'linear-gradient(90deg, #ff9800, #ffc107)'\n                            : 'linear-gradient(90deg, #f44336, #ff5722)'\n                        }\n                      }}\n                    />\n                  </Box>\n\n                  {/* Note se presenti */}\n                  {log.notes && (\n                    <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\" sx={{ fontStyle: 'italic' }}>\n                        \"{log.notes}\"\n                      </Typography>\n                    </Box>\n                  )}\n\n                  {/* Azioni */}\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>\n                    <Tooltip title=\"Modifica\">\n                      <IconButton\n                        onClick={() => onEdit(log)}\n                        color=\"primary\"\n                        size=\"small\"\n                      >\n                        <Edit />\n                      </IconButton>\n                    </Tooltip>\n                    <Tooltip title=\"Elimina\">\n                      <IconButton\n                        onClick={() => onDelete(log.id)}\n                        color=\"error\"\n                        size=\"small\"\n                      >\n                        <Delete />\n                      </IconButton>\n                    </Tooltip>\n                  </Box>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Paginazione moderna */}\n      {totalPages > 1 && (\n        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>\n          <Paper elevation={2} sx={{ p: 2 }}>\n            <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Mostrando {(pagination.page - 1) * pagination.per_page + 1}-\n                {Math.min(pagination.page * pagination.per_page, pagination.total_count)} di {pagination.total_count}\n              </Typography>\n\n              <Pagination\n                count={totalPages}\n                page={pagination.page}\n                onChange={(event, value) => setPagination(prev => ({ ...prev, page: value }))}\n                color=\"primary\"\n                variant=\"outlined\"\n                shape=\"rounded\"\n                showFirstButton\n                showLastButton\n              />\n            </Stack>\n          </Paper>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default WorkLogsList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,YAAY,QACP,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,gBAAgB,EAAEC;EAAqB,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC5D,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC;IACrCkE,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC;IAC3CwE,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM6E,iBAAiB,GAAG,CAAAlB,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEmB,WAAW,KAClCC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC,IACxD,IAAI;EAC7B,MAAMC,mBAAmB,GAAG,CAAAvB,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEwB,QAAQ,KAC/BH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAC5C,0BAA0B;EAErDhF,SAAS,CAAC,MAAM;IACdmF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAENnF,SAAS,CAAC,MAAM;IACdoF,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACrB,OAAO,EAAEM,UAAU,CAACE,IAAI,CAAC,CAAC;EAE9B,MAAMY,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAM,CAACE,YAAY,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpDvC,aAAa,CAACwC,GAAG,CAAC,eAAe,CAAC,EAClCxC,aAAa,CAACwC,GAAG,CAAC,WAAW,CAAC,CAC/B,CAAC;MAEFd,YAAY,CAACU,YAAY,CAACK,IAAI,IAAI,EAAE,CAAC;MACrCC,WAAW,CAACL,WAAW,CAACI,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC;EAED,MAAMR,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMgC,MAAM,GAAG;QACbvB,IAAI,EAAEF,UAAU,CAACE,IAAI;QACrBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;QAC7B,GAAGuB,MAAM,CAACC,WAAW,CACnBD,MAAM,CAACE,OAAO,CAAClC,OAAO,CAAC,CAACmC,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,KAAKA,KAAK,KAAK,EAAE,CAC7D;MACF,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMpD,aAAa,CAACwC,GAAG,CAAC,eAAe,EAAE;QAAEK;MAAO,CAAC,CAAC;MAErElC,WAAW,CAACyC,QAAQ,CAACX,IAAI,CAACY,SAAS,IAAI,EAAE,CAAC;MAC1ChC,aAAa,CAACiC,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP9B,WAAW,EAAE4B,QAAQ,CAACX,IAAI,CAACjB,WAAW,IAAI;MAC5C,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAM;MAAEC,IAAI;MAAEN;IAAM,CAAC,GAAGK,CAAC,CAACE,MAAM;IAChC3C,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACG,IAAI,GAAGN;IAAM,CAAC,CAAC,CAAC;IAChD9B,aAAa,CAACiC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC;EAED,MAAMqC,YAAY,GAAGA,CAAA,KAAM;IACzB5C,UAAU,CAAC;MACTC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfW,WAAW,EAAE,EAAE;MACfV,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyC,cAAc,GAAIC,UAAU,IAAK;IACrC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;MAClDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,GAAG;IACxB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAOC,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKG,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;EACtD,CAAC;EAED,MAAMC,eAAe,GAAIC,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,cAAc;QAAE,OAAO,IAAI;MAChC,KAAK,gBAAgB;QAAE,OAAO,GAAG;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIC,SAAS,IAAK;IACvC,QAAQA,SAAS;MACf,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,iBAAiB;QAAE,OAAO,+BAA+B;MAC9D,KAAK,YAAY;QAAE,OAAO,+BAA+B;MACzD,KAAK,SAAS;QAAE,OAAO,2BAA2B;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMC,UAAU,GAAGP,IAAI,CAACQ,IAAI,CAAC5D,UAAU,CAACI,WAAW,GAAGJ,UAAU,CAACG,QAAQ,CAAC;EAE1E,IAAIX,OAAO,IAAIF,QAAQ,CAACuE,MAAM,KAAK,CAAC,EAAE;IACpC,oBACE/E,OAAA,CAAClD,GAAG;MAACkI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrCnF,OAAA,CAAChC,gBAAgB;QAACgH,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCxF,OAAA,CAAChD,UAAU;QAACyI,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEhD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACExF,OAAA,CAAClD,GAAG;IAACkI,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBnF,OAAA,CAAClD,GAAG;MAACkI,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjBnF,OAAA,CAAClD,GAAG;QAACkI,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAET,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBACzFnF,OAAA,CAAClD,GAAG;UAACkI,EAAE,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAV,QAAA,gBACjDnF,OAAA,CAACtB,UAAU;YAACsG,EAAE,EAAE;cAAEc,QAAQ,EAAE,EAAE;cAAEJ,KAAK,EAAE,cAAc;cAAEK,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClExF,OAAA,CAAChD,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACO,SAAS,EAAC,IAAI;YAAChB,EAAE,EAAE;cAAEiB,UAAU,EAAE;YAAI,CAAE;YAAAd,QAAA,EAAC;UAEjE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENxF,OAAA,CAACtC,MAAM;UACL+H,OAAO,EAAC,WAAW;UACnBS,OAAO,EAAE9F,WAAY;UACrB+F,SAAS,eAAEnG,OAAA,CAACZ,GAAG;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBY,IAAI,EAAC,OAAO;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kDAAkD;YAC9D,SAAS,EAAE;cACTA,UAAU,EAAE;YACd;UACF,CAAE;UAAAlB,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxF,OAAA,CAAC/C,IAAI;QAACqJ,SAAS;QAACC,OAAO,EAAE,CAAE;QAAApB,QAAA,gBACzBnF,OAAA,CAAC/C,IAAI;UAACuJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BnF,OAAA,CAAC9C,IAAI;YACH0J,SAAS,EAAE,CAAE;YACb5B,EAAE,EAAE;cACFqB,UAAU,EAAE,mDAAmD;cAC/DX,KAAK,EAAE,OAAO;cACdmB,QAAQ,EAAE,UAAU;cACpBC,QAAQ,EAAE;YACZ,CAAE;YAAA3B,QAAA,eAEFnF,OAAA,CAAC7C,WAAW;cAAC6H,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACzBnF,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAT,QAAA,gBAClFnF,OAAA,CAAClD,GAAG;kBAAAqI,QAAA,gBACFnF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEgC,OAAO,EAAE,GAAG;sBAAE5B,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEiB,UAAU,EAAE;oBAAI,CAAE;oBAAAd,QAAA,EAC9CjE,UAAU,CAACI;kBAAW;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxF,OAAA,CAACnC,MAAM;kBAACmH,EAAE,EAAE;oBAAEiC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAAhC,QAAA,eACtEnF,OAAA,CAACtB,UAAU;oBAACsG,EAAE,EAAE;sBAAEc,QAAQ,EAAE;oBAAG;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;UAACuJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BnF,OAAA,CAAC9C,IAAI;YACH0J,SAAS,EAAE,CAAE;YACb5B,EAAE,EAAE;cACFqB,UAAU,EAAE,mDAAmD;cAC/DX,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,eAEFnF,OAAA,CAAC7C,WAAW;cAAC6H,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACzBnF,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAT,QAAA,gBAClFnF,OAAA,CAAClD,GAAG;kBAAAqI,QAAA,gBACFnF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEgC,OAAO,EAAE,GAAG;sBAAE5B,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEiB,UAAU,EAAE;oBAAI,CAAE;oBAAAd,QAAA,EAC9C3E,QAAQ,CAAC4G,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;kBAAC;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,SAAS;oBAACT,EAAE,EAAE;sBAAEgC,OAAO,EAAE;oBAAI,CAAE;oBAAA7B,QAAA,EAAC;kBAEpD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxF,OAAA,CAACnC,MAAM;kBAACmH,EAAE,EAAE;oBAAEiC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAAhC,QAAA,eACtEnF,OAAA,CAAClB,UAAU;oBAACkG,EAAE,EAAE;sBAAEc,QAAQ,EAAE;oBAAG;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;UAACuJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BnF,OAAA,CAAC9C,IAAI;YACH0J,SAAS,EAAE,CAAE;YACb5B,EAAE,EAAE;cACFqB,UAAU,EAAE,mDAAmD;cAC/DX,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,eAEFnF,OAAA,CAAC7C,WAAW;cAAC6H,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACzBnF,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAT,QAAA,gBAClFnF,OAAA,CAAClD,GAAG;kBAAAqI,QAAA,gBACFnF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEgC,OAAO,EAAE,GAAG;sBAAE5B,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEiB,UAAU,EAAE;oBAAI,CAAE;oBAAAd,QAAA,EAC9C3E,QAAQ,CAAC4G,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACG,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACD,OAAO,CAAC,CAAC;kBAAC;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,SAAS;oBAACT,EAAE,EAAE;sBAAEgC,OAAO,EAAE;oBAAI,CAAE;oBAAA7B,QAAA,EAAC;kBAEpD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxF,OAAA,CAACnC,MAAM;kBAACmH,EAAE,EAAE;oBAAEiC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAAhC,QAAA,eACtEnF,OAAA,CAACpB,QAAQ;oBAACoG,EAAE,EAAE;sBAAEc,QAAQ,EAAE;oBAAG;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;UAACuJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BnF,OAAA,CAAC9C,IAAI;YACH0J,SAAS,EAAE,CAAE;YACb5B,EAAE,EAAE;cACFqB,UAAU,EAAE,mDAAmD;cAC/DX,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,eAEFnF,OAAA,CAAC7C,WAAW;cAAC6H,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACzBnF,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAT,QAAA,gBAClFnF,OAAA,CAAClD,GAAG;kBAAAqI,QAAA,gBACFnF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEgC,OAAO,EAAE,GAAG;sBAAE5B,EAAE,EAAE;oBAAE,CAAE;oBAAAD,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEiB,UAAU,EAAE;oBAAI,CAAE;oBAAAd,QAAA,EAC9C3E,QAAQ,CAACuE,MAAM,GAAG,CAAC,GAClB,CAACvE,QAAQ,CAAC4G,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACI,qBAAqB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGlH,QAAQ,CAACuE,MAAM,EAAEyC,OAAO,CAAC,CAAC,CAAC,GACrG;kBAAM;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEA,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,SAAS;oBAACT,EAAE,EAAE;sBAAEgC,OAAO,EAAE;oBAAI,CAAE;oBAAA7B,QAAA,EAAC;kBAEpD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxF,OAAA,CAACnC,MAAM;kBAACmH,EAAE,EAAE;oBAAEiC,OAAO,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,EAAE;oBAAEC,MAAM,EAAE;kBAAG,CAAE;kBAAAhC,QAAA,eACtEnF,OAAA,CAACV,KAAK;oBAAC0F,EAAE,EAAE;sBAAEc,QAAQ,EAAE;oBAAG;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNxF,OAAA,CAACzB,SAAS;MAACqI,SAAS,EAAE,CAAE;MAAC5B,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACrCnF,OAAA,CAACxB,gBAAgB;QACfmJ,UAAU,eAAE3H,OAAA,CAACX,UAAU;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BR,EAAE,EAAE;UACFqB,UAAU,EAAE,mDAAmD;UAC/D,SAAS,EAAE;YACTA,UAAU,EAAE;UACd;QACF,CAAE;QAAAlB,QAAA,eAEFnF,OAAA,CAAClD,GAAG;UAACkI,EAAE,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAA/B,QAAA,gBAChEnF,OAAA,CAACjB,UAAU;YAACiG,EAAE,EAAE;cAAEe,EAAE,EAAE,CAAC;cAAEL,KAAK,EAAE;YAAe;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDxF,OAAA,CAAChD,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEiB,UAAU,EAAE;YAAI,CAAE;YAAAd,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxF,OAAA,CAAClD,GAAG;YAACkI,EAAE,EAAE;cAAE4C,EAAE,EAAE,MAAM;cAAE7B,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAC5BvC,MAAM,CAACiF,MAAM,CAACjH,OAAO,CAAC,CAACkH,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,iBACzC/H,OAAA,CAACpC,IAAI;cACHoK,KAAK,EAAC,eAAe;cACrBtC,KAAK,EAAC,SAAS;cACfU,IAAI,EAAC,OAAO;cACZX,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eACnBxF,OAAA,CAACvB,gBAAgB;QAAA0G,QAAA,eACfnF,OAAA,CAAC/C,IAAI;UAACqJ,SAAS;UAACC,OAAO,EAAE,CAAE;UAAApB,QAAA,gBACzBnF,OAAA,CAAC/C,IAAI;YAACuJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACsB,EAAE,EAAE,GAAI;YAAA9C,QAAA,eAChCnF,OAAA,CAACxC,WAAW;cAAC0K,SAAS;cAAC9B,IAAI,EAAC,OAAO;cAAAjB,QAAA,gBACjCnF,OAAA,CAACvC,UAAU;gBAAA0H,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCxF,OAAA,CAAC1C,MAAM;gBACLiG,IAAI,EAAC,eAAe;gBACpBN,KAAK,EAAErC,OAAO,CAACE,aAAc;gBAC7BqH,QAAQ,EAAE9E,kBAAmB;gBAC7B2E,KAAK,EAAC,aAAU;gBAAA7C,QAAA,gBAEhBnF,OAAA,CAACzC,QAAQ;kBAAC0F,KAAK,EAAC,EAAE;kBAAAkC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCxF,OAAA,CAACzC,QAAQ;kBAAC0F,KAAK,EAAC,MAAM;kBAAAkC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACzCxF,OAAA,CAACzC,QAAQ;kBAAC0F,KAAK,EAAC,cAAc;kBAAAkC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACzDxF,OAAA,CAACzC,QAAQ;kBAAC0F,KAAK,EAAC,gBAAgB;kBAAAkC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;YAACuJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACsB,EAAE,EAAE,GAAI;YAAA9C,QAAA,eAChCnF,OAAA,CAACxC,WAAW;cAAC0K,SAAS;cAAC9B,IAAI,EAAC,OAAO;cAAAjB,QAAA,gBACjCnF,OAAA,CAACvC,UAAU;gBAAA0H,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCxF,OAAA,CAAC1C,MAAM;gBACLiG,IAAI,EAAC,aAAa;gBAClBN,KAAK,EAAErC,OAAO,CAACG,WAAY;gBAC3BoH,QAAQ,EAAE9E,kBAAmB;gBAC7B2E,KAAK,EAAC,WAAW;gBAAA7C,QAAA,gBAEjBnF,OAAA,CAACzC,QAAQ;kBAAC0F,KAAK,EAAC,EAAE;kBAAAkC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClCjE,SAAS,CAAC6G,GAAG,CAACC,EAAE,iBACfrI,OAAA,CAACzC,QAAQ;kBAA0B0F,KAAK,EAAEoF,EAAE,CAACC,eAAgB;kBAAAnD,QAAA,eAC3DnF,OAAA,CAAClD,GAAG;oBAACkI,EAAE,EAAE;sBAAEW,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE;oBAAS,CAAE;oBAAAV,QAAA,gBACjDnF,OAAA,CAACrB,MAAM;sBAACqG,EAAE,EAAE;wBAAEe,EAAE,EAAE,CAAC;wBAAED,QAAQ,EAAE;sBAAG;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtC6C,EAAE,CAACE,iBAAiB;kBAAA;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC,GAJO6C,EAAE,CAACC,eAAe;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKvB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;YAACuJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACsB,EAAE,EAAE,GAAI;YAAA9C,QAAA,eAChCnF,OAAA,CAACxC,WAAW;cAAC0K,SAAS;cAAC9B,IAAI,EAAC,OAAO;cAAAjB,QAAA,gBACjCnF,OAAA,CAACvC,UAAU;gBAAA0H,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCxF,OAAA,CAAC1C,MAAM;gBACLiG,IAAI,EAAC,aAAa;gBAClBN,KAAK,EAAErC,OAAO,CAACc,WAAY;gBAC3ByG,QAAQ,EAAE9E,kBAAmB;gBAC7B2E,KAAK,EAAC,UAAU;gBAAA7C,QAAA,gBAEhBnF,OAAA,CAACzC,QAAQ;kBAAC0F,KAAK,EAAC,EAAE;kBAAAkC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClCgD,QAAQ,CAACJ,GAAG,CAACK,QAAQ,iBACpBzI,OAAA,CAACzC,QAAQ;kBAA4B0F,KAAK,EAAEwF,QAAQ,CAAC/G,WAAY;kBAAAyD,QAAA,eAC/DnF,OAAA,CAAClD,GAAG;oBAACkI,EAAE,EAAE;sBAAEW,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE;oBAAS,CAAE;oBAAAV,QAAA,gBACjDnF,OAAA,CAACP,UAAU;sBAACuF,EAAE,EAAE;wBAAEe,EAAE,EAAE,CAAC;wBAAED,QAAQ,EAAE;sBAAG;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC1CiD,QAAQ,CAAC1G,QAAQ;kBAAA;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC,GAJOiD,QAAQ,CAAC/G,WAAW;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKzB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;YAACuJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACsB,EAAE,EAAE,GAAI;YAAA9C,QAAA,eAChCnF,OAAA,CAAC3C,SAAS;cACR6K,SAAS;cACT9B,IAAI,EAAC,OAAO;cACZsC,IAAI,EAAC,MAAM;cACXnF,IAAI,EAAC,YAAY;cACjByE,KAAK,EAAC,aAAa;cACnB/E,KAAK,EAAErC,OAAO,CAACI,UAAW;cAC1BmH,QAAQ,EAAE9E,kBAAmB;cAC7BsF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;YAACuJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACsB,EAAE,EAAE,GAAI;YAAA9C,QAAA,eAChCnF,OAAA,CAAC3C,SAAS;cACR6K,SAAS;cACT9B,IAAI,EAAC,OAAO;cACZsC,IAAI,EAAC,MAAM;cACXnF,IAAI,EAAC,UAAU;cACfyE,KAAK,EAAC,WAAW;cACjB/E,KAAK,EAAErC,OAAO,CAACK,QAAS;cACxBkH,QAAQ,EAAE9E,kBAAmB;cAC7BsF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPxF,OAAA,CAAC/C,IAAI;YAACuJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChBnF,OAAA,CAAClD,GAAG;cAACkI,EAAE,EAAE;gBAAEW,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEiD,GAAG,EAAE;cAAE,CAAE;cAAA1D,QAAA,eAC/DnF,OAAA,CAACtC,MAAM;gBACL+H,OAAO,EAAC,UAAU;gBAClBS,OAAO,EAAEzC,YAAa;gBACtB0C,SAAS,eAAEnG,OAAA,CAAChB,KAAK;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBY,IAAI,EAAC,OAAO;gBAAAjB,QAAA,EACb;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGXhF,QAAQ,CAACuE,MAAM,KAAK,CAAC,gBACpB/E,OAAA,CAACrC,KAAK;MAACiJ,SAAS,EAAE,CAAE;MAAC5B,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrDnF,OAAA,CAACtB,UAAU;QAACsG,EAAE,EAAE;UAAEc,QAAQ,EAAE,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAE4B,OAAO,EAAE,GAAG;UAAEtB,KAAK,EAAE;QAAiB;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClFxF,OAAA,CAAChD,UAAU;QAACyI,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEI,EAAE,EAAE,CAAC;UAAEa,UAAU,EAAE;QAAI,CAAE;QAAAd,QAAA,EAAC;MAEzD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAAChD,UAAU;QAACyI,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,EAC9DvC,MAAM,CAACiF,MAAM,CAACjH,OAAO,CAAC,CAACkH,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,GACvC,qFAAqF,GACrF;MAAsE;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhE,CAAC,eACbxF,OAAA,CAACtC,MAAM;QACL+H,OAAO,EAAC,WAAW;QACnBS,OAAO,EAAE9F,WAAY;QACrB+F,SAAS,eAAEnG,OAAA,CAACZ,GAAG;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnBY,IAAI,EAAC,OAAO;QACZpB,EAAE,EAAE;UACFqB,UAAU,EAAE,kDAAkD;UAC9D,SAAS,EAAE;YACTA,UAAU,EAAE;UACd;QACF,CAAE;QAAAlB,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAERxF,OAAA,CAAC/C,IAAI;MAACqJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAApB,QAAA,EACxB3E,QAAQ,CAAC4H,GAAG,CAAEd,GAAG;QAAA,IAAAwB,aAAA,EAAAC,qBAAA,EAAAC,sBAAA;QAAA,oBAChBhJ,OAAA,CAAC/C,IAAI;UAACuJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAACsB,EAAE,EAAE,CAAE;UAAA9C,QAAA,eAC9BnF,OAAA,CAAC9C,IAAI;YACH0J,SAAS,EAAE,CAAE;YACb5B,EAAE,EAAE;cACFmC,MAAM,EAAE,MAAM;cACdxB,OAAO,EAAE,MAAM;cACfsD,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAAjE,QAAA,gBAEFnF,OAAA,CAAC5C,UAAU;cACTiM,MAAM,eACJrJ,OAAA,CAACnC,MAAM;gBACLmH,EAAE,EAAE;kBACFiC,OAAO,EAAEK,GAAG,CAACxG,aAAa,KAAK,MAAM,GAAG,cAAc,GAC9CwG,GAAG,CAACxG,aAAa,KAAK,cAAc,GAAG,gBAAgB,GAAG,cAAc;kBAChFoG,KAAK,EAAE,EAAE;kBACTC,MAAM,EAAE;gBACV,CAAE;gBAAAhC,QAAA,EAEDmC,GAAG,CAACxG,aAAa,KAAK,MAAM,gBAAGd,OAAA,CAACnB,WAAW;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAC9C8B,GAAG,CAACxG,aAAa,KAAK,cAAc,gBAAGd,OAAA,CAACN,KAAK;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxF,OAAA,CAACL,UAAU;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACT;cACD8D,KAAK,eACHtJ,OAAA,CAAChD,UAAU;gBAACyI,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEiB,UAAU,EAAE;gBAAI,CAAE;gBAAAd,QAAA,EAC9CmC,GAAG,CAACxG;cAAa;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACb;cACD+D,SAAS,eACPvJ,OAAA,CAAChD,UAAU;gBAACyI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,EAC/CmC,GAAG,CAACkC,mBAAmB,IAAI;cAA2B;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CACb;cACDiE,MAAM,eACJzJ,OAAA,CAACpC,IAAI;gBACHoK,KAAK,EAAEtE,cAAc,CAAC4D,GAAG,CAACoC,eAAe,CAAE;gBAC3CtD,IAAI,EAAC,OAAO;gBACZX,OAAO,EAAC,UAAU;gBAClBC,KAAK,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFxF,OAAA,CAAC7C,WAAW;cAAC6H,EAAE,EAAE;gBAAE2E,QAAQ,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAzE,QAAA,gBAEtCnF,OAAA,CAAC/C,IAAI;gBAACqJ,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAACvB,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,gBACxCnF,OAAA,CAAC/C,IAAI;kBAACuJ,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAtB,QAAA,eACfnF,OAAA,CAAClD,GAAG;oBAACkI,EAAE,EAAE;sBAAEE,SAAS,EAAE,QAAQ;sBAAED,CAAC,EAAE,CAAC;sBAAEgC,OAAO,EAAE,YAAY;sBAAE4C,YAAY,EAAE;oBAAE,CAAE;oBAAA1E,QAAA,gBAC7EnF,OAAA,CAAChD,UAAU;sBAACyI,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEiB,UAAU,EAAE,GAAG;wBAAEP,KAAK,EAAE;sBAAe,CAAE;sBAAAP,QAAA,GAAA2D,aAAA,GACrExB,GAAG,CAACC,QAAQ,cAAAuB,aAAA,uBAAZA,aAAA,CAActB,OAAO,CAAC,CAAC;oBAAC;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACbxF,OAAA,CAAChD,UAAU;sBAACyI,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EACjDmC,GAAG,CAACxG,aAAa,KAAK,MAAM,GAAG,OAAO,GAAG;oBAAO;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACPxF,OAAA,CAAC/C,IAAI;kBAACuJ,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAtB,QAAA,eACfnF,OAAA,CAAClD,GAAG;oBAACkI,EAAE,EAAE;sBAAEE,SAAS,EAAE,QAAQ;sBAAED,CAAC,EAAE,CAAC;sBAAEgC,OAAO,EAAE,YAAY;sBAAE4C,YAAY,EAAE;oBAAE,CAAE;oBAAA1E,QAAA,gBAC7EnF,OAAA,CAAChD,UAAU;sBAACyI,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEiB,UAAU,EAAE,GAAG;wBAAEP,KAAK,EAAE;sBAAe,CAAE;sBAAAP,QAAA,EACrEhB,cAAc,CAACmD,GAAG,CAACwC,gBAAgB;oBAAC;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACbxF,OAAA,CAAChD,UAAU;sBAACyI,OAAO,EAAC,SAAS;sBAACC,KAAK,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGPxF,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,gBACjBnF,OAAA,CAAClD,GAAG;kBAACkI,EAAE,EAAE;oBAAEW,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAET,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,gBACxDnF,OAAA,CAACrB,MAAM;oBAACqG,EAAE,EAAE;sBAAEe,EAAE,EAAE,CAAC;sBAAED,QAAQ,EAAE,EAAE;sBAAEJ,KAAK,EAAE;oBAAiB;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChExF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAAAN,QAAA,GAAC,aACf,EAACmC,GAAG,CAACvG,WAAW;kBAAA;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACbxF,OAAA,CAACpC,IAAI;oBACHoK,KAAK,EAAE,GAAGV,GAAG,CAACyC,2BAA2B,KAAM;oBAC/C3D,IAAI,EAAC,OAAO;oBACZpB,EAAE,EAAE;sBAAE4C,EAAE,EAAE;oBAAO;kBAAE;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENxF,OAAA,CAAClD,GAAG;kBAACkI,EAAE,EAAE;oBAAEW,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAET,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,gBACxDnF,OAAA,CAACV,KAAK;oBAAC0F,EAAE,EAAE;sBAAEe,EAAE,EAAE,CAAC;sBAAED,QAAQ,EAAE,EAAE;sBAAEJ,KAAK,EAAE;oBAAiB;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/DxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAAAN,QAAA,IAAA4D,qBAAA,GACxBzB,GAAG,CAACI,qBAAqB,cAAAqB,qBAAA,uBAAzBA,qBAAA,CAA2BvB,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;kBAAA;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAACV,EAAE,EAAE;sBAAE4C,EAAE,EAAE;oBAAE,CAAE;oBAAAzC,QAAA,GAAC,GACjE,GAAA6D,sBAAA,GAAC1B,GAAG,CAAC0C,gCAAgC,cAAAhB,sBAAA,uBAApCA,sBAAA,CAAsCxB,OAAO,CAAC,CAAC,CAAC,EAAC,SACrD;kBAAA;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENxF,OAAA,CAAClD,GAAG;kBAACkI,EAAE,EAAE;oBAAEW,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAV,QAAA,gBACjDnF,OAAA,CAACP,UAAU;oBAACuF,EAAE,EAAE;sBAAEe,EAAE,EAAE,CAAC;sBAAED,QAAQ,EAAE,EAAE;sBAAEJ,KAAK,EAAE;oBAAiB;kBAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpExF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAAAN,QAAA,GACxBmC,GAAG,CAAC2C,wBAAwB,EAAC,UAAG,EAAC3C,GAAG,CAAC4C,UAAU;kBAAA;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxF,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,gBACjBnF,OAAA,CAAClD,GAAG;kBAACkI,EAAE,EAAE;oBAAEW,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAER,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,gBACnEnF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbxF,OAAA,CAAChD,UAAU;oBAACyI,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,GACjDb,IAAI,CAAC6F,GAAG,CAAC,GAAG,EAAG7C,GAAG,CAAC0C,gCAAgC,GAAG,GAAG,GAAI,GAAG,CAAC,CAACxC,OAAO,CAAC,CAAC,CAAC,EAAC,GAChF;kBAAA;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxF,OAAA,CAAC5B,cAAc;kBACbqH,OAAO,EAAC,aAAa;kBACrBxC,KAAK,EAAEqB,IAAI,CAAC6F,GAAG,CAAC,GAAG,EAAG7C,GAAG,CAAC0C,gCAAgC,GAAG,GAAG,GAAI,GAAG,CAAE;kBACzEhF,EAAE,EAAE;oBACFmC,MAAM,EAAE,CAAC;oBACT0C,YAAY,EAAE,CAAC;oBACf5C,OAAO,EAAE,UAAU;oBACnB,0BAA0B,EAAE;sBAC1B4C,YAAY,EAAE,CAAC;sBACfxD,UAAU,EAAEiB,GAAG,CAAC0C,gCAAgC,GAAG,EAAE,GACjD,0CAA0C,GAC1C1C,GAAG,CAAC0C,gCAAgC,GAAG,EAAE,GACzC,0CAA0C,GAC1C;oBACN;kBACF;gBAAE;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGL8B,GAAG,CAAC8C,KAAK,iBACRpK,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEgC,OAAO,EAAE,SAAS;kBAAE4C,YAAY,EAAE,CAAC;kBAAEzE,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,eAC5DnF,OAAA,CAAChD,UAAU;kBAACyI,OAAO,EAAC,SAAS;kBAACC,KAAK,EAAC,gBAAgB;kBAACV,EAAE,EAAE;oBAAEqF,SAAS,EAAE;kBAAS,CAAE;kBAAAlF,QAAA,GAAC,IAC/E,EAACmC,GAAG,CAAC8C,KAAK,EAAC,IACd;gBAAA;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,eAGDxF,OAAA,CAAClD,GAAG;gBAACkI,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,UAAU;kBAAEiD,GAAG,EAAE;gBAAE,CAAE;gBAAA1D,QAAA,gBAC/DnF,OAAA,CAAC9B,OAAO;kBAACoL,KAAK,EAAC,UAAU;kBAAAnE,QAAA,eACvBnF,OAAA,CAAC/B,UAAU;oBACTiI,OAAO,EAAEA,CAAA,KAAMhG,MAAM,CAACoH,GAAG,CAAE;oBAC3B5B,KAAK,EAAC,SAAS;oBACfU,IAAI,EAAC,OAAO;oBAAAjB,QAAA,eAEZnF,OAAA,CAACf,IAAI;sBAAAoG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACVxF,OAAA,CAAC9B,OAAO;kBAACoL,KAAK,EAAC,SAAS;kBAAAnE,QAAA,eACtBnF,OAAA,CAAC/B,UAAU;oBACTiI,OAAO,EAAEA,CAAA,KAAM/F,QAAQ,CAACmH,GAAG,CAACgD,EAAE,CAAE;oBAChC5E,KAAK,EAAC,OAAO;oBACbU,IAAI,EAAC,OAAO;oBAAAjB,QAAA,eAEZnF,OAAA,CAACd,MAAM;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GArK6B8B,GAAG,CAACgD,EAAE;UAAAjF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsKtC,CAAC;MAAA,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,EAGAX,UAAU,GAAG,CAAC,iBACb7E,OAAA,CAAClD,GAAG;MAACkI,EAAE,EAAE;QAAEuF,EAAE,EAAE,CAAC;QAAE5E,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAT,QAAA,eAC5DnF,OAAA,CAACrC,KAAK;QAACiJ,SAAS,EAAE,CAAE;QAAC5B,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,eAChCnF,OAAA,CAAC1B,KAAK;UAACkM,SAAS,EAAC,KAAK;UAACjE,OAAO,EAAE,CAAE;UAACV,UAAU,EAAC,QAAQ;UAAAV,QAAA,gBACpDnF,OAAA,CAAChD,UAAU;YAACyI,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAP,QAAA,GAAC,YACvC,EAAC,CAACjE,UAAU,CAACE,IAAI,GAAG,CAAC,IAAIF,UAAU,CAACG,QAAQ,GAAG,CAAC,EAAC,GAC3D,EAACiD,IAAI,CAAC6F,GAAG,CAACjJ,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACG,QAAQ,EAAEH,UAAU,CAACI,WAAW,CAAC,EAAC,MAAI,EAACJ,UAAU,CAACI,WAAW;UAAA;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEbxF,OAAA,CAAC3B,UAAU;YACToM,KAAK,EAAE5F,UAAW;YAClBzD,IAAI,EAAEF,UAAU,CAACE,IAAK;YACtB+G,QAAQ,EAAEA,CAACuC,KAAK,EAAEzH,KAAK,KAAK9B,aAAa,CAACiC,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEhC,IAAI,EAAE6B;YAAM,CAAC,CAAC,CAAE;YAC9EyC,KAAK,EAAC,SAAS;YACfD,OAAO,EAAC,UAAU;YAClBkF,KAAK,EAAC,SAAS;YACfC,eAAe;YACfC,cAAc;UAAA;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnF,EAAA,CA1oBIJ,YAAY;EAAA,QACmCJ,OAAO;AAAA;AAAAiL,EAAA,GADtD7K,YAAY;AA4oBlB,eAAeA,YAAY;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}