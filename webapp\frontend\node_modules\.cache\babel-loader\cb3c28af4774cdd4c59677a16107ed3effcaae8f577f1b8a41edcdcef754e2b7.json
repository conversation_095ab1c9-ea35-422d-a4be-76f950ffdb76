{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\ProductivityMain.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Container, Typography, Tabs, Tab, Paper, Fab, Snackbar, Alert, Chip, Grid, Card, CardContent, IconButton, Tooltip } from '@mui/material';\nimport { Dashboard as DashboardIcon, Assignment as WorkLogIcon, TrendingUp as EstimationIcon, Add as AddIcon, Analytics as AnalyticsIcon, Speed as SpeedIcon, Timeline as TimelineIcon } from '@mui/icons-material';\nimport Dashboard from './Dashboard';\nimport WorkLogForm from './WorkLogForm';\nimport EstimationTool from './EstimationTool';\nimport WorkLogsList from './WorkLogsList';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductivityMain = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [showWorkLogForm, setShowWorkLogForm] = useState(false);\n  const [editingWorkLog, setEditingWorkLog] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const tabs = [{\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this),\n    description: 'Panoramica generale e statistiche',\n    color: '#1976d2'\n  }, {\n    id: 'worklogs',\n    label: 'Work Logs',\n    icon: /*#__PURE__*/_jsxDEV(WorkLogIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    description: 'Gestione registrazioni di lavoro',\n    color: '#388e3c'\n  }, {\n    id: 'estimation',\n    label: 'Stima Produttività',\n    icon: /*#__PURE__*/_jsxDEV(EstimationIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    description: 'Calcolo tempi e produttività',\n    color: '#f57c00'\n  }];\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification(null), 5000);\n  };\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setShowWorkLogForm(false);\n    setEditingWorkLog(null);\n  };\n  const handleCreateWorkLog = async workLogData => {\n    try {\n      await axiosInstance.post('/v1/work-logs', workLogData);\n      showNotification('Work log creato con successo!', 'success');\n      setShowWorkLogForm(false);\n\n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore nella creazione work log:', error);\n      showNotification(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore nella creazione del work log', 'error');\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n  const handleUpdateWorkLog = async workLogData => {\n    try {\n      await axiosInstance.put(`/v1/work-logs/${editingWorkLog.id}`, workLogData);\n      showNotification('Work log aggiornato con successo!', 'success');\n      setShowWorkLogForm(false);\n      setEditingWorkLog(null);\n\n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Errore nell\\'aggiornamento work log:', error);\n      showNotification(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Errore nell\\'aggiornamento del work log', 'error');\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n  const handleEditWorkLog = workLog => {\n    setEditingWorkLog(workLog);\n    setShowWorkLogForm(true);\n  };\n  const handleDeleteWorkLog = async workLogId => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo work log?')) {\n      return;\n    }\n    try {\n      await axiosInstance.delete(`/v1/work-logs/${workLogId}`);\n      showNotification('Work log eliminato con successo!', 'success');\n\n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Errore nell\\'eliminazione work log:', error);\n      showNotification(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Errore nell\\'eliminazione del work log', 'error');\n    }\n  };\n  const renderTabContent = () => {\n    if (showWorkLogForm) {\n      return /*#__PURE__*/_jsxDEV(WorkLogForm, {\n        initialData: editingWorkLog,\n        onSubmit: editingWorkLog ? handleUpdateWorkLog : handleCreateWorkLog,\n        onCancel: () => {\n          setShowWorkLogForm(false);\n          setEditingWorkLog(null);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this);\n    }\n    switch (activeTab) {\n      case 'dashboard':\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 16\n        }, this);\n      case 'worklogs':\n        return /*#__PURE__*/_jsxDEV(WorkLogsList, {\n          onEdit: handleEditWorkLog,\n          onDelete: handleDeleteWorkLog,\n          onCreateNew: () => setShowWorkLogForm(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this);\n      case 'estimation':\n        return /*#__PURE__*/_jsxDEV(EstimationTool, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"\\u26A1 Sistema Produttivit\\xE0 Cavi Elettrici\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: \"Gestione e analisi della produttivit\\xE0 per installazione cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), activeTab === 'worklogs' && !showWorkLogForm && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowWorkLogForm(true),\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium\",\n            children: \"\\u2795 Nuovo Work Log\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${notification.type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-2\",\n          children: notification.type === 'success' ? '✅' : '❌'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), notification.message, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setNotification(null),\n          className: \"ml-4 text-gray-400 hover:text-gray-600\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this), !showWorkLogForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex space-x-8\",\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: tab.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this), tab.label]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 9\n    }, this), showWorkLogForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex py-3 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowWorkLogForm(false);\n              setEditingWorkLog(null);\n            },\n            className: \"text-blue-600 hover:text-blue-800\",\n            children: \"\\uD83D\\uDCDD Work Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mx-2 text-gray-400\",\n            children: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: editingWorkLog ? 'Modifica Work Log' : 'Nuovo Work Log'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n      children: renderTabContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-white border-t mt-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Sistema Produttivit\\xE0 Cavi Elettrici - Versione 1.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1\",\n            children: \"Implementa le specifiche del progetto di produttivit\\xE0 secondo Task 1-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductivityMain, \"giiMiXNNEGTdYX6+e+aweL0IB0Q=\");\n_c = ProductivityMain;\nexport default ProductivityMain;\nvar _c;\n$RefreshReg$(_c, \"ProductivityMain\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Container", "Typography", "Tabs", "Tab", "Paper", "Fab", "Snackbar", "<PERSON><PERSON>", "Chip", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "Dashboard", "DashboardIcon", "Assignment", "WorkLogIcon", "TrendingUp", "EstimationIcon", "Add", "AddIcon", "Analytics", "AnalyticsIcon", "Speed", "SpeedIcon", "Timeline", "TimelineIcon", "WorkLogForm", "EstimationTool", "WorkLogsList", "axiosInstance", "jsxDEV", "_jsxDEV", "ProductivityMain", "_s", "activeTab", "setActiveTab", "showWorkLogForm", "setShowWorkLogForm", "editingWorkLog", "setEditingWorkLog", "notification", "setNotification", "tabs", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "color", "showNotification", "message", "type", "setTimeout", "handleTabChange", "event", "newValue", "handleCreateWorkLog", "workLogData", "post", "window", "location", "reload", "error", "_error$response", "_error$response$data", "console", "response", "data", "detail", "handleUpdateWorkLog", "put", "_error$response2", "_error$response2$data", "handleEditWorkLog", "workLog", "handleDeleteWorkLog", "workLogId", "confirm", "delete", "_error$response3", "_error$response3$data", "renderTabContent", "initialData", "onSubmit", "onCancel", "onEdit", "onDelete", "onCreateNew", "className", "children", "onClick", "map", "tab", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/ProductivityMain.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Tabs,\n  Tab,\n  Paper,\n  Fab,\n  Snackbar,\n  Alert,\n  Chip,\n  Grid,\n  Card,\n  CardContent,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  Assignment as WorkLogIcon,\n  TrendingUp as EstimationIcon,\n  Add as AddIcon,\n  Analytics as AnalyticsIcon,\n  Speed as SpeedIcon,\n  Timeline as TimelineIcon\n} from '@mui/icons-material';\nimport Dashboard from './Dashboard';\nimport WorkLogForm from './WorkLogForm';\nimport EstimationTool from './EstimationTool';\nimport WorkLogsList from './WorkLogsList';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst ProductivityMain = () => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [showWorkLogForm, setShowWorkLogForm] = useState(false);\n  const [editingWorkLog, setEditingWorkLog] = useState(null);\n  const [notification, setNotification] = useState(null);\n\n  const tabs = [\n    {\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: <DashboardIcon />,\n      description: 'Panoramica generale e statistiche',\n      color: '#1976d2'\n    },\n    {\n      id: 'worklogs',\n      label: 'Work Logs',\n      icon: <WorkLogIcon />,\n      description: 'Gestione registrazioni di lavoro',\n      color: '#388e3c'\n    },\n    {\n      id: 'estimation',\n      label: 'Stima Produttività',\n      icon: <EstimationIcon />,\n      description: 'Calcolo tempi e produttività',\n      color: '#f57c00'\n    },\n  ];\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setShowWorkLogForm(false);\n    setEditingWorkLog(null);\n  };\n\n  const handleCreateWorkLog = async (workLogData) => {\n    try {\n      await axiosInstance.post('/v1/work-logs', workLogData);\n      showNotification('Work log creato con successo!', 'success');\n      setShowWorkLogForm(false);\n      \n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      console.error('Errore nella creazione work log:', error);\n      showNotification(\n        error.response?.data?.detail || 'Errore nella creazione del work log',\n        'error'\n      );\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n\n  const handleUpdateWorkLog = async (workLogData) => {\n    try {\n      await axiosInstance.put(`/v1/work-logs/${editingWorkLog.id}`, workLogData);\n      showNotification('Work log aggiornato con successo!', 'success');\n      setShowWorkLogForm(false);\n      setEditingWorkLog(null);\n      \n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento work log:', error);\n      showNotification(\n        error.response?.data?.detail || 'Errore nell\\'aggiornamento del work log',\n        'error'\n      );\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n\n  const handleEditWorkLog = (workLog) => {\n    setEditingWorkLog(workLog);\n    setShowWorkLogForm(true);\n  };\n\n  const handleDeleteWorkLog = async (workLogId) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo work log?')) {\n      return;\n    }\n\n    try {\n      await axiosInstance.delete(`/v1/work-logs/${workLogId}`);\n      showNotification('Work log eliminato con successo!', 'success');\n      \n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      console.error('Errore nell\\'eliminazione work log:', error);\n      showNotification(\n        error.response?.data?.detail || 'Errore nell\\'eliminazione del work log',\n        'error'\n      );\n    }\n  };\n\n  const renderTabContent = () => {\n    if (showWorkLogForm) {\n      return (\n        <WorkLogForm\n          initialData={editingWorkLog}\n          onSubmit={editingWorkLog ? handleUpdateWorkLog : handleCreateWorkLog}\n          onCancel={() => {\n            setShowWorkLogForm(false);\n            setEditingWorkLog(null);\n          }}\n        />\n      );\n    }\n\n    switch (activeTab) {\n      case 'dashboard':\n        return <Dashboard />;\n      \n      case 'worklogs':\n        return (\n          <WorkLogsList\n            onEdit={handleEditWorkLog}\n            onDelete={handleDeleteWorkLog}\n            onCreateNew={() => setShowWorkLogForm(true)}\n          />\n        );\n      \n      case 'estimation':\n        return <EstimationTool />;\n      \n      default:\n        return <Dashboard />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                ⚡ Sistema Produttività Cavi Elettrici\n              </h1>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Gestione e analisi della produttività per installazione cavi\n              </p>\n            </div>\n            \n            {/* Pulsante Nuovo Work Log */}\n            {activeTab === 'worklogs' && !showWorkLogForm && (\n              <button\n                onClick={() => setShowWorkLogForm(true)}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium\"\n              >\n                ➕ Nuovo Work Log\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Notification */}\n      {notification && (\n        <div className={`fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${\n          notification.type === 'success' \n            ? 'bg-green-100 border border-green-400 text-green-700' \n            : 'bg-red-100 border border-red-400 text-red-700'\n        }`}>\n          <div className=\"flex items-center\">\n            <span className=\"mr-2\">\n              {notification.type === 'success' ? '✅' : '❌'}\n            </span>\n            {notification.message}\n            <button\n              onClick={() => setNotification(null)}\n              className=\"ml-4 text-gray-400 hover:text-gray-600\"\n            >\n              ✕\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Navigation Tabs */}\n      {!showWorkLogForm && (\n        <div className=\"bg-white border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <nav className=\"flex space-x-8\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <span className=\"mr-2\">{tab.icon}</span>\n                  {tab.label}\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n      )}\n\n      {/* Breadcrumb per Work Log Form */}\n      {showWorkLogForm && (\n        <div className=\"bg-white border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <nav className=\"flex py-3 text-sm\">\n              <button\n                onClick={() => {\n                  setShowWorkLogForm(false);\n                  setEditingWorkLog(null);\n                }}\n                className=\"text-blue-600 hover:text-blue-800\"\n              >\n                📝 Work Logs\n              </button>\n              <span className=\"mx-2 text-gray-400\">/</span>\n              <span className=\"text-gray-600\">\n                {editingWorkLog ? 'Modifica Work Log' : 'Nuovo Work Log'}\n              </span>\n            </nav>\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {renderTabContent()}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t mt-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"text-center text-sm text-gray-500\">\n            <p>Sistema Produttività Cavi Elettrici - Versione 1.0</p>\n            <p className=\"mt-1\">\n              Implementa le specifiche del progetto di produttività secondo Task 1-3\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default ProductivityMain;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,WAAW,EACzBC,UAAU,IAAIC,cAAc,EAC5BC,GAAG,IAAIC,OAAO,EACdC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOb,SAAS,MAAM,aAAa;AACnC,OAAOc,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM8C,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,WAAW;IAClBC,IAAI,eAAEd,OAAA,CAAClB,aAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,WAAW,EAAE,mCAAmC;IAChDC,KAAK,EAAE;EACT,CAAC,EACD;IACER,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,WAAW;IAClBC,IAAI,eAAEd,OAAA,CAAChB,WAAW;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,WAAW,EAAE,kCAAkC;IAC/CC,KAAK,EAAE;EACT,CAAC,EACD;IACER,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,eAAEd,OAAA,CAACd,cAAc;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,WAAW,EAAE,8BAA8B;IAC3CC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDb,eAAe,CAAC;MAAEY,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCC,UAAU,CAAC,MAAMd,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMe,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CvB,YAAY,CAACuB,QAAQ,CAAC;IACtBrB,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD,IAAI;MACF,MAAM/B,aAAa,CAACgC,IAAI,CAAC,eAAe,EAAED,WAAW,CAAC;MACtDR,gBAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;MAC5Df,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACA,IAAIH,SAAS,KAAK,UAAU,EAAE;QAC5B4B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDb,gBAAgB,CACd,EAAAc,eAAA,GAAAD,KAAK,CAACI,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,KAAI,qCAAqC,EACrE,OACF,CAAC;MACD,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMO,mBAAmB,GAAG,MAAOZ,WAAW,IAAK;IACjD,IAAI;MACF,MAAM/B,aAAa,CAAC4C,GAAG,CAAC,iBAAiBnC,cAAc,CAACK,EAAE,EAAE,EAAEiB,WAAW,CAAC;MAC1ER,gBAAgB,CAAC,mCAAmC,EAAE,SAAS,CAAC;MAChEf,kBAAkB,CAAC,KAAK,CAAC;MACzBE,iBAAiB,CAAC,IAAI,CAAC;;MAEvB;MACA,IAAIL,SAAS,KAAK,UAAU,EAAE;QAC5B4B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAS,gBAAA,EAAAC,qBAAA;MACdP,OAAO,CAACH,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5Db,gBAAgB,CACd,EAAAsB,gBAAA,GAAAT,KAAK,CAACI,QAAQ,cAAAK,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI,yCAAyC,EACzE,OACF,CAAC;MACD,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMW,iBAAiB,GAAIC,OAAO,IAAK;IACrCtC,iBAAiB,CAACsC,OAAO,CAAC;IAC1BxC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyC,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI,CAACjB,MAAM,CAACkB,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACrE;IACF;IAEA,IAAI;MACF,MAAMnD,aAAa,CAACoD,MAAM,CAAC,iBAAiBF,SAAS,EAAE,CAAC;MACxD3B,gBAAgB,CAAC,kCAAkC,EAAE,SAAS,CAAC;;MAE/D;MACA,IAAIlB,SAAS,KAAK,UAAU,EAAE;QAC5B4B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACdf,OAAO,CAACH,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3Db,gBAAgB,CACd,EAAA8B,gBAAA,GAAAjB,KAAK,CAACI,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBZ,MAAM,KAAI,wCAAwC,EACxE,OACF,CAAC;IACH;EACF,CAAC;EAED,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhD,eAAe,EAAE;MACnB,oBACEL,OAAA,CAACL,WAAW;QACV2D,WAAW,EAAE/C,cAAe;QAC5BgD,QAAQ,EAAEhD,cAAc,GAAGkC,mBAAmB,GAAGb,mBAAoB;QACrE4B,QAAQ,EAAEA,CAAA,KAAM;UACdlD,kBAAkB,CAAC,KAAK,CAAC;UACzBE,iBAAiB,CAAC,IAAI,CAAC;QACzB;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEN;IAEA,QAAQf,SAAS;MACf,KAAK,WAAW;QACd,oBAAOH,OAAA,CAACnB,SAAS;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAEtB,KAAK,UAAU;QACb,oBACElB,OAAA,CAACH,YAAY;UACX4D,MAAM,EAAEZ,iBAAkB;UAC1Ba,QAAQ,EAAEX,mBAAoB;UAC9BY,WAAW,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC,IAAI;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAGN,KAAK,YAAY;QACf,oBAAOlB,OAAA,CAACJ,cAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE3B;QACE,oBAAOlB,OAAA,CAACnB,SAAS;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,oBACElB,OAAA;IAAK4D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC7D,OAAA;MAAK4D,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C7D,OAAA;QAAK4D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD7D,OAAA;UAAK4D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD7D,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAI4D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAEjD;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlB,OAAA;cAAG4D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAGLf,SAAS,KAAK,UAAU,IAAI,CAACE,eAAe,iBAC3CL,OAAA;YACE8D,OAAO,EAAEA,CAAA,KAAMxD,kBAAkB,CAAC,IAAI,CAAE;YACxCsD,SAAS,EAAC,+HAA+H;YAAAC,QAAA,EAC1I;UAED;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLT,YAAY,iBACXT,OAAA;MAAK4D,SAAS,EAAE,qDACdnD,YAAY,CAACc,IAAI,KAAK,SAAS,GAC3B,qDAAqD,GACrD,+CAA+C,EAClD;MAAAsC,QAAA,eACD7D,OAAA;QAAK4D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7D,OAAA;UAAM4D,SAAS,EAAC,MAAM;UAAAC,QAAA,EACnBpD,YAAY,CAACc,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,EACNT,YAAY,CAACa,OAAO,eACrBtB,OAAA;UACE8D,OAAO,EAAEA,CAAA,KAAMpD,eAAe,CAAC,IAAI,CAAE;UACrCkD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EACnD;QAED;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAACb,eAAe,iBACfL,OAAA;MAAK4D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC7D,OAAA;QAAK4D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD7D,OAAA;UAAK4D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BlD,IAAI,CAACoD,GAAG,CAAEC,GAAG,iBACZhE,OAAA;YAEE8D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC4D,GAAG,CAACpD,EAAE,CAAE;YACpCgD,SAAS,EAAE,4CACTzD,SAAS,KAAK6D,GAAG,CAACpD,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,EAC/E;YAAAiD,QAAA,gBAEH7D,OAAA;cAAM4D,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEG,GAAG,CAAClD;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACvC8C,GAAG,CAACnD,KAAK;UAAA,GATLmD,GAAG,CAACpD,EAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAb,eAAe,iBACdL,OAAA;MAAK4D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC7D,OAAA;QAAK4D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD7D,OAAA;UAAK4D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7D,OAAA;YACE8D,OAAO,EAAEA,CAAA,KAAM;cACbxD,kBAAkB,CAAC,KAAK,CAAC;cACzBE,iBAAiB,CAAC,IAAI,CAAC;YACzB,CAAE;YACFoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9C;UAED;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlB,OAAA;YAAM4D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAC;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7ClB,OAAA;YAAM4D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC5BtD,cAAc,GAAG,mBAAmB,GAAG;UAAgB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlB,OAAA;MAAM4D,SAAS,EAAC,6CAA6C;MAAAC,QAAA,EAC1DR,gBAAgB,CAAC;IAAC;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGPlB,OAAA;MAAQ4D,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACzC7D,OAAA;QAAK4D,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D7D,OAAA;UAAK4D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7D,OAAA;YAAA6D,QAAA,EAAG;UAAkD;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzDlB,OAAA;YAAG4D,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAEpB;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChB,EAAA,CAnQID,gBAAgB;AAAAgE,EAAA,GAAhBhE,gBAAgB;AAqQtB,eAAeA,gBAAgB;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}