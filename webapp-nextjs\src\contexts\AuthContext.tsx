'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User, Cantier<PERSON> } from '@/types'
import { authApi } from '@/lib/api'

interface AuthContextType {
  user: User | null
  cantiere: Cantiere | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (username: string, password: string) => Promise<void>
  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [cantiere, setCantiere] = useState<Cantiere | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user || !!cantiere

  // Verifica l'autenticazione al caricamento
  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        setIsLoading(false)
        return
      }

      const response = await authApi.verifyToken()
      
      if (response.user) {
        setUser(response.user)
      } else if (response.cantiere) {
        setCantiere(response.cantiere)
      }
    } catch (error) {
      console.error('Errore verifica autenticazione:', error)
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_data')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.login({ username, password })
      
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('user_data', JSON.stringify(response.user))
      
      setUser(response.user)
      setCantiere(null)
    } catch (error) {
      console.error('Errore login:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })
      
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('cantiere_data', JSON.stringify(response.cantiere))
      
      setCantiere(response.cantiere)
      setUser(null)
    } catch (error) {
      console.error('Errore login cantiere:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('user_data')
    localStorage.removeItem('cantiere_data')
    setUser(null)
    setCantiere(null)
    window.location.href = '/login'
  }

  const value: AuthContextType = {
    user,
    cantiere,
    isAuthenticated,
    isLoading,
    login,
    loginCantiere,
    logout,
    checkAuth,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
