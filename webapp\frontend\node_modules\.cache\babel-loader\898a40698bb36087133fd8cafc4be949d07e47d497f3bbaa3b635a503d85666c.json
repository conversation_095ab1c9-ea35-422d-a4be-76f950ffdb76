{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\WorkLogForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, TextField, Select, MenuItem, FormControl, InputLabel, Button, Paper, Chip, Avatar, Divider, Alert, CircularProgress, Stepper, Step, StepLabel, StepContent } from '@mui/material';\nimport { Assignment, Person, Schedule, Engineering, Save, Cancel, CheckCircle } from '@mui/icons-material';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkLogForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    operator_id: '',\n    cable_type_id: '',\n    activity_type: 'Posa',\n    sub_activity_detail: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    quantity: '',\n    start_timestamp: '',\n    end_timestamp: '',\n    number_of_operators_on_task: 1,\n    notes: '',\n    id_cantiere: ''\n  });\n  const [operators, setOperators] = useState([]);\n  const [cableTypes, setCableTypes] = useState([]);\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [{\n    value: 'Posa',\n    label: 'Posa'\n  }, {\n    value: 'Collegamento',\n    label: 'Collegamento'\n  }, {\n    value: 'Certificazione',\n    label: 'Certificazione'\n  }];\n  const environmentalConditions = [{\n    value: 'Normale',\n    label: 'Normale'\n  }, {\n    value: 'Spazi Ristretti',\n    label: 'Spazi Ristretti'\n  }, {\n    value: 'In Altezza',\n    label: 'In Altezza'\n  }, {\n    value: 'Esterno',\n    label: 'Esterno'\n  }];\n  const toolsUsed = [{\n    value: 'Manuale',\n    label: 'Manuale'\n  }, {\n    value: 'Automatico',\n    label: 'Automatico'\n  }];\n  useEffect(() => {\n    loadInitialData();\n    if (initialData) {\n      setFormData({\n        ...initialData,\n        start_timestamp: formatDateTimeLocal(initialData.start_timestamp),\n        end_timestamp: formatDateTimeLocal(initialData.end_timestamp)\n      });\n    }\n  }, [initialData]);\n  const formatDateTimeLocal = dateString => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toISOString().slice(0, 16);\n  };\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n\n      // Carica operatori, tipi di cavo e cantieri in parallelo\n      const [operatorsRes, cableTypesRes, cantieriRes] = await Promise.all([axiosInstance.get('/responsabili'), axiosInstance.get('/admin/tipologie-cavi'), axiosInstance.get('/cantieri')]);\n      setOperators(operatorsRes.data || []);\n      setCableTypes(cableTypesRes.data || []);\n      setCantieri(cantieriRes.data || []);\n\n      // Se c'è solo un cantiere, selezionalo automaticamente\n      if (cantieriRes.data && cantieriRes.data.length === 1) {\n        setFormData(prev => ({\n          ...prev,\n          id_cantiere: cantieriRes.data[0].id_cantiere\n        }));\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n      setErrors({\n        general: 'Errore nel caricamento dei dati'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n\n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.operator_id) newErrors.operator_id = 'Operatore richiesto';\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Quantità deve essere maggiore di 0';\n    if (!formData.start_timestamp) newErrors.start_timestamp = 'Data/ora inizio richiesta';\n    if (!formData.end_timestamp) newErrors.end_timestamp = 'Data/ora fine richiesta';\n    if (!formData.id_cantiere) newErrors.id_cantiere = 'Cantiere richiesto';\n    if (!formData.number_of_operators_on_task || formData.number_of_operators_on_task < 1) {\n      newErrors.number_of_operators_on_task = 'Numero operatori deve essere almeno 1';\n    }\n\n    // Valida che end_timestamp sia dopo start_timestamp\n    if (formData.start_timestamp && formData.end_timestamp) {\n      const start = new Date(formData.start_timestamp);\n      const end = new Date(formData.end_timestamp);\n      if (end <= start) {\n        newErrors.end_timestamp = 'Data/ora fine deve essere dopo inizio';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Prepara i dati per l'invio\n      const submitData = {\n        ...formData,\n        operator_id: parseInt(formData.operator_id),\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        quantity: parseFloat(formData.quantity),\n        number_of_operators_on_task: parseInt(formData.number_of_operators_on_task),\n        id_cantiere: parseInt(formData.id_cantiere),\n        start_timestamp: new Date(formData.start_timestamp).toISOString(),\n        end_timestamp: new Date(formData.end_timestamp).toISOString()\n      };\n      await onSubmit(submitData);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore nell\\'invio del form:', error);\n      setErrors({\n        general: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore nell\\'invio del work log'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading && !operators.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: \"Caricamento...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-800 mb-6\",\n      children: initialData ? 'Modifica Work Log' : 'Nuovo Work Log'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Operatore *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"operator_id\",\n            value: formData.operator_id,\n            onChange: handleInputChange,\n            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.operator_id ? 'border-red-500' : 'border-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Seleziona operatore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), operators.map(op => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: op.id_responsabile,\n              children: [op.nome_responsabile, \" (\", op.experience_level || 'Senior', \")\"]\n            }, op.id_responsabile, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), errors.operator_id && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.operator_id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Cantiere *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"id_cantiere\",\n            value: formData.id_cantiere,\n            onChange: handleInputChange,\n            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.id_cantiere ? 'border-red-500' : 'border-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Seleziona cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cantiere.id_cantiere,\n              children: cantiere.commessa\n            }, cantiere.id_cantiere, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), errors.id_cantiere && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.id_cantiere\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Tipo Attivit\\xE0 *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"activity_type\",\n            value: formData.activity_type,\n            onChange: handleInputChange,\n            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.activity_type ? 'border-red-500' : 'border-gray-300'}`,\n            children: activityTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: type.value,\n              children: type.label\n            }, type.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), errors.activity_type && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.activity_type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Tipo di Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"cable_type_id\",\n            value: formData.cable_type_id,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Seleziona tipo di cavo (opzionale)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), cableTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: type.id_tipologia,\n              children: [type.codice_prodotto, \" - \", type.nome_commerciale]\n            }, type.id_tipologia, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Dettaglio Attivit\\xE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"sub_activity_detail\",\n            value: formData.sub_activity_detail,\n            onChange: handleInputChange,\n            placeholder: \"es. Posa in canalina a vista\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: [\"Quantit\\xE0 * \", formData.activity_type === 'Posa' ? '(metri)' : '(unità)']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"quantity\",\n            value: formData.quantity,\n            onChange: handleInputChange,\n            step: \"0.1\",\n            min: \"0\",\n            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.quantity ? 'border-red-500' : 'border-gray-300'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), errors.quantity && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.quantity\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Condizioni Ambientali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"environmental_conditions\",\n            value: formData.environmental_conditions,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: environmentalConditions.map(condition => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: condition.value,\n              children: condition.label\n            }, condition.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Strumenti Utilizzati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"tools_used\",\n            value: formData.tools_used,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: toolsUsed.map(tool => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: tool.value,\n              children: tool.label\n            }, tool.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Data/Ora Inizio *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"datetime-local\",\n            name: \"start_timestamp\",\n            value: formData.start_timestamp,\n            onChange: handleInputChange,\n            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.start_timestamp ? 'border-red-500' : 'border-gray-300'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), errors.start_timestamp && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.start_timestamp\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 40\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Data/Ora Fine *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"datetime-local\",\n            name: \"end_timestamp\",\n            value: formData.end_timestamp,\n            onChange: handleInputChange,\n            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.end_timestamp ? 'border-red-500' : 'border-gray-300'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), errors.end_timestamp && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.end_timestamp\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Numero Operatori *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"number_of_operators_on_task\",\n            value: formData.number_of_operators_on_task,\n            onChange: handleInputChange,\n            min: \"1\",\n            className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.number_of_operators_on_task ? 'border-red-500' : 'border-gray-300'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), errors.number_of_operators_on_task && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm mt-1\",\n            children: errors.number_of_operators_on_task\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 52\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Note\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"notes\",\n          value: formData.notes,\n          onChange: handleInputChange,\n          rows: \"3\",\n          placeholder: \"Note aggiuntive sul lavoro svolto...\",\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-4 pt-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\",\n          children: loading ? 'Salvando...' : initialData ? 'Aggiorna' : 'Crea Work Log'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkLogForm, \"hJ+MEyMZs3QRaBnrGuytC3pSiuU=\");\n_c = WorkLogForm;\nexport default WorkLogForm;\nvar _c;\n$RefreshReg$(_c, \"WorkLogForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Paper", "Chip", "Avatar", "Divider", "<PERSON><PERSON>", "CircularProgress", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Assignment", "Person", "Schedule", "Engineering", "Save", "Cancel", "CheckCircle", "axiosInstance", "jsxDEV", "_jsxDEV", "WorkLogForm", "onSubmit", "onCancel", "initialData", "_s", "formData", "setFormData", "operator_id", "cable_type_id", "activity_type", "sub_activity_detail", "environmental_conditions", "tools_used", "quantity", "start_timestamp", "end_timestamp", "number_of_operators_on_task", "notes", "id_cantiere", "operators", "setOperators", "cableTypes", "setCableTypes", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "errors", "setErrors", "activityTypes", "value", "label", "environmentalConditions", "toolsUsed", "loadInitialData", "formatDateTimeLocal", "dateString", "date", "Date", "toISOString", "slice", "operatorsRes", "cableTypesRes", "cantieriRes", "Promise", "all", "get", "data", "length", "prev", "error", "console", "general", "handleInputChange", "e", "name", "type", "target", "parseFloat", "validateForm", "newErrors", "start", "end", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "parseInt", "_error$response", "_error$response$data", "response", "detail", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "map", "op", "id_responsabile", "nome_responsabile", "experience_level", "cantiere", "commessa", "id_tipologia", "codice_prodotto", "nome_commerciale", "placeholder", "step", "min", "condition", "tool", "rows", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/WorkLogForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Button,\n  Paper,\n  Chip,\n  Avatar,\n  Divider,\n  Alert,\n  CircularProgress,\n  <PERSON><PERSON>,\n  <PERSON>,\n  StepLabel,\n  StepContent\n} from '@mui/material';\nimport {\n  Assignment,\n  Person,\n  Schedule,\n  Engineering,\n  Save,\n  Cancel,\n  CheckCircle\n} from '@mui/icons-material';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst WorkLogForm = ({ onSubmit, onCancel, initialData = null }) => {\n  const [formData, setFormData] = useState({\n    operator_id: '',\n    cable_type_id: '',\n    activity_type: 'Posa',\n    sub_activity_detail: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    quantity: '',\n    start_timestamp: '',\n    end_timestamp: '',\n    number_of_operators_on_task: 1,\n    notes: '',\n    id_cantiere: ''\n  });\n\n  const [operators, setOperators] = useState([]);\n  const [cableTypes, setCableTypes] = useState([]);\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [\n    { value: 'Posa', label: 'Posa' },\n    { value: 'Collegamento', label: 'Collegamento' },\n    { value: 'Certificazione', label: 'Certificazione' }\n  ];\n\n  const environmentalConditions = [\n    { value: 'Normale', label: 'Normale' },\n    { value: 'Spazi Ristretti', label: 'Spazi Ristretti' },\n    { value: 'In Altezza', label: 'In Altezza' },\n    { value: 'Esterno', label: 'Esterno' }\n  ];\n\n  const toolsUsed = [\n    { value: 'Manuale', label: 'Manuale' },\n    { value: 'Automatico', label: 'Automatico' }\n  ];\n\n  useEffect(() => {\n    loadInitialData();\n    if (initialData) {\n      setFormData({\n        ...initialData,\n        start_timestamp: formatDateTimeLocal(initialData.start_timestamp),\n        end_timestamp: formatDateTimeLocal(initialData.end_timestamp)\n      });\n    }\n  }, [initialData]);\n\n  const formatDateTimeLocal = (dateString) => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toISOString().slice(0, 16);\n  };\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      \n      // Carica operatori, tipi di cavo e cantieri in parallelo\n      const [operatorsRes, cableTypesRes, cantieriRes] = await Promise.all([\n        axiosInstance.get('/responsabili'),\n        axiosInstance.get('/admin/tipologie-cavi'),\n        axiosInstance.get('/cantieri')\n      ]);\n\n      setOperators(operatorsRes.data || []);\n      setCableTypes(cableTypesRes.data || []);\n      setCantieri(cantieriRes.data || []);\n\n      // Se c'è solo un cantiere, selezionalo automaticamente\n      if (cantieriRes.data && cantieriRes.data.length === 1) {\n        setFormData(prev => ({ ...prev, id_cantiere: cantieriRes.data[0].id_cantiere }));\n      }\n\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n      setErrors({ general: 'Errore nel caricamento dei dati' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n    \n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: null }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.operator_id) newErrors.operator_id = 'Operatore richiesto';\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Quantità deve essere maggiore di 0';\n    if (!formData.start_timestamp) newErrors.start_timestamp = 'Data/ora inizio richiesta';\n    if (!formData.end_timestamp) newErrors.end_timestamp = 'Data/ora fine richiesta';\n    if (!formData.id_cantiere) newErrors.id_cantiere = 'Cantiere richiesto';\n    if (!formData.number_of_operators_on_task || formData.number_of_operators_on_task < 1) {\n      newErrors.number_of_operators_on_task = 'Numero operatori deve essere almeno 1';\n    }\n\n    // Valida che end_timestamp sia dopo start_timestamp\n    if (formData.start_timestamp && formData.end_timestamp) {\n      const start = new Date(formData.start_timestamp);\n      const end = new Date(formData.end_timestamp);\n      if (end <= start) {\n        newErrors.end_timestamp = 'Data/ora fine deve essere dopo inizio';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      // Prepara i dati per l'invio\n      const submitData = {\n        ...formData,\n        operator_id: parseInt(formData.operator_id),\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        quantity: parseFloat(formData.quantity),\n        number_of_operators_on_task: parseInt(formData.number_of_operators_on_task),\n        id_cantiere: parseInt(formData.id_cantiere),\n        start_timestamp: new Date(formData.start_timestamp).toISOString(),\n        end_timestamp: new Date(formData.end_timestamp).toISOString()\n      };\n\n      await onSubmit(submitData);\n      \n    } catch (error) {\n      console.error('Errore nell\\'invio del form:', error);\n      setErrors({ \n        general: error.response?.data?.detail || 'Errore nell\\'invio del work log' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading && !operators.length) {\n    return (\n      <div className=\"flex justify-center items-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n        <span className=\"ml-2\">Caricamento...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto\">\n      <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">\n        {initialData ? 'Modifica Work Log' : 'Nuovo Work Log'}\n      </h2>\n\n      {errors.general && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          {errors.general}\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Operatore */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Operatore *\n            </label>\n            <select\n              name=\"operator_id\"\n              value={formData.operator_id}\n              onChange={handleInputChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.operator_id ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">Seleziona operatore</option>\n              {operators.map(op => (\n                <option key={op.id_responsabile} value={op.id_responsabile}>\n                  {op.nome_responsabile} ({op.experience_level || 'Senior'})\n                </option>\n              ))}\n            </select>\n            {errors.operator_id && <p className=\"text-red-500 text-sm mt-1\">{errors.operator_id}</p>}\n          </div>\n\n          {/* Cantiere */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Cantiere *\n            </label>\n            <select\n              name=\"id_cantiere\"\n              value={formData.id_cantiere}\n              onChange={handleInputChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.id_cantiere ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">Seleziona cantiere</option>\n              {cantieri.map(cantiere => (\n                <option key={cantiere.id_cantiere} value={cantiere.id_cantiere}>\n                  {cantiere.commessa}\n                </option>\n              ))}\n            </select>\n            {errors.id_cantiere && <p className=\"text-red-500 text-sm mt-1\">{errors.id_cantiere}</p>}\n          </div>\n\n          {/* Tipo di Attività */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Tipo Attività *\n            </label>\n            <select\n              name=\"activity_type\"\n              value={formData.activity_type}\n              onChange={handleInputChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.activity_type ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              {activityTypes.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n            {errors.activity_type && <p className=\"text-red-500 text-sm mt-1\">{errors.activity_type}</p>}\n          </div>\n\n          {/* Tipo di Cavo */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Tipo di Cavo\n            </label>\n            <select\n              name=\"cable_type_id\"\n              value={formData.cable_type_id}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Seleziona tipo di cavo (opzionale)</option>\n              {cableTypes.map(type => (\n                <option key={type.id_tipologia} value={type.id_tipologia}>\n                  {type.codice_prodotto} - {type.nome_commerciale}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Dettaglio Sub-Attività */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Dettaglio Attività\n            </label>\n            <input\n              type=\"text\"\n              name=\"sub_activity_detail\"\n              value={formData.sub_activity_detail}\n              onChange={handleInputChange}\n              placeholder=\"es. Posa in canalina a vista\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          {/* Quantità */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Quantità * {formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}\n            </label>\n            <input\n              type=\"number\"\n              name=\"quantity\"\n              value={formData.quantity}\n              onChange={handleInputChange}\n              step=\"0.1\"\n              min=\"0\"\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.quantity ? 'border-red-500' : 'border-gray-300'\n              }`}\n            />\n            {errors.quantity && <p className=\"text-red-500 text-sm mt-1\">{errors.quantity}</p>}\n          </div>\n\n          {/* Condizioni Ambientali */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Condizioni Ambientali\n            </label>\n            <select\n              name=\"environmental_conditions\"\n              value={formData.environmental_conditions}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {environmentalConditions.map(condition => (\n                <option key={condition.value} value={condition.value}>\n                  {condition.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Strumenti Utilizzati */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Strumenti Utilizzati\n            </label>\n            <select\n              name=\"tools_used\"\n              value={formData.tools_used}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {toolsUsed.map(tool => (\n                <option key={tool.value} value={tool.value}>\n                  {tool.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Data/Ora Inizio */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Data/Ora Inizio *\n            </label>\n            <input\n              type=\"datetime-local\"\n              name=\"start_timestamp\"\n              value={formData.start_timestamp}\n              onChange={handleInputChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.start_timestamp ? 'border-red-500' : 'border-gray-300'\n              }`}\n            />\n            {errors.start_timestamp && <p className=\"text-red-500 text-sm mt-1\">{errors.start_timestamp}</p>}\n          </div>\n\n          {/* Data/Ora Fine */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Data/Ora Fine *\n            </label>\n            <input\n              type=\"datetime-local\"\n              name=\"end_timestamp\"\n              value={formData.end_timestamp}\n              onChange={handleInputChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.end_timestamp ? 'border-red-500' : 'border-gray-300'\n              }`}\n            />\n            {errors.end_timestamp && <p className=\"text-red-500 text-sm mt-1\">{errors.end_timestamp}</p>}\n          </div>\n\n          {/* Numero Operatori */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Numero Operatori *\n            </label>\n            <input\n              type=\"number\"\n              name=\"number_of_operators_on_task\"\n              value={formData.number_of_operators_on_task}\n              onChange={handleInputChange}\n              min=\"1\"\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                errors.number_of_operators_on_task ? 'border-red-500' : 'border-gray-300'\n              }`}\n            />\n            {errors.number_of_operators_on_task && <p className=\"text-red-500 text-sm mt-1\">{errors.number_of_operators_on_task}</p>}\n          </div>\n        </div>\n\n        {/* Note */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Note\n          </label>\n          <textarea\n            name=\"notes\"\n            value={formData.notes}\n            onChange={handleInputChange}\n            rows=\"3\"\n            placeholder=\"Note aggiuntive sul lavoro svolto...\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n        </div>\n\n        <div className=\"flex justify-end space-x-4 pt-6\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            Annulla\n          </button>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\"\n          >\n            {loading ? 'Salvando...' : (initialData ? 'Aggiorna' : 'Crea Work Log')}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default WorkLogForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,WAAW,QACN,eAAe;AACtB,SACEC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,QACN,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC;IACvC0C,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,MAAM;IACrBC,mBAAmB,EAAE,EAAE;IACvBC,wBAAwB,EAAE,SAAS;IACnCC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE,EAAE;IACjBC,2BAA2B,EAAE,CAAC;IAC9BC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4D,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8D,MAAM,EAAEC,SAAS,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMgE,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,EAChD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACrD;EAED,MAAMC,uBAAuB,GAAG,CAC9B;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACvC;EAED,MAAME,SAAS,GAAG,CAChB;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,CAC7C;EAEDjE,SAAS,CAAC,MAAM;IACdoE,eAAe,CAAC,CAAC;IACjB,IAAI/B,WAAW,EAAE;MACfG,WAAW,CAAC;QACV,GAAGH,WAAW;QACdW,eAAe,EAAEqB,mBAAmB,CAAChC,WAAW,CAACW,eAAe,CAAC;QACjEC,aAAa,EAAEoB,mBAAmB,CAAChC,WAAW,CAACY,aAAa;MAC9D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;EAEjB,MAAMgC,mBAAmB,GAAIC,UAAU,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;EAED,MAAMN,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACe,YAAY,EAAEC,aAAa,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnEhD,aAAa,CAACiD,GAAG,CAAC,eAAe,CAAC,EAClCjD,aAAa,CAACiD,GAAG,CAAC,uBAAuB,CAAC,EAC1CjD,aAAa,CAACiD,GAAG,CAAC,WAAW,CAAC,CAC/B,CAAC;MAEF1B,YAAY,CAACqB,YAAY,CAACM,IAAI,IAAI,EAAE,CAAC;MACrCzB,aAAa,CAACoB,aAAa,CAACK,IAAI,IAAI,EAAE,CAAC;MACvCvB,WAAW,CAACmB,WAAW,CAACI,IAAI,IAAI,EAAE,CAAC;;MAEnC;MACA,IAAIJ,WAAW,CAACI,IAAI,IAAIJ,WAAW,CAACI,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;QACrD1C,WAAW,CAAC2C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE/B,WAAW,EAAEyB,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC7B;QAAY,CAAC,CAAC,CAAC;MAClF;IAEF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DtB,SAAS,CAAC;QAAEwB,OAAO,EAAE;MAAkC,CAAC,CAAC;IAC3D,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEzB,KAAK;MAAE0B;IAAK,CAAC,GAAGF,CAAC,CAACG,MAAM;IACtCnD,WAAW,CAAC2C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACM,IAAI,GAAGC,IAAI,KAAK,QAAQ,GAAGE,UAAU,CAAC5B,KAAK,CAAC,IAAI,EAAE,GAAGA;IACxD,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIH,MAAM,CAAC4B,IAAI,CAAC,EAAE;MAChB3B,SAAS,CAACqB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACM,IAAI,GAAG;MAAK,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACvD,QAAQ,CAACE,WAAW,EAAEqD,SAAS,CAACrD,WAAW,GAAG,qBAAqB;IACxE,IAAI,CAACF,QAAQ,CAACI,aAAa,EAAEmD,SAAS,CAACnD,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACJ,QAAQ,CAACQ,QAAQ,IAAIR,QAAQ,CAACQ,QAAQ,IAAI,CAAC,EAAE+C,SAAS,CAAC/C,QAAQ,GAAG,oCAAoC;IAC3G,IAAI,CAACR,QAAQ,CAACS,eAAe,EAAE8C,SAAS,CAAC9C,eAAe,GAAG,2BAA2B;IACtF,IAAI,CAACT,QAAQ,CAACU,aAAa,EAAE6C,SAAS,CAAC7C,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACV,QAAQ,CAACa,WAAW,EAAE0C,SAAS,CAAC1C,WAAW,GAAG,oBAAoB;IACvE,IAAI,CAACb,QAAQ,CAACW,2BAA2B,IAAIX,QAAQ,CAACW,2BAA2B,GAAG,CAAC,EAAE;MACrF4C,SAAS,CAAC5C,2BAA2B,GAAG,uCAAuC;IACjF;;IAEA;IACA,IAAIX,QAAQ,CAACS,eAAe,IAAIT,QAAQ,CAACU,aAAa,EAAE;MACtD,MAAM8C,KAAK,GAAG,IAAIvB,IAAI,CAACjC,QAAQ,CAACS,eAAe,CAAC;MAChD,MAAMgD,GAAG,GAAG,IAAIxB,IAAI,CAACjC,QAAQ,CAACU,aAAa,CAAC;MAC5C,IAAI+C,GAAG,IAAID,KAAK,EAAE;QAChBD,SAAS,CAAC7C,aAAa,GAAG,uCAAuC;MACnE;IACF;IAEAa,SAAS,CAACgC,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACZ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACFjC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMyC,UAAU,GAAG;QACjB,GAAG9D,QAAQ;QACXE,WAAW,EAAE6D,QAAQ,CAAC/D,QAAQ,CAACE,WAAW,CAAC;QAC3CC,aAAa,EAAEH,QAAQ,CAACG,aAAa,GAAG4D,QAAQ,CAAC/D,QAAQ,CAACG,aAAa,CAAC,GAAG,IAAI;QAC/EK,QAAQ,EAAE6C,UAAU,CAACrD,QAAQ,CAACQ,QAAQ,CAAC;QACvCG,2BAA2B,EAAEoD,QAAQ,CAAC/D,QAAQ,CAACW,2BAA2B,CAAC;QAC3EE,WAAW,EAAEkD,QAAQ,CAAC/D,QAAQ,CAACa,WAAW,CAAC;QAC3CJ,eAAe,EAAE,IAAIwB,IAAI,CAACjC,QAAQ,CAACS,eAAe,CAAC,CAACyB,WAAW,CAAC,CAAC;QACjExB,aAAa,EAAE,IAAIuB,IAAI,CAACjC,QAAQ,CAACU,aAAa,CAAC,CAACwB,WAAW,CAAC;MAC9D,CAAC;MAED,MAAMtC,QAAQ,CAACkE,UAAU,CAAC;IAE5B,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdnB,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDtB,SAAS,CAAC;QACRwB,OAAO,EAAE,EAAAiB,eAAA,GAAAnB,KAAK,CAACqB,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBtB,IAAI,cAAAuB,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI;MAC3C,CAAC,CAAC;IACJ,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,IAAI,CAACN,SAAS,CAAC6B,MAAM,EAAE;IAChC,oBACEjD,OAAA;MAAK0E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD3E,OAAA;QAAK0E,SAAS,EAAC;MAA8D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpF/E,OAAA;QAAM0E,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAEV;EAEA,oBACE/E,OAAA;IAAK0E,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAClE3E,OAAA;MAAI0E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAClDvE,WAAW,GAAG,mBAAmB,GAAG;IAAgB;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,EAEJnD,MAAM,CAACyB,OAAO,iBACbrD,OAAA;MAAK0E,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClF/C,MAAM,CAACyB;IAAO;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACN,eAED/E,OAAA;MAAME,QAAQ,EAAEgE,YAAa;MAACQ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACjD3E,OAAA;QAAK0E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD3E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEwD,IAAI,EAAC,aAAa;YAClBzB,KAAK,EAAEzB,QAAQ,CAACE,WAAY;YAC5BwE,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAE,0FACT9C,MAAM,CAACpB,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,EACxD;YAAAmE,QAAA,gBAEH3E,OAAA;cAAQ+B,KAAK,EAAC,EAAE;cAAA4C,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC5C3D,SAAS,CAAC6D,GAAG,CAACC,EAAE,iBACflF,OAAA;cAAiC+B,KAAK,EAAEmD,EAAE,CAACC,eAAgB;cAAAR,QAAA,GACxDO,EAAE,CAACE,iBAAiB,EAAC,IAAE,EAACF,EAAE,CAACG,gBAAgB,IAAI,QAAQ,EAAC,GAC3D;YAAA,GAFaH,EAAE,CAACC,eAAe;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEvB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRnD,MAAM,CAACpB,WAAW,iBAAIR,OAAA;YAAG0E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE/C,MAAM,CAACpB;UAAW;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEwD,IAAI,EAAC,aAAa;YAClBzB,KAAK,EAAEzB,QAAQ,CAACa,WAAY;YAC5B6D,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAE,0FACT9C,MAAM,CAACT,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,EACxD;YAAAwD,QAAA,gBAEH3E,OAAA;cAAQ+B,KAAK,EAAC,EAAE;cAAA4C,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC3CvD,QAAQ,CAACyD,GAAG,CAACK,QAAQ,iBACpBtF,OAAA;cAAmC+B,KAAK,EAAEuD,QAAQ,CAACnE,WAAY;cAAAwD,QAAA,EAC5DW,QAAQ,CAACC;YAAQ,GADPD,QAAQ,CAACnE,WAAW;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEzB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRnD,MAAM,CAACT,WAAW,iBAAInB,OAAA;YAAG0E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE/C,MAAM,CAACT;UAAW;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEwD,IAAI,EAAC,eAAe;YACpBzB,KAAK,EAAEzB,QAAQ,CAACI,aAAc;YAC9BsE,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAE,0FACT9C,MAAM,CAAClB,aAAa,GAAG,gBAAgB,GAAG,iBAAiB,EAC1D;YAAAiE,QAAA,EAEF7C,aAAa,CAACmD,GAAG,CAACxB,IAAI,iBACrBzD,OAAA;cAAyB+B,KAAK,EAAE0B,IAAI,CAAC1B,KAAM;cAAA4C,QAAA,EACxClB,IAAI,CAACzB;YAAK,GADAyB,IAAI,CAAC1B,KAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRnD,MAAM,CAAClB,aAAa,iBAAIV,OAAA;YAAG0E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE/C,MAAM,CAAClB;UAAa;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEwD,IAAI,EAAC,eAAe;YACpBzB,KAAK,EAAEzB,QAAQ,CAACG,aAAc;YAC9BuE,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBAElH3E,OAAA;cAAQ+B,KAAK,EAAC,EAAE;cAAA4C,QAAA,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC3DzD,UAAU,CAAC2D,GAAG,CAACxB,IAAI,iBAClBzD,OAAA;cAAgC+B,KAAK,EAAE0B,IAAI,CAAC+B,YAAa;cAAAb,QAAA,GACtDlB,IAAI,CAACgC,eAAe,EAAC,KAAG,EAAChC,IAAI,CAACiC,gBAAgB;YAAA,GADpCjC,IAAI,CAAC+B,YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEtB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD3E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEyD,IAAI,EAAC,MAAM;YACXD,IAAI,EAAC,qBAAqB;YAC1BzB,KAAK,EAAEzB,QAAQ,CAACK,mBAAoB;YACpCqE,QAAQ,EAAE1B,iBAAkB;YAC5BqC,WAAW,EAAC,8BAA8B;YAC1CjB,SAAS,EAAC;UAAwG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,gBACnD,EAACrE,QAAQ,CAACI,aAAa,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;UAAA;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACR/E,OAAA;YACEyD,IAAI,EAAC,QAAQ;YACbD,IAAI,EAAC,UAAU;YACfzB,KAAK,EAAEzB,QAAQ,CAACQ,QAAS;YACzBkE,QAAQ,EAAE1B,iBAAkB;YAC5BsC,IAAI,EAAC,KAAK;YACVC,GAAG,EAAC,GAAG;YACPnB,SAAS,EAAE,0FACT9C,MAAM,CAACd,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB;UACrD;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACDnD,MAAM,CAACd,QAAQ,iBAAId,OAAA;YAAG0E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE/C,MAAM,CAACd;UAAQ;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEwD,IAAI,EAAC,0BAA0B;YAC/BzB,KAAK,EAAEzB,QAAQ,CAACM,wBAAyB;YACzCoE,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAC,wGAAwG;YAAAC,QAAA,EAEjH1C,uBAAuB,CAACgD,GAAG,CAACa,SAAS,iBACpC9F,OAAA;cAA8B+B,KAAK,EAAE+D,SAAS,CAAC/D,KAAM;cAAA4C,QAAA,EAClDmB,SAAS,CAAC9D;YAAK,GADL8D,SAAS,CAAC/D,KAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEpB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEwD,IAAI,EAAC,YAAY;YACjBzB,KAAK,EAAEzB,QAAQ,CAACO,UAAW;YAC3BmE,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAC,wGAAwG;YAAAC,QAAA,EAEjHzC,SAAS,CAAC+C,GAAG,CAACc,IAAI,iBACjB/F,OAAA;cAAyB+B,KAAK,EAAEgE,IAAI,CAAChE,KAAM;cAAA4C,QAAA,EACxCoB,IAAI,CAAC/D;YAAK,GADA+D,IAAI,CAAChE,KAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEyD,IAAI,EAAC,gBAAgB;YACrBD,IAAI,EAAC,iBAAiB;YACtBzB,KAAK,EAAEzB,QAAQ,CAACS,eAAgB;YAChCiE,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAE,0FACT9C,MAAM,CAACb,eAAe,GAAG,gBAAgB,GAAG,iBAAiB;UAC5D;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACDnD,MAAM,CAACb,eAAe,iBAAIf,OAAA;YAAG0E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE/C,MAAM,CAACb;UAAe;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEyD,IAAI,EAAC,gBAAgB;YACrBD,IAAI,EAAC,eAAe;YACpBzB,KAAK,EAAEzB,QAAQ,CAACU,aAAc;YAC9BgE,QAAQ,EAAE1B,iBAAkB;YAC5BoB,SAAS,EAAE,0FACT9C,MAAM,CAACZ,aAAa,GAAG,gBAAgB,GAAG,iBAAiB;UAC1D;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACDnD,MAAM,CAACZ,aAAa,iBAAIhB,OAAA;YAAG0E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE/C,MAAM,CAACZ;UAAa;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/E,OAAA;YACEyD,IAAI,EAAC,QAAQ;YACbD,IAAI,EAAC,6BAA6B;YAClCzB,KAAK,EAAEzB,QAAQ,CAACW,2BAA4B;YAC5C+D,QAAQ,EAAE1B,iBAAkB;YAC5BuC,GAAG,EAAC,GAAG;YACPnB,SAAS,EAAE,0FACT9C,MAAM,CAACX,2BAA2B,GAAG,gBAAgB,GAAG,iBAAiB;UACxE;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACDnD,MAAM,CAACX,2BAA2B,iBAAIjB,OAAA;YAAG0E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE/C,MAAM,CAACX;UAA2B;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/E,OAAA;QAAA2E,QAAA,gBACE3E,OAAA;UAAO0E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR/E,OAAA;UACEwD,IAAI,EAAC,OAAO;UACZzB,KAAK,EAAEzB,QAAQ,CAACY,KAAM;UACtB8D,QAAQ,EAAE1B,iBAAkB;UAC5B0C,IAAI,EAAC,GAAG;UACRL,WAAW,EAAC,sCAAsC;UAClDjB,SAAS,EAAC;QAAwG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN/E,OAAA;QAAK0E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C3E,OAAA;UACEyD,IAAI,EAAC,QAAQ;UACbwC,OAAO,EAAE9F,QAAS;UAClBuE,SAAS,EAAC,gIAAgI;UAAAC,QAAA,EAC3I;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/E,OAAA;UACEyD,IAAI,EAAC,QAAQ;UACbyC,QAAQ,EAAExE,OAAQ;UAClBgD,SAAS,EAAC,uIAAuI;UAAAC,QAAA,EAEhJjD,OAAO,GAAG,aAAa,GAAItB,WAAW,GAAG,UAAU,GAAG;QAAgB;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1E,EAAA,CA/aIJ,WAAW;AAAAkG,EAAA,GAAXlG,WAAW;AAibjB,eAAeA,WAAW;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}