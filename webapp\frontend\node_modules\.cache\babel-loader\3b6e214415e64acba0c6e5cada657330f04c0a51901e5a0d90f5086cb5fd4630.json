{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\ProductivityMain.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Container, Typography, Tabs, Tab, Paper, Fab, Snackbar, Alert, Chip, Grid, Card, CardContent, IconButton, Tooltip } from '@mui/material';\nimport { Dashboard as DashboardIcon, Assignment as WorkLogIcon, TrendingUp as EstimationIcon, Add as AddIcon, Analytics as AnalyticsIcon, Speed as SpeedIcon, Timeline as TimelineIcon } from '@mui/icons-material';\nimport Dashboard from './Dashboard';\nimport WorkLogForm from './WorkLogForm';\nimport EstimationTool from './EstimationTool';\nimport WorkLogsList from './WorkLogsList';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductivityMain = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [showWorkLogForm, setShowWorkLogForm] = useState(false);\n  const [editingWorkLog, setEditingWorkLog] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const tabs = [{\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this),\n    description: 'Panoramica generale e statistiche',\n    color: '#1976d2'\n  }, {\n    id: 'worklogs',\n    label: 'Work Logs',\n    icon: /*#__PURE__*/_jsxDEV(WorkLogIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    description: 'Gestione registrazioni di lavoro',\n    color: '#388e3c'\n  }, {\n    id: 'estimation',\n    label: 'Stima Produttività',\n    icon: /*#__PURE__*/_jsxDEV(EstimationIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    description: 'Calcolo tempi e produttività',\n    color: '#f57c00'\n  }];\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification(null), 5000);\n  };\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setShowWorkLogForm(false);\n    setEditingWorkLog(null);\n  };\n  const handleCreateWorkLog = async workLogData => {\n    try {\n      await axiosInstance.post('/v1/work-logs', workLogData);\n      showNotification('Work log creato con successo!', 'success');\n      setShowWorkLogForm(false);\n\n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore nella creazione work log:', error);\n      showNotification(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore nella creazione del work log', 'error');\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n  const handleUpdateWorkLog = async workLogData => {\n    try {\n      await axiosInstance.put(`/v1/work-logs/${editingWorkLog.id}`, workLogData);\n      showNotification('Work log aggiornato con successo!', 'success');\n      setShowWorkLogForm(false);\n      setEditingWorkLog(null);\n\n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Errore nell\\'aggiornamento work log:', error);\n      showNotification(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Errore nell\\'aggiornamento del work log', 'error');\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n  const handleEditWorkLog = workLog => {\n    setEditingWorkLog(workLog);\n    setShowWorkLogForm(true);\n  };\n  const handleDeleteWorkLog = async workLogId => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo work log?')) {\n      return;\n    }\n    try {\n      await axiosInstance.delete(`/v1/work-logs/${workLogId}`);\n      showNotification('Work log eliminato con successo!', 'success');\n\n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Errore nell\\'eliminazione work log:', error);\n      showNotification(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Errore nell\\'eliminazione del work log', 'error');\n    }\n  };\n  const renderTabContent = () => {\n    if (showWorkLogForm) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(WorkLogForm, {\n          initialData: editingWorkLog,\n          onSubmit: editingWorkLog ? handleUpdateWorkLog : handleCreateWorkLog,\n          onCancel: () => {\n            setShowWorkLogForm(false);\n            setEditingWorkLog(null);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this);\n    }\n    switch (activeTab) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 16\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(WorkLogsList, {\n          onEdit: handleEditWorkLog,\n          onDelete: handleDeleteWorkLog,\n          onCreateNew: () => setShowWorkLogForm(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(EstimationTool, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'background.default',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        bgcolor: 'rgba(255, 255, 255, 0.95)',\n        backdropFilter: 'blur(10px)',\n        borderRadius: 0,\n        borderBottom: '1px solid rgba(0, 0, 0, 0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"xl\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            py: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 8,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(SpeedIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: 'primary.main',\n                    mr: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  component: \"h1\",\n                  sx: {\n                    fontWeight: 700,\n                    background: 'linear-gradient(45deg, #1976d2, #42a5f5)',\n                    backgroundClip: 'text',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"Sistema Produttivit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                color: \"text.secondary\",\n                children: \"Gestione e analisi della produttivit\\xE0 per installazione cavi elettrici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 27\n                  }, this),\n                  label: \"Analytics Avanzate\",\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 27\n                  }, this),\n                  label: \"Real-time\",\n                  color: \"success\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), !showWorkLogForm && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        bgcolor: 'rgba(255, 255, 255, 0.9)',\n        backdropFilter: 'blur(10px)',\n        borderRadius: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"xl\",\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          variant: \"fullWidth\",\n          sx: {\n            '& .MuiTab-root': {\n              minHeight: 80,\n              textTransform: 'none',\n              fontSize: '1rem',\n              fontWeight: 600\n            },\n            '& .MuiTabs-indicator': {\n              height: 3,\n              borderRadius: '3px 3px 0 0'\n            }\n          },\n          children: tabs.map((tab, index) => /*#__PURE__*/_jsxDEV(Tab, {\n            icon: tab.icon,\n            label: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: tab.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: tab.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 21\n            }, this),\n            sx: {\n              '&.Mui-selected': {\n                color: tab.color\n              }\n            }\n          }, tab.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        py: 4,\n        position: 'relative',\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.95)',\n          borderRadius: 3,\n          backdropFilter: 'blur(10px)',\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          overflow: 'hidden',\n          minHeight: '70vh'\n        },\n        children: renderTabContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), activeTab === 1 && !showWorkLogForm && /*#__PURE__*/_jsxDEV(Fab, {\n      color: \"primary\",\n      \"aria-label\": \"add work log\",\n      onClick: () => setShowWorkLogForm(true),\n      sx: {\n        position: 'fixed',\n        bottom: 32,\n        right: 32,\n        background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n        '&:hover': {\n          background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: !!notification,\n      autoHideDuration: 5000,\n      onClose: () => setNotification(null),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => setNotification(null),\n        severity: (notification === null || notification === void 0 ? void 0 : notification.type) || 'success',\n        variant: \"filled\",\n        sx: {\n          width: '100%'\n        },\n        children: notification === null || notification === void 0 ? void 0 : notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductivityMain, \"giiMiXNNEGTdYX6+e+aweL0IB0Q=\");\n_c = ProductivityMain;\nexport default ProductivityMain;\nvar _c;\n$RefreshReg$(_c, \"ProductivityMain\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Container", "Typography", "Tabs", "Tab", "Paper", "Fab", "Snackbar", "<PERSON><PERSON>", "Chip", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "Dashboard", "DashboardIcon", "Assignment", "WorkLogIcon", "TrendingUp", "EstimationIcon", "Add", "AddIcon", "Analytics", "AnalyticsIcon", "Speed", "SpeedIcon", "Timeline", "TimelineIcon", "WorkLogForm", "EstimationTool", "WorkLogsList", "axiosInstance", "jsxDEV", "_jsxDEV", "ProductivityMain", "_s", "activeTab", "setActiveTab", "showWorkLogForm", "setShowWorkLogForm", "editingWorkLog", "setEditingWorkLog", "notification", "setNotification", "tabs", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "color", "showNotification", "message", "type", "setTimeout", "handleTabChange", "event", "newValue", "handleCreateWorkLog", "workLogData", "post", "window", "location", "reload", "error", "_error$response", "_error$response$data", "console", "response", "data", "detail", "handleUpdateWorkLog", "put", "_error$response2", "_error$response2$data", "handleEditWorkLog", "workLog", "handleDeleteWorkLog", "workLogId", "confirm", "delete", "_error$response3", "_error$response3$data", "renderTabContent", "sx", "mt", "children", "initialData", "onSubmit", "onCancel", "onEdit", "onDelete", "onCreateNew", "minHeight", "bgcolor", "background", "position", "elevation", "<PERSON><PERSON>ilter", "borderRadius", "borderBottom", "max<PERSON><PERSON><PERSON>", "py", "container", "alignItems", "spacing", "item", "xs", "md", "display", "mb", "fontSize", "mr", "variant", "component", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "justifyContent", "gap", "value", "onChange", "textTransform", "height", "map", "tab", "index", "textAlign", "zIndex", "boxShadow", "overflow", "onClick", "bottom", "right", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/ProductivityMain.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Tabs,\n  Tab,\n  Paper,\n  Fab,\n  Snackbar,\n  Alert,\n  Chip,\n  Grid,\n  Card,\n  CardContent,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  Assignment as WorkLogIcon,\n  TrendingUp as EstimationIcon,\n  Add as AddIcon,\n  Analytics as AnalyticsIcon,\n  Speed as SpeedIcon,\n  Timeline as TimelineIcon\n} from '@mui/icons-material';\nimport Dashboard from './Dashboard';\nimport WorkLogForm from './WorkLogForm';\nimport EstimationTool from './EstimationTool';\nimport WorkLogsList from './WorkLogsList';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst ProductivityMain = () => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [showWorkLogForm, setShowWorkLogForm] = useState(false);\n  const [editingWorkLog, setEditingWorkLog] = useState(null);\n  const [notification, setNotification] = useState(null);\n\n  const tabs = [\n    {\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: <DashboardIcon />,\n      description: 'Panoramica generale e statistiche',\n      color: '#1976d2'\n    },\n    {\n      id: 'worklogs',\n      label: 'Work Logs',\n      icon: <WorkLogIcon />,\n      description: 'Gestione registrazioni di lavoro',\n      color: '#388e3c'\n    },\n    {\n      id: 'estimation',\n      label: 'Stima Produttività',\n      icon: <EstimationIcon />,\n      description: 'Calcolo tempi e produttività',\n      color: '#f57c00'\n    },\n  ];\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setShowWorkLogForm(false);\n    setEditingWorkLog(null);\n  };\n\n  const handleCreateWorkLog = async (workLogData) => {\n    try {\n      await axiosInstance.post('/v1/work-logs', workLogData);\n      showNotification('Work log creato con successo!', 'success');\n      setShowWorkLogForm(false);\n      \n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      console.error('Errore nella creazione work log:', error);\n      showNotification(\n        error.response?.data?.detail || 'Errore nella creazione del work log',\n        'error'\n      );\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n\n  const handleUpdateWorkLog = async (workLogData) => {\n    try {\n      await axiosInstance.put(`/v1/work-logs/${editingWorkLog.id}`, workLogData);\n      showNotification('Work log aggiornato con successo!', 'success');\n      setShowWorkLogForm(false);\n      setEditingWorkLog(null);\n      \n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento work log:', error);\n      showNotification(\n        error.response?.data?.detail || 'Errore nell\\'aggiornamento del work log',\n        'error'\n      );\n      throw error; // Re-throw per gestione nel form\n    }\n  };\n\n  const handleEditWorkLog = (workLog) => {\n    setEditingWorkLog(workLog);\n    setShowWorkLogForm(true);\n  };\n\n  const handleDeleteWorkLog = async (workLogId) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo work log?')) {\n      return;\n    }\n\n    try {\n      await axiosInstance.delete(`/v1/work-logs/${workLogId}`);\n      showNotification('Work log eliminato con successo!', 'success');\n      \n      // Aggiorna la lista se siamo nella tab work logs\n      if (activeTab === 'worklogs') {\n        window.location.reload(); // Semplice refresh per ora\n      }\n    } catch (error) {\n      console.error('Errore nell\\'eliminazione work log:', error);\n      showNotification(\n        error.response?.data?.detail || 'Errore nell\\'eliminazione del work log',\n        'error'\n      );\n    }\n  };\n\n  const renderTabContent = () => {\n    if (showWorkLogForm) {\n      return (\n        <Box sx={{ mt: 3 }}>\n          <WorkLogForm\n            initialData={editingWorkLog}\n            onSubmit={editingWorkLog ? handleUpdateWorkLog : handleCreateWorkLog}\n            onCancel={() => {\n              setShowWorkLogForm(false);\n              setEditingWorkLog(null);\n            }}\n          />\n        </Box>\n      );\n    }\n\n    switch (activeTab) {\n      case 0:\n        return <Dashboard />;\n\n      case 1:\n        return (\n          <WorkLogsList\n            onEdit={handleEditWorkLog}\n            onDelete={handleDeleteWorkLog}\n            onCreateNew={() => setShowWorkLogForm(true)}\n          />\n        );\n\n      case 2:\n        return <EstimationTool />;\n\n      default:\n        return <Dashboard />;\n    }\n  };\n\n  return (\n    <Box sx={{\n      minHeight: '100vh',\n      bgcolor: 'background.default',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      position: 'relative'\n    }}>\n      {/* Header moderno */}\n      <Paper\n        elevation={0}\n        sx={{\n          bgcolor: 'rgba(255, 255, 255, 0.95)',\n          backdropFilter: 'blur(10px)',\n          borderRadius: 0,\n          borderBottom: '1px solid rgba(0, 0, 0, 0.1)'\n        }}\n      >\n        <Container maxWidth=\"xl\">\n          <Box sx={{ py: 3 }}>\n            <Grid container alignItems=\"center\" spacing={2}>\n              <Grid item xs={12} md={8}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <SpeedIcon sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />\n                  <Typography\n                    variant=\"h4\"\n                    component=\"h1\"\n                    sx={{\n                      fontWeight: 700,\n                      background: 'linear-gradient(45deg, #1976d2, #42a5f5)',\n                      backgroundClip: 'text',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                    }}\n                  >\n                    Sistema Produttività\n                  </Typography>\n                </Box>\n                <Typography variant=\"subtitle1\" color=\"text.secondary\">\n                  Gestione e analisi della produttività per installazione cavi elettrici\n                </Typography>\n              </Grid>\n\n              <Grid item xs={12} md={4}>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>\n                  <Chip\n                    icon={<AnalyticsIcon />}\n                    label=\"Analytics Avanzate\"\n                    color=\"primary\"\n                    variant=\"outlined\"\n                  />\n                  <Chip\n                    icon={<TimelineIcon />}\n                    label=\"Real-time\"\n                    color=\"success\"\n                    variant=\"outlined\"\n                  />\n                </Box>\n              </Grid>\n            </Grid>\n          </Box>\n        </Container>\n      </Paper>\n\n      {/* Navigation Tabs moderne */}\n      {!showWorkLogForm && (\n        <Paper\n          elevation={0}\n          sx={{\n            bgcolor: 'rgba(255, 255, 255, 0.9)',\n            backdropFilter: 'blur(10px)',\n            borderRadius: 0\n          }}\n        >\n          <Container maxWidth=\"xl\">\n            <Tabs\n              value={activeTab}\n              onChange={handleTabChange}\n              variant=\"fullWidth\"\n              sx={{\n                '& .MuiTab-root': {\n                  minHeight: 80,\n                  textTransform: 'none',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                },\n                '& .MuiTabs-indicator': {\n                  height: 3,\n                  borderRadius: '3px 3px 0 0',\n                }\n              }}\n            >\n              {tabs.map((tab, index) => (\n                <Tab\n                  key={tab.id}\n                  icon={tab.icon}\n                  label={\n                    <Box sx={{ textAlign: 'center' }}>\n                      <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\n                        {tab.label}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {tab.description}\n                      </Typography>\n                    </Box>\n                  }\n                  sx={{\n                    '&.Mui-selected': {\n                      color: tab.color,\n                    }\n                  }}\n                />\n              ))}\n            </Tabs>\n          </Container>\n        </Paper>\n      )}\n\n      {/* Main Content con sfondo moderno */}\n      <Container maxWidth=\"xl\" sx={{ py: 4, position: 'relative', zIndex: 1 }}>\n        <Box sx={{\n          bgcolor: 'rgba(255, 255, 255, 0.95)',\n          borderRadius: 3,\n          backdropFilter: 'blur(10px)',\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          overflow: 'hidden',\n          minHeight: '70vh'\n        }}>\n          {renderTabContent()}\n        </Box>\n      </Container>\n\n      {/* Floating Action Button per Work Logs */}\n      {activeTab === 1 && !showWorkLogForm && (\n        <Fab\n          color=\"primary\"\n          aria-label=\"add work log\"\n          onClick={() => setShowWorkLogForm(true)}\n          sx={{\n            position: 'fixed',\n            bottom: 32,\n            right: 32,\n            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n            '&:hover': {\n              background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',\n            }\n          }}\n        >\n          <AddIcon />\n        </Fab>\n      )}\n\n      {/* Snackbar per notifiche */}\n      <Snackbar\n        open={!!notification}\n        autoHideDuration={5000}\n        onClose={() => setNotification(null)}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert\n          onClose={() => setNotification(null)}\n          severity={notification?.type || 'success'}\n          variant=\"filled\"\n          sx={{ width: '100%' }}\n        >\n          {notification?.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ProductivityMain;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,WAAW,EACzBC,UAAU,IAAIC,cAAc,EAC5BC,GAAG,IAAIC,OAAO,EACdC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOb,SAAS,MAAM,aAAa;AACnC,OAAOc,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM8C,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,WAAW;IAClBC,IAAI,eAAEd,OAAA,CAAClB,aAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,WAAW,EAAE,mCAAmC;IAChDC,KAAK,EAAE;EACT,CAAC,EACD;IACER,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,WAAW;IAClBC,IAAI,eAAEd,OAAA,CAAChB,WAAW;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,WAAW,EAAE,kCAAkC;IAC/CC,KAAK,EAAE;EACT,CAAC,EACD;IACER,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,eAAEd,OAAA,CAACd,cAAc;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,WAAW,EAAE,8BAA8B;IAC3CC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDb,eAAe,CAAC;MAAEY,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCC,UAAU,CAAC,MAAMd,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMe,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CvB,YAAY,CAACuB,QAAQ,CAAC;IACtBrB,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD,IAAI;MACF,MAAM/B,aAAa,CAACgC,IAAI,CAAC,eAAe,EAAED,WAAW,CAAC;MACtDR,gBAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;MAC5Df,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACA,IAAIH,SAAS,KAAK,UAAU,EAAE;QAC5B4B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDb,gBAAgB,CACd,EAAAc,eAAA,GAAAD,KAAK,CAACI,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,KAAI,qCAAqC,EACrE,OACF,CAAC;MACD,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMO,mBAAmB,GAAG,MAAOZ,WAAW,IAAK;IACjD,IAAI;MACF,MAAM/B,aAAa,CAAC4C,GAAG,CAAC,iBAAiBnC,cAAc,CAACK,EAAE,EAAE,EAAEiB,WAAW,CAAC;MAC1ER,gBAAgB,CAAC,mCAAmC,EAAE,SAAS,CAAC;MAChEf,kBAAkB,CAAC,KAAK,CAAC;MACzBE,iBAAiB,CAAC,IAAI,CAAC;;MAEvB;MACA,IAAIL,SAAS,KAAK,UAAU,EAAE;QAC5B4B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAS,gBAAA,EAAAC,qBAAA;MACdP,OAAO,CAACH,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5Db,gBAAgB,CACd,EAAAsB,gBAAA,GAAAT,KAAK,CAACI,QAAQ,cAAAK,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI,yCAAyC,EACzE,OACF,CAAC;MACD,MAAMN,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMW,iBAAiB,GAAIC,OAAO,IAAK;IACrCtC,iBAAiB,CAACsC,OAAO,CAAC;IAC1BxC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyC,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI,CAACjB,MAAM,CAACkB,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACrE;IACF;IAEA,IAAI;MACF,MAAMnD,aAAa,CAACoD,MAAM,CAAC,iBAAiBF,SAAS,EAAE,CAAC;MACxD3B,gBAAgB,CAAC,kCAAkC,EAAE,SAAS,CAAC;;MAE/D;MACA,IAAIlB,SAAS,KAAK,UAAU,EAAE;QAC5B4B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACdf,OAAO,CAACH,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3Db,gBAAgB,CACd,EAAA8B,gBAAA,GAAAjB,KAAK,CAACI,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBZ,MAAM,KAAI,wCAAwC,EACxE,OACF,CAAC;IACH;EACF,CAAC;EAED,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhD,eAAe,EAAE;MACnB,oBACEL,OAAA,CAAClC,GAAG;QAACwF,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eACjBxD,OAAA,CAACL,WAAW;UACV8D,WAAW,EAAElD,cAAe;UAC5BmD,QAAQ,EAAEnD,cAAc,GAAGkC,mBAAmB,GAAGb,mBAAoB;UACrE+B,QAAQ,EAAEA,CAAA,KAAM;YACdrD,kBAAkB,CAAC,KAAK,CAAC;YACzBE,iBAAiB,CAAC,IAAI,CAAC;UACzB;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV;IAEA,QAAQf,SAAS;MACf,KAAK,CAAC;QACJ,oBAAOH,OAAA,CAACnB,SAAS;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAEtB,KAAK,CAAC;QACJ,oBACElB,OAAA,CAACH,YAAY;UACX+D,MAAM,EAAEf,iBAAkB;UAC1BgB,QAAQ,EAAEd,mBAAoB;UAC9Be,WAAW,EAAEA,CAAA,KAAMxD,kBAAkB,CAAC,IAAI;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAGN,KAAK,CAAC;QACJ,oBAAOlB,OAAA,CAACJ,cAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE3B;QACE,oBAAOlB,OAAA,CAACnB,SAAS;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,oBACElB,OAAA,CAAClC,GAAG;IAACwF,EAAE,EAAE;MACPS,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,oBAAoB;MAC7BC,UAAU,EAAE,mDAAmD;MAC/DC,QAAQ,EAAE;IACZ,CAAE;IAAAV,QAAA,gBAEAxD,OAAA,CAAC7B,KAAK;MACJgG,SAAS,EAAE,CAAE;MACbb,EAAE,EAAE;QACFU,OAAO,EAAE,2BAA2B;QACpCI,cAAc,EAAE,YAAY;QAC5BC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;MAChB,CAAE;MAAAd,QAAA,eAEFxD,OAAA,CAACjC,SAAS;QAACwG,QAAQ,EAAC,IAAI;QAAAf,QAAA,eACtBxD,OAAA,CAAClC,GAAG;UAACwF,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAhB,QAAA,eACjBxD,OAAA,CAACxB,IAAI;YAACiG,SAAS;YAACC,UAAU,EAAC,QAAQ;YAACC,OAAO,EAAE,CAAE;YAAAnB,QAAA,gBAC7CxD,OAAA,CAACxB,IAAI;cAACoG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,gBACvBxD,OAAA,CAAClC,GAAG;gBAACwF,EAAE,EAAE;kBAAEyB,OAAO,EAAE,MAAM;kBAAEL,UAAU,EAAE,QAAQ;kBAAEM,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,gBACxDxD,OAAA,CAACR,SAAS;kBAAC8D,EAAE,EAAE;oBAAE2B,QAAQ,EAAE,EAAE;oBAAE7D,KAAK,EAAE,cAAc;oBAAE8D,EAAE,EAAE;kBAAE;gBAAE;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjElB,OAAA,CAAChC,UAAU;kBACTmH,OAAO,EAAC,IAAI;kBACZC,SAAS,EAAC,IAAI;kBACd9B,EAAE,EAAE;oBACF+B,UAAU,EAAE,GAAG;oBACfpB,UAAU,EAAE,0CAA0C;oBACtDqB,cAAc,EAAE,MAAM;oBACtBC,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAAhC,QAAA,EACH;gBAED;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNlB,OAAA,CAAChC,UAAU;gBAACmH,OAAO,EAAC,WAAW;gBAAC/D,KAAK,EAAC,gBAAgB;gBAAAoC,QAAA,EAAC;cAEvD;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEPlB,OAAA,CAACxB,IAAI;cAACoG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACvBxD,OAAA,CAAClC,GAAG;gBAACwF,EAAE,EAAE;kBAAEyB,OAAO,EAAE,MAAM;kBAAEU,cAAc,EAAE,UAAU;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAlC,QAAA,gBAC/DxD,OAAA,CAACzB,IAAI;kBACHuC,IAAI,eAAEd,OAAA,CAACV,aAAa;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBL,KAAK,EAAC,oBAAoB;kBAC1BO,KAAK,EAAC,SAAS;kBACf+D,OAAO,EAAC;gBAAU;kBAAApE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACFlB,OAAA,CAACzB,IAAI;kBACHuC,IAAI,eAAEd,OAAA,CAACN,YAAY;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBL,KAAK,EAAC,WAAW;kBACjBO,KAAK,EAAC,SAAS;kBACf+D,OAAO,EAAC;gBAAU;kBAAApE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,EAGP,CAACb,eAAe,iBACfL,OAAA,CAAC7B,KAAK;MACJgG,SAAS,EAAE,CAAE;MACbb,EAAE,EAAE;QACFU,OAAO,EAAE,0BAA0B;QACnCI,cAAc,EAAE,YAAY;QAC5BC,YAAY,EAAE;MAChB,CAAE;MAAAb,QAAA,eAEFxD,OAAA,CAACjC,SAAS;QAACwG,QAAQ,EAAC,IAAI;QAAAf,QAAA,eACtBxD,OAAA,CAAC/B,IAAI;UACH0H,KAAK,EAAExF,SAAU;UACjByF,QAAQ,EAAEnE,eAAgB;UAC1B0D,OAAO,EAAC,WAAW;UACnB7B,EAAE,EAAE;YACF,gBAAgB,EAAE;cAChBS,SAAS,EAAE,EAAE;cACb8B,aAAa,EAAE,MAAM;cACrBZ,QAAQ,EAAE,MAAM;cAChBI,UAAU,EAAE;YACd,CAAC;YACD,sBAAsB,EAAE;cACtBS,MAAM,EAAE,CAAC;cACTzB,YAAY,EAAE;YAChB;UACF,CAAE;UAAAb,QAAA,EAED7C,IAAI,CAACoF,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACnBjG,OAAA,CAAC9B,GAAG;YAEF4C,IAAI,EAAEkF,GAAG,CAAClF,IAAK;YACfD,KAAK,eACHb,OAAA,CAAClC,GAAG;cAACwF,EAAE,EAAE;gBAAE4C,SAAS,EAAE;cAAS,CAAE;cAAA1C,QAAA,gBAC/BxD,OAAA,CAAChC,UAAU;gBAACmH,OAAO,EAAC,WAAW;gBAAC7B,EAAE,EAAE;kBAAE+B,UAAU,EAAE;gBAAI,CAAE;gBAAA7B,QAAA,EACrDwC,GAAG,CAACnF;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACblB,OAAA,CAAChC,UAAU;gBAACmH,OAAO,EAAC,SAAS;gBAAC/D,KAAK,EAAC,gBAAgB;gBAAAoC,QAAA,EACjDwC,GAAG,CAAC7E;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;YACDoC,EAAE,EAAE;cACF,gBAAgB,EAAE;gBAChBlC,KAAK,EAAE4E,GAAG,CAAC5E;cACb;YACF;UAAE,GAhBG4E,GAAG,CAACpF,EAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBZ,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACR,eAGDlB,OAAA,CAACjC,SAAS;MAACwG,QAAQ,EAAC,IAAI;MAACjB,EAAE,EAAE;QAAEkB,EAAE,EAAE,CAAC;QAAEN,QAAQ,EAAE,UAAU;QAAEiC,MAAM,EAAE;MAAE,CAAE;MAAA3C,QAAA,eACtExD,OAAA,CAAClC,GAAG;QAACwF,EAAE,EAAE;UACPU,OAAO,EAAE,2BAA2B;UACpCK,YAAY,EAAE,CAAC;UACfD,cAAc,EAAE,YAAY;UAC5BgC,SAAS,EAAE,+BAA+B;UAC1CC,QAAQ,EAAE,QAAQ;UAClBtC,SAAS,EAAE;QACb,CAAE;QAAAP,QAAA,EACCH,gBAAgB,CAAC;MAAC;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGXf,SAAS,KAAK,CAAC,IAAI,CAACE,eAAe,iBAClCL,OAAA,CAAC5B,GAAG;MACFgD,KAAK,EAAC,SAAS;MACf,cAAW,cAAc;MACzBkF,OAAO,EAAEA,CAAA,KAAMhG,kBAAkB,CAAC,IAAI,CAAE;MACxCgD,EAAE,EAAE;QACFY,QAAQ,EAAE,OAAO;QACjBqC,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,EAAE;QACTvC,UAAU,EAAE,kDAAkD;QAC9D,SAAS,EAAE;UACTA,UAAU,EAAE;QACd;MACF,CAAE;MAAAT,QAAA,eAEFxD,OAAA,CAACZ,OAAO;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,eAGDlB,OAAA,CAAC3B,QAAQ;MACPoI,IAAI,EAAE,CAAC,CAAChG,YAAa;MACrBiG,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMjG,eAAe,CAAC,IAAI,CAAE;MACrCkG,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAtD,QAAA,eAE1DxD,OAAA,CAAC1B,KAAK;QACJqI,OAAO,EAAEA,CAAA,KAAMjG,eAAe,CAAC,IAAI,CAAE;QACrCqG,QAAQ,EAAE,CAAAtG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEc,IAAI,KAAI,SAAU;QAC1C4D,OAAO,EAAC,QAAQ;QAChB7B,EAAE,EAAE;UAAE0D,KAAK,EAAE;QAAO,CAAE;QAAAxD,QAAA,EAErB/C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAChB,EAAA,CA3TID,gBAAgB;AAAAgH,EAAA,GAAhBhH,gBAAgB;AA6TtB,eAAeA,gBAAgB;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}