{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'\n\n// Configurazione base per l'API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n\n// Crea istanza axios con configurazione base\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n})\n\n// Interceptor per aggiungere il token di autenticazione\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  (error) => {\n    return Promise.reject(error)\n  }\n)\n\n// Interceptor per gestire le risposte e gli errori\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token scaduto o non valido\n      localStorage.removeItem('access_token')\n      localStorage.removeItem('user_data')\n      window.location.href = '/login'\n    }\n    return Promise.reject(error)\n  }\n)\n\n// Tipi per le risposte API\nexport interface ApiResponse<T = any> {\n  data: T\n  message?: string\n  status: number\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[]\n  total: number\n  page: number\n  size: number\n  pages: number\n}\n\n// Funzioni helper per le chiamate API\nexport const api = {\n  // GET request\n  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.get<T>(url, config)\n    return response.data\n  },\n\n  // POST request\n  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.post<T>(url, data, config)\n    return response.data\n  },\n\n  // PUT request\n  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.put<T>(url, data, config)\n    return response.data\n  },\n\n  // PATCH request\n  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.patch<T>(url, data, config)\n    return response.data\n  },\n\n  // DELETE request\n  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.delete<T>(url, config)\n    return response.data\n  },\n}\n\n// Servizi API specifici per CABLYS\nexport const authApi = {\n  // Login utente\n  login: (credentials: { username: string; password: string }) =>\n    api.post<{ access_token: string; token_type: string; user: any }>('/auth/login', credentials),\n\n  // Login cantiere\n  loginCantiere: (credentials: { codice_cantiere: string; password_cantiere: string }) =>\n    api.post<{ access_token: string; token_type: string; cantiere: any }>('/auth/login-cantiere', credentials),\n\n  // Verifica token\n  verifyToken: () =>\n    api.get<{ user: any }>('/auth/me'),\n\n  // Logout\n  logout: () => {\n    localStorage.removeItem('access_token')\n    localStorage.removeItem('user_data')\n    window.location.href = '/login'\n  }\n}\n\nexport const caviApi = {\n  // Ottieni tutti i cavi\n  getCavi: (cantiereId: number, params?: any) =>\n    api.get<any[]>(`/cavi/${cantiereId}`, { params }),\n\n  // Ottieni cavo specifico\n  getCavo: (cantiereId: number, idCavo: string) =>\n    api.get<any>(`/cavi/${cantiereId}/${idCavo}`),\n\n  // Crea nuovo cavo\n  createCavo: (cantiereId: number, cavo: any) =>\n    api.post<any>(`/cavi/${cantiereId}`, cavo),\n\n  // Aggiorna cavo\n  updateCavo: (cantiereId: number, idCavo: string, updates: any) =>\n    api.put<any>(`/cavi/${cantiereId}/${idCavo}`, updates),\n\n  // Elimina cavo\n  deleteCavo: (cantiereId: number, idCavo: string) =>\n    api.delete(`/cavi/${cantiereId}/${idCavo}`),\n\n  // Aggiorna metri posati\n  updateMetriPosati: (cantiereId: number, idCavo: string, metri: number) =>\n    api.patch<any>(`/cavi/${cantiereId}/${idCavo}/metri-posati`, { metri_posati: metri }),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idCavo: string, bobina: string) =>\n    api.patch<any>(`/cavi/${cantiereId}/${idCavo}/bobina`, { id_bobina: bobina }),\n\n  // Aggiorna collegamento\n  updateCollegamento: (cantiereId: number, idCavo: string, collegamento: number) =>\n    api.patch<any>(`/cavi/${cantiereId}/${idCavo}/collegamento`, { collegamenti: collegamento }),\n}\n\nexport const parcoCaviApi = {\n  // Ottieni tutte le bobine\n  getBobine: (cantiereId: number) =>\n    api.get<any[]>(`/parco-cavi/${cantiereId}`),\n\n  // Ottieni bobina specifica\n  getBobina: (cantiereId: number, idBobina: string) =>\n    api.get<any>(`/parco-cavi/${cantiereId}/${idBobina}`),\n\n  // Crea nuova bobina\n  createBobina: (cantiereId: number, bobina: any) =>\n    api.post<any>(`/parco-cavi/${cantiereId}`, bobina),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idBobina: string, updates: any) =>\n    api.put<any>(`/parco-cavi/${cantiereId}/${idBobina}`, updates),\n\n  // Elimina bobina\n  deleteBobina: (cantiereId: number, idBobina: string) =>\n    api.delete(`/parco-cavi/${cantiereId}/${idBobina}`),\n}\n\nexport const comandeApi = {\n  // Ottieni tutte le comande\n  getComande: (cantiereId: number) =>\n    api.get<any[]>(`/comande/${cantiereId}`),\n\n  // Ottieni comanda specifica\n  getComanda: (cantiereId: number, codiceComanda: string) =>\n    api.get<any>(`/comande/${cantiereId}/${codiceComanda}`),\n\n  // Crea nuova comanda\n  createComanda: (cantiereId: number, comanda: any) =>\n    api.post<any>(`/comande/${cantiereId}`, comanda),\n\n  // Aggiorna comanda\n  updateComanda: (cantiereId: number, codiceComanda: string, updates: any) =>\n    api.put<any>(`/comande/${cantiereId}/${codiceComanda}`, updates),\n\n  // Elimina comanda\n  deleteComanda: (cantiereId: number, codiceComanda: string) =>\n    api.delete(`/comande/${cantiereId}/${codiceComanda}`),\n\n  // Assegna cavi a comanda\n  assegnaCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>\n    api.post<any>(`/comande/${cantiereId}/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),\n}\n\nexport const responsabiliApi = {\n  // Ottieni tutti i responsabili\n  getResponsabili: (cantiereId: number) =>\n    api.get<any[]>(`/responsabili/${cantiereId}`),\n\n  // Crea nuovo responsabile\n  createResponsabile: (cantiereId: number, responsabile: any) =>\n    api.post<any>(`/responsabili/${cantiereId}`, responsabile),\n\n  // Aggiorna responsabile\n  updateResponsabile: (cantiereId: number, id: number, updates: any) =>\n    api.put<any>(`/responsabili/${cantiereId}/${id}`, updates),\n\n  // Elimina responsabile\n  deleteResponsabile: (cantiereId: number, id: number) =>\n    api.delete(`/responsabili/${cantiereId}/${id}`),\n}\n\nexport const reportsApi = {\n  // Report avanzamento\n  getReportAvanzamento: (cantiereId: number) =>\n    api.get<any>(`/reports/${cantiereId}/avanzamento`),\n\n  // Report BOQ\n  getReportBOQ: (cantiereId: number) =>\n    api.get<any>(`/reports/${cantiereId}/boq`),\n\n  // Report utilizzo bobine\n  getReportUtilizzoBobine: (cantiereId: number) =>\n    api.get<any>(`/reports/${cantiereId}/utilizzo-bobine`),\n\n  // Report progress\n  getReportProgress: (cantiereId: number) =>\n    api.get<any>(`/reports/${cantiereId}/progress`),\n}\n\nexport const cantieriApi = {\n  // Ottieni tutti i cantieri\n  getCantieri: () =>\n    api.get<any[]>('/cantieri'),\n\n  // Ottieni cantiere specifico\n  getCantiere: (id: number) =>\n    api.get<any>(`/cantieri/${id}`),\n\n  // Crea nuovo cantiere\n  createCantiere: (cantiere: any) =>\n    api.post<any>('/cantieri', cantiere),\n\n  // Aggiorna cantiere\n  updateCantiere: (id: number, updates: any) =>\n    api.put<any>(`/cantieri/${id}`, updates),\n}\n\nexport default apiClient\n"], "names": [], "mappings": ";;;;;;;;;;;AAGqB;AAHrB;;AAEA,gCAAgC;AAChC,MAAM,eAAe,6DAAmC;AAExD,6CAA6C;AAC7C,MAAM,YAA2B,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wDAAwD;AACxD,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,mDAAmD;AACnD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,6BAA6B;QAC7B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAmBK,MAAM,MAAM;IACjB,cAAc;IACd,KAAK,OAAgB,KAAa;QAChC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM,OAAgB,KAAa,MAAY;QAC7C,MAAM,WAAW,MAAM,UAAU,IAAI,CAAI,KAAK,MAAM;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,KAAK,OAAgB,KAAa,MAAY;QAC5C,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK,MAAM;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,OAAO,OAAgB,KAAa,MAAY;QAC9C,MAAM,WAAW,MAAM,UAAU,KAAK,CAAI,KAAK,MAAM;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,QAAQ,OAAgB,KAAa;QACnC,MAAM,WAAW,MAAM,UAAU,MAAM,CAAI,KAAK;QAChD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,eAAe;IACf,OAAO,CAAC,cACN,IAAI,IAAI,CAA0D,eAAe;IAEnF,iBAAiB;IACjB,eAAe,CAAC,cACd,IAAI,IAAI,CAA8D,wBAAwB;IAEhG,iBAAiB;IACjB,aAAa,IACX,IAAI,GAAG,CAAgB;IAEzB,SAAS;IACT,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF;AAEO,MAAM,UAAU;IACrB,uBAAuB;IACvB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAQ,CAAC,MAAM,EAAE,YAAY,EAAE;YAAE;QAAO;IAEjD,yBAAyB;IACzB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAM,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,QAAQ;IAE9C,kBAAkB;IAClB,YAAY,CAAC,YAAoB,OAC/B,IAAI,IAAI,CAAM,CAAC,MAAM,EAAE,YAAY,EAAE;IAEvC,gBAAgB;IAChB,YAAY,CAAC,YAAoB,QAAgB,UAC/C,IAAI,GAAG,CAAM,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE;IAEhD,eAAe;IACf,YAAY,CAAC,YAAoB,SAC/B,IAAI,MAAM,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,QAAQ;IAE5C,wBAAwB;IACxB,mBAAmB,CAAC,YAAoB,QAAgB,QACtD,IAAI,KAAK,CAAM,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAAE,cAAc;QAAM;IAErF,kBAAkB;IAClB,cAAc,CAAC,YAAoB,QAAgB,SACjD,IAAI,KAAK,CAAM,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE;YAAE,WAAW;QAAO;IAE7E,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,QAAgB,eACvD,IAAI,KAAK,CAAM,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAAE,cAAc;QAAa;AAC9F;AAEO,MAAM,eAAe;IAC1B,0BAA0B;IAC1B,WAAW,CAAC,aACV,IAAI,GAAG,CAAQ,CAAC,YAAY,EAAE,YAAY;IAE5C,2BAA2B;IAC3B,WAAW,CAAC,YAAoB,WAC9B,IAAI,GAAG,CAAM,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,UAAU;IAEtD,oBAAoB;IACpB,cAAc,CAAC,YAAoB,SACjC,IAAI,IAAI,CAAM,CAAC,YAAY,EAAE,YAAY,EAAE;IAE7C,kBAAkB;IAClB,cAAc,CAAC,YAAoB,UAAkB,UACnD,IAAI,GAAG,CAAM,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,UAAU,EAAE;IAExD,iBAAiB;IACjB,cAAc,CAAC,YAAoB,WACjC,IAAI,MAAM,CAAC,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,UAAU;AACtD;AAEO,MAAM,aAAa;IACxB,2BAA2B;IAC3B,YAAY,CAAC,aACX,IAAI,GAAG,CAAQ,CAAC,SAAS,EAAE,YAAY;IAEzC,4BAA4B;IAC5B,YAAY,CAAC,YAAoB,gBAC/B,IAAI,GAAG,CAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,eAAe;IAExD,qBAAqB;IACrB,eAAe,CAAC,YAAoB,UAClC,IAAI,IAAI,CAAM,CAAC,SAAS,EAAE,YAAY,EAAE;IAE1C,mBAAmB;IACnB,eAAe,CAAC,YAAoB,eAAuB,UACzD,IAAI,GAAG,CAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,eAAe,EAAE;IAE1D,kBAAkB;IAClB,eAAe,CAAC,YAAoB,gBAClC,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,eAAe;IAEtD,yBAAyB;IACzB,aAAa,CAAC,YAAoB,eAAuB,UACvD,IAAI,IAAI,CAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,cAAc,aAAa,CAAC,EAAE;YAAE,UAAU;QAAQ;AAC9F;AAEO,MAAM,kBAAkB;IAC7B,+BAA+B;IAC/B,iBAAiB,CAAC,aAChB,IAAI,GAAG,CAAQ,CAAC,cAAc,EAAE,YAAY;IAE9C,0BAA0B;IAC1B,oBAAoB,CAAC,YAAoB,eACvC,IAAI,IAAI,CAAM,CAAC,cAAc,EAAE,YAAY,EAAE;IAE/C,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,IAAY,UACnD,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE;IAEpD,uBAAuB;IACvB,oBAAoB,CAAC,YAAoB,KACvC,IAAI,MAAM,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,IAAI;AAClD;AAEO,MAAM,aAAa;IACxB,qBAAqB;IACrB,sBAAsB,CAAC,aACrB,IAAI,GAAG,CAAM,CAAC,SAAS,EAAE,WAAW,YAAY,CAAC;IAEnD,aAAa;IACb,cAAc,CAAC,aACb,IAAI,GAAG,CAAM,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC;IAE3C,yBAAyB;IACzB,yBAAyB,CAAC,aACxB,IAAI,GAAG,CAAM,CAAC,SAAS,EAAE,WAAW,gBAAgB,CAAC;IAEvD,kBAAkB;IAClB,mBAAmB,CAAC,aAClB,IAAI,GAAG,CAAM,CAAC,SAAS,EAAE,WAAW,SAAS,CAAC;AAClD;AAEO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,aAAa,IACX,IAAI,GAAG,CAAQ;IAEjB,6BAA6B;IAC7B,aAAa,CAAC,KACZ,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,IAAI;IAEhC,sBAAsB;IACtB,gBAAgB,CAAC,WACf,IAAI,IAAI,CAAM,aAAa;IAE7B,oBAAoB;IACpB,gBAAgB,CAAC,IAAY,UAC3B,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,IAAI,EAAE;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { User, Cantier<PERSON> } from '@/types'\nimport { authApi } from '@/lib/api'\n\ninterface AuthContextType {\n  user: User | null\n  cantiere: Cantiere | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  login: (username: string, password: string) => Promise<void>\n  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>\n  logout: () => void\n  checkAuth: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [cantiere, setCantiere] = useState<Cantiere | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  const isAuthenticated = !!user || !!cantiere\n\n  // Verifica l'autenticazione al caricamento\n  useEffect(() => {\n    checkAuth()\n  }, [])\n\n  const checkAuth = async () => {\n    try {\n      const token = localStorage.getItem('access_token')\n      if (!token) {\n        setIsLoading(false)\n        return\n      }\n\n      const response = await authApi.verifyToken()\n      \n      if (response.user) {\n        setUser(response.user)\n      } else if (response.cantiere) {\n        setCantiere(response.cantiere)\n      }\n    } catch (error) {\n      console.error('Errore verifica autenticazione:', error)\n      localStorage.removeItem('access_token')\n      localStorage.removeItem('user_data')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const login = async (username: string, password: string) => {\n    try {\n      setIsLoading(true)\n      const response = await authApi.login({ username, password })\n      \n      localStorage.setItem('access_token', response.access_token)\n      localStorage.setItem('user_data', JSON.stringify(response.user))\n      \n      setUser(response.user)\n      setCantiere(null)\n    } catch (error) {\n      console.error('Errore login:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {\n    try {\n      setIsLoading(true)\n      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })\n      \n      localStorage.setItem('access_token', response.access_token)\n      localStorage.setItem('cantiere_data', JSON.stringify(response.cantiere))\n      \n      setCantiere(response.cantiere)\n      setUser(null)\n    } catch (error) {\n      console.error('Errore login cantiere:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const logout = () => {\n    localStorage.removeItem('access_token')\n    localStorage.removeItem('user_data')\n    localStorage.removeItem('cantiere_data')\n    setUser(null)\n    setCantiere(null)\n    window.location.href = '/login'\n  }\n\n  const value: AuthContextType = {\n    user,\n    cantiere,\n    isAuthenticated,\n    isLoading,\n    login,\n    loginCantiere,\n    logout,\n    checkAuth,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEpC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,aAAa;gBACb;YACF;YAEA,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW;YAE1C,IAAI,SAAS,IAAI,EAAE;gBACjB,QAAQ,SAAS,IAAI;YACvB,OAAO,IAAI,SAAS,QAAQ,EAAE;gBAC5B,YAAY,SAAS,QAAQ;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAU;YAAS;YAE1D,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;YAC1D,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,IAAI;YAE9D,QAAQ,SAAS,IAAI;YACrB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,OAAO,iBAAyB;QACpD,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,aAAa,CAAC;gBAAE;gBAAiB;YAAkB;YAElF,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;YAC1D,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC,SAAS,QAAQ;YAEtE,YAAY,SAAS,QAAQ;YAC7B,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,QAAQ;QACR,YAAY;QACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAjGgB;KAAA", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport {\n  Cable,\n  Home,\n  Activity,\n  BarChart3,\n  Settings,\n  Users,\n  Menu,\n  X,\n  Building2,\n  ClipboardList,\n  FileText,\n  LogOut,\n  Package\n} from 'lucide-react'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: Home },\n  { name: 'Cantieri', href: '/cantieri', icon: Building2 },\n  { name: 'Gestione Cavi', href: '/cavi', icon: Cable },\n  { name: '<PERSON><PERSON><PERSON>', href: '/parco-cavi', icon: Package },\n  { name: 'Comande', href: '/comande', icon: ClipboardList },\n  { name: 'Produttività', href: '/productivity', icon: Activity },\n  { name: 'Report', href: '/reports', icon: BarChart3 },\n  { name: 'Certificazioni', href: '/certificazioni', icon: FileText },\n  { name: 'Amministrazione', href: '/admin', icon: Settings },\n]\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, cantiere, isAuthenticated, logout } = useAuth()\n\n  // Non mostrare navbar nella pagina di login\n  if (pathname === '/login') {\n    return null\n  }\n\n  // Se non autenticato, non mostrare navbar\n  if (!isAuthenticated) {\n    return null\n  }\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          \n          {/* Logo e Brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\">\n                <Cable className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-slate-900\">CABLYS</h1>\n                <p className=\"text-xs text-slate-500 -mt-1\">Cable Installation System</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Navigation Desktop */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || \n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n              \n              return (\n                <Link key={item.name} href={item.href}>\n                  <Button\n                    variant={isActive ? \"default\" : \"ghost\"}\n                    size=\"sm\"\n                    className={`flex items-center space-x-2 ${\n                      isActive \n                        ? 'bg-blue-600 text-white hover:bg-blue-700' \n                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'\n                    }`}\n                  >\n                    <Icon className=\"w-4 h-4\" />\n                    <span className=\"hidden lg:inline\">{item.name}</span>\n                  </Button>\n                </Link>\n              )\n            })}\n          </div>\n\n          {/* User Info e Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            \n            {/* User Info */}\n            <div className=\"hidden sm:flex items-center space-x-3\">\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-slate-900\">\n                  {user ? user.username : cantiere?.commessa}\n                </p>\n                <p className=\"text-xs text-slate-500\">\n                  {user ? user.ruolo : 'Cantiere'}\n                </p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                {user ? <Users className=\"w-4 h-4 text-white\" /> : <Building2 className=\"w-4 h-4 text-white\" />}\n              </div>\n              <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n                Online\n              </Badge>\n              <Button variant=\"ghost\" size=\"sm\" onClick={logout}>\n                <LogOut className=\"w-4 h-4\" />\n              </Button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"text-slate-600\"\n              >\n                {isOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden border-t border-slate-200 bg-white\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || \n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n              \n              return (\n                <Link key={item.name} href={item.href}>\n                  <Button\n                    variant={isActive ? \"default\" : \"ghost\"}\n                    size=\"sm\"\n                    className={`w-full justify-start space-x-3 ${\n                      isActive \n                        ? 'bg-blue-600 text-white' \n                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'\n                    }`}\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <Icon className=\"w-4 h-4\" />\n                    <span>{item.name}</span>\n                  </Button>\n                </Link>\n              )\n            })}\n          </div>\n          \n          {/* Mobile User Info */}\n          <div className=\"border-t border-slate-200 px-4 py-3\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                <Users className=\"w-4 h-4 text-white\" />\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-slate-900\">Admin User</p>\n                <p className=\"text-xs text-slate-500\">Cantiere Demo</p>\n              </div>\n              <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800 ml-auto\">\n                Online\n              </Badge>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAwBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC3C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,mNAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAiB,MAAM;QAAS,MAAM,uMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,2MAAA,CAAA,UAAO;IAAC;IACzD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,2NAAA,CAAA,gBAAa;IAAC;IACzD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAC9D;QAAE,MAAM;QAAU,MAAM;QAAY,MAAM,qNAAA,CAAA,YAAS;IAAC;IACpD;QAAE,MAAM;QAAkB,MAAM;QAAmB,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAClE;QAAE,MAAM;QAAmB,MAAM;QAAU,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC3D;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE1D,4CAA4C;IAC5C,IAAI,aAAa,UAAU;QACzB,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;sCAMlD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;gCAClE,MAAM,OAAO,KAAK,IAAI;gCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAAiB,MAAM,KAAK,IAAI;8CACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,WAAW,YAAY;wCAChC,MAAK;wCACL,WAAW,CAAC,4BAA4B,EACtC,WACI,6CACA,0DACJ;;0DAEF,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAoB,KAAK,IAAI;;;;;;;;;;;;mCAXtC,KAAK,IAAI;;;;;4BAexB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAGb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,OAAO,KAAK,QAAQ,GAAG,UAAU;;;;;;8DAEpC,6LAAC;oDAAE,WAAU;8DACV,OAAO,KAAK,KAAK,GAAG;;;;;;;;;;;;sDAGzB,6LAAC;4CAAI,WAAU;sDACZ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAA0B,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAE1E,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAA8B;;;;;;sDAGnE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,SAAS;sDACzC,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,UAAU,CAAC;wCAC1B,WAAU;kDAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/D,wBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;4BAClE,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,WAAW,YAAY;oCAChC,MAAK;oCACL,WAAW,CAAC,+BAA+B,EACzC,WACI,2BACA,0DACJ;oCACF,SAAS,IAAM,UAAU;;sDAEzB,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;;;;;;+BAZT,KAAK,IAAI;;;;;wBAgBxB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAExC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzF;GAlJgB;;QAEG,qIAAA,CAAA,cAAW;QACwB,kIAAA,CAAA,UAAO;;;KAH7C", "debugId": null}}]}