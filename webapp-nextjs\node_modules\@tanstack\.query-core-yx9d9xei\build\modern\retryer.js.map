{"version": 3, "sources": ["../../src/retryer.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { pendingThenable } from './thenable'\nimport { isServer, sleep } from './utils'\nimport type { CancelOptions, DefaultError, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = DefaultError> {\n  fn: () => TData | Promise<TData>\n  initialPromise?: Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n  canRun: () => boolean\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n  canStart: () => boolean\n  start: () => Promise<TData>\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError extends Error {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    super('CancelledError')\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = DefaultError>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => void) | undefined\n\n  const thenable = pendingThenable<TData>()\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const canContinue = () =>\n    focusManager.isFocused() &&\n    (config.networkMode === 'always' || onlineManager.isOnline()) &&\n    config.canRun()\n\n  const canStart = () => canFetch(config.networkMode) && config.canRun()\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      thenable.resolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      thenable.reject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value)\n        }\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // we can re-use config.initialPromise on the first call of run()\n    const initialPromise =\n      failureCount === 0 ? config.initialPromise : undefined\n\n    // Execute query\n    try {\n      promiseOrValue = initialPromise ?? config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? (isServer ? 0 : 3)\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            return canContinue() ? undefined : pause()\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.()\n      return thenable\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      // Start loop\n      if (canStart()) {\n        run()\n      } else {\n        pause().then(run)\n      }\n      return thenable\n    },\n  }\n}\n"], "mappings": ";AAAA,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAChC,SAAS,UAAU,aAAa;AA4ChC,SAAS,kBAAkB,cAAsB;AAC/C,SAAO,KAAK,IAAI,MAAO,KAAK,cAAc,GAAK;AACjD;AAEO,SAAS,SAAS,aAA+C;AACtE,UAAQ,eAAe,cAAc,WACjC,cAAc,SAAS,IACvB;AACN;AAEO,IAAM,iBAAN,cAA6B,MAAM;AAAA,EAGxC,YAAY,SAAyB;AACnC,UAAM,gBAAgB;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AACF;AAEO,SAAS,iBAAiB,OAAqC;AACpE,SAAO,iBAAiB;AAC1B;AAEO,SAAS,cACd,QACgB;AAChB,MAAI,mBAAmB;AACvB,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI;AAEJ,QAAM,WAAW,gBAAuB;AAExC,QAAM,SAAS,CAAC,kBAAwC;AACtD,QAAI,CAAC,YAAY;AACf,aAAO,IAAI,eAAe,aAAa,CAAC;AAExC,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,QAAM,cAAc,MAAM;AACxB,uBAAmB;AAAA,EACrB;AAEA,QAAM,gBAAgB,MAAM;AAC1B,uBAAmB;AAAA,EACrB;AAEA,QAAM,cAAc,MAClB,aAAa,UAAU,MACtB,OAAO,gBAAgB,YAAY,cAAc,SAAS,MAC3D,OAAO,OAAO;AAEhB,QAAM,WAAW,MAAM,SAAS,OAAO,WAAW,KAAK,OAAO,OAAO;AAErE,QAAM,UAAU,CAAC,UAAe;AAC9B,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,aAAO,YAAY,KAAK;AACxB,mBAAa;AACb,eAAS,QAAQ,KAAK;AAAA,IACxB;AAAA,EACF;AAEA,QAAM,SAAS,CAAC,UAAe;AAC7B,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,aAAO,UAAU,KAAK;AACtB,mBAAa;AACb,eAAS,OAAO,KAAK;AAAA,IACvB;AAAA,EACF;AAEA,QAAM,QAAQ,MAAM;AAClB,WAAO,IAAI,QAAQ,CAAC,oBAAoB;AACtC,mBAAa,CAAC,UAAU;AACtB,YAAI,cAAc,YAAY,GAAG;AAC/B,0BAAgB,KAAK;AAAA,QACvB;AAAA,MACF;AACA,aAAO,UAAU;AAAA,IACnB,CAAC,EAAE,KAAK,MAAM;AACZ,mBAAa;AACb,UAAI,CAAC,YAAY;AACf,eAAO,aAAa;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAM,MAAM,MAAM;AAEhB,QAAI,YAAY;AACd;AAAA,IACF;AAEA,QAAI;AAGJ,UAAM,iBACJ,iBAAiB,IAAI,OAAO,iBAAiB;AAG/C,QAAI;AACF,uBAAiB,kBAAkB,OAAO,GAAG;AAAA,IAC/C,SAAS,OAAO;AACd,uBAAiB,QAAQ,OAAO,KAAK;AAAA,IACvC;AAEA,YAAQ,QAAQ,cAAc,EAC3B,KAAK,OAAO,EACZ,MAAM,CAAC,UAAU;AAEhB,UAAI,YAAY;AACd;AAAA,MACF;AAGA,YAAM,QAAQ,OAAO,UAAU,WAAW,IAAI;AAC9C,YAAM,aAAa,OAAO,cAAc;AACxC,YAAM,QACJ,OAAO,eAAe,aAClB,WAAW,cAAc,KAAK,IAC9B;AACN,YAAM,cACJ,UAAU,QACT,OAAO,UAAU,YAAY,eAAe,SAC5C,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK;AAE3D,UAAI,oBAAoB,CAAC,aAAa;AAEpC,eAAO,KAAK;AACZ;AAAA,MACF;AAEA;AAGA,aAAO,SAAS,cAAc,KAAK;AAGnC,YAAM,KAAK,EAER,KAAK,MAAM;AACV,eAAO,YAAY,IAAI,SAAY,MAAM;AAAA,MAC3C,CAAC,EACA,KAAK,MAAM;AACV,YAAI,kBAAkB;AACpB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,cAAI;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAEA,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM;AACd,mBAAa;AACb,aAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,MAAM;AAEX,UAAI,SAAS,GAAG;AACd,YAAI;AAAA,MACN,OAAO;AACL,cAAM,EAAE,KAAK,GAAG;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}