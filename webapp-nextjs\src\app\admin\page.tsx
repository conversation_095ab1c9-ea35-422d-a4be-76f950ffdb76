'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { api, cantieriApi } from '@/lib/api'
import { User, Cantiere } from '@/types'
import { 
  Settings, 
  Users, 
  Building2, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Eye,
  Shield,
  Key,
  Loader2
} from 'lucide-react'

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('users')
  const [searchTerm, setSearchTerm] = useState('')
  const [users, setUsers] = useState<User[]>([])
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user } = useAuth()

  // Carica dati dal backend
  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')
      
      if (activeTab === 'users') {
        const usersData = await api.get('/admin/users')
        setUsers(usersData)
      } else if (activeTab === 'cantieri') {
        const cantieriData = await cantieriApi.getCantieri()
        setCantieri(cantieriData)
      }
    } catch (error: any) {
      console.error('Errore caricamento dati:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const getRuoloBadge = (ruolo: string) => {
    switch (ruolo) {
      case 'owner':
        return <Badge className="bg-purple-100 text-purple-800">Owner</Badge>
      case 'user':
        return <Badge className="bg-blue-100 text-blue-800">User</Badge>
      case 'cantieri_user':
        return <Badge className="bg-green-100 text-green-800">Cantieri User</Badge>
      default:
        return <Badge variant="secondary">{ruolo}</Badge>
    }
  }

  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {
    if (!abilitato) {
      return <Badge className="bg-red-100 text-red-800">Disabilitato</Badge>
    }
    
    if (data_scadenza) {
      const scadenza = new Date(data_scadenza)
      const oggi = new Date()
      
      if (scadenza < oggi) {
        return <Badge className="bg-red-100 text-red-800">Scaduto</Badge>
      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {
        return <Badge className="bg-yellow-100 text-yellow-800">In Scadenza</Badge>
      }
    }
    
    return <Badge className="bg-green-100 text-green-800">Attivo</Badge>
  }

  const filteredUsers = users.filter(u => 
    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredCantieri = cantieri.filter(c => 
    c.commessa?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    c.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    c.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Verifica se l'utente ha permessi di amministrazione
  if (user?.ruolo !== 'owner') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-slate-900 mb-2">Accesso Negato</h2>
            <p className="text-slate-600">Non hai i permessi necessari per accedere a questa sezione.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Settings className="h-8 w-8 text-blue-600" />
              Amministrazione
            </h1>
            <p className="text-slate-600 mt-1">Gestione utenti, cantieri e configurazioni di sistema</p>
          </div>
          
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            {activeTab === 'users' ? 'Nuovo Utente' : 'Nuovo Cantiere'}
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex gap-2">
          <Button
            variant={activeTab === 'users' ? 'default' : 'outline'}
            onClick={() => setActiveTab('users')}
            className="flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Utenti
          </Button>
          <Button
            variant={activeTab === 'cantieri' ? 'default' : 'outline'}
            onClick={() => setActiveTab('cantieri')}
            className="flex items-center gap-2"
          >
            <Building2 className="h-4 w-4" />
            Cantieri
          </Button>
          <Button
            variant={activeTab === 'settings' ? 'default' : 'outline'}
            onClick={() => setActiveTab('settings')}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Configurazioni
          </Button>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 text-slate-500" />
              <Input
                placeholder={`Cerca ${activeTab === 'users' ? 'utenti' : 'cantieri'}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1"
              />
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        {activeTab === 'users' && (
          <Card>
            <CardHeader>
              <CardTitle>Gestione Utenti</CardTitle>
              <CardDescription>Amministrazione utenti e permessi di sistema</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Username</TableHead>
                      <TableHead>Ragione Sociale</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Ruolo</TableHead>
                      <TableHead>Stato</TableHead>
                      <TableHead>Scadenza</TableHead>
                      <TableHead>Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Caricamento utenti...
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : error ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="flex items-center justify-center gap-2 text-red-600">
                            <AlertCircle className="h-4 w-4" />
                            {error}
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                          Nessun utente trovato
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((utente) => (
                        <TableRow key={utente.id_utente}>
                          <TableCell className="font-medium">{utente.username}</TableCell>
                          <TableCell>{utente.ragione_sociale || '-'}</TableCell>
                          <TableCell>{utente.email || '-'}</TableCell>
                          <TableCell>{getRuoloBadge(utente.ruolo)}</TableCell>
                          <TableCell>{getStatusBadge(utente.abilitato, utente.data_scadenza)}</TableCell>
                          <TableCell>
                            {utente.data_scadenza ? 
                              new Date(utente.data_scadenza).toLocaleDateString('it-IT') : 
                              'Nessuna'
                            }
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Key className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'cantieri' && (
          <Card>
            <CardHeader>
              <CardTitle>Gestione Cantieri</CardTitle>
              <CardDescription>Amministrazione cantieri e configurazioni</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Commessa</TableHead>
                      <TableHead>Descrizione</TableHead>
                      <TableHead>Cliente</TableHead>
                      <TableHead>Ubicazione</TableHead>
                      <TableHead>Data Creazione</TableHead>
                      <TableHead>Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Caricamento cantieri...
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : filteredCantieri.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8 text-slate-500">
                          Nessun cantiere trovato
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredCantieri.map((cantiere) => (
                        <TableRow key={cantiere.id_cantiere}>
                          <TableCell className="font-medium">{cantiere.commessa}</TableCell>
                          <TableCell>{cantiere.descrizione}</TableCell>
                          <TableCell>{cantiere.nome_cliente || '-'}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{cantiere.citta_cantiere || '-'}</div>
                              <div className="text-slate-500">{cantiere.nazione_cantiere || '-'}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {new Date(cantiere.data_creazione).toLocaleDateString('it-IT')}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'settings' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Configurazioni Sistema</CardTitle>
                <CardDescription>Impostazioni generali del sistema</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Backup Automatico</span>
                    <Badge className="bg-green-100 text-green-800">Attivo</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Notifiche Email</span>
                    <Badge className="bg-green-100 text-green-800">Attivo</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Log Audit</span>
                    <Badge className="bg-green-100 text-green-800">Attivo</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Statistiche Sistema</CardTitle>
                <CardDescription>Informazioni generali del sistema</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Utenti Totali</span>
                    <span className="font-medium">{users.length}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Cantieri Attivi</span>
                    <span className="font-medium">{cantieri.length}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Versione Sistema</span>
                    <span className="font-medium">2.0.0</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

      </div>
    </div>
  )
}
