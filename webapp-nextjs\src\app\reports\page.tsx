'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useAuth } from '@/contexts/AuthContext'
import { reportsApi } from '@/lib/api'
import { ReportAvanzamento, ReportBOQ } from '@/types'
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  Target,
  Activity,
  Clock,
  CheckCircle,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [reportAvanzamento, setReportAvanzamento] = useState<ReportAvanzamento | null>(null)
  const [reportBOQ, setReportBOQ] = useState<ReportBOQ | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()

  // Carica i report dal backend
  useEffect(() => {
    loadReports()
  }, [])

  const loadReports = async () => {
    try {
      setIsLoading(true)
      setError('')

      const cantiereId = cantiere?.id_cantiere || user?.id_utente
      if (!cantiereId) {
        setError('Cantiere non selezionato')
        return
      }

      const [avanzamentoData, boqData] = await Promise.all([
        reportsApi.getReportAvanzamento(cantiereId),
        reportsApi.getReportBOQ(cantiereId)
      ])

      setReportAvanzamento(avanzamentoData)
      setReportBOQ(boqData)
    } catch (error: any) {
      console.error('Errore caricamento report:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei report')
    } finally {
      setIsLoading(false)
    }
  }

  // Dati per i grafici basati sui report reali
  const statusData = reportAvanzamento ? [
    { name: 'Installati', value: reportAvanzamento.cavi_posati, color: '#22c55e' },
    { name: 'Collegati', value: reportAvanzamento.cavi_collegati, color: '#3b82f6' },
    { name: 'Certificati', value: reportAvanzamento.cavi_certificati, color: '#f59e0b' },
    { name: 'Da Installare', value: reportAvanzamento.totale_cavi - reportAvanzamento.cavi_posati, color: '#94a3b8' }
  ] : []

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              Report e Analytics
            </h1>
            <p className="text-slate-600 mt-1">Analisi dettagliate dell'avanzamento del cantiere</p>
          </div>
          
          <div className="flex gap-2">
            {['week', 'month', 'quarter'].map((period) => (
              <Button
                key={period}
                variant={selectedPeriod === period ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(period)}
                className="capitalize"
              >
                {period === 'week' ? 'Settimana' : period === 'month' ? 'Mese' : 'Trimestre'}
              </Button>
            ))}
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Esporta PDF
            </Button>
          </div>
        </div>

        {/* KPI Overview */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Caricamento report...</span>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-6 w-6" />
              <span>{error}</span>
            </div>
          </div>
        ) : reportAvanzamento ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-slate-600">Completamento Totale</CardTitle>
                <Target className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-900">{reportAvanzamento.percentuale_completamento.toFixed(1)}%</div>
                <Progress value={reportAvanzamento.percentuale_completamento} className="mt-2" />
                <p className="text-xs text-slate-500 mt-2">
                  {reportAvanzamento.cavi_posati} di {reportAvanzamento.totale_cavi} cavi
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-slate-600">Media Giornaliera</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-900">{reportAvanzamento.media_giornaliera.toFixed(1)}</div>
                <p className="text-xs text-slate-500">cavi/giorno</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600">Basato su {reportAvanzamento.giorni_lavorativi} giorni</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-purple-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-slate-600">Metri Completati</CardTitle>
                <Activity className="h-4 w-4 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-900">{reportAvanzamento.percentuale_metri.toFixed(1)}%</div>
                <p className="text-xs text-slate-500">{reportAvanzamento.metri_posati}m di {reportAvanzamento.metri_totali}m</p>
                <div className="flex items-center mt-2">
                  <CheckCircle className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600">Metrature</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-orange-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-slate-600">Stima Completamento</CardTitle>
                <Clock className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-900">{reportAvanzamento.stima_completamento}</div>
                <p className="text-xs text-slate-500">al ritmo attuale</p>
                <div className="flex items-center mt-2">
                  <Calendar className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600">Proiezione</span>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : null}

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          
          {/* Installation Trend */}
          <Card>
            <CardHeader>
              <CardTitle>Trend Installazioni Settimanali</CardTitle>
              <CardDescription>Confronto installazioni vs target giornaliero</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={[]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="target" fill="#e2e8f0" name="Target" />
                  <Bar dataKey="installati" fill="#3b82f6" name="Installati" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Distribuzione Stato Cavi</CardTitle>
              <CardDescription>Panoramica dello stato di avanzamento</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Progress by Sector */}
        <Card>
          <CardHeader>
            <CardTitle>Avanzamento per Settore</CardTitle>
            <CardDescription>Stato di completamento dettagliato per ogni settore</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center py-8 text-slate-500">
                Dati di avanzamento per settore saranno disponibili quando collegati al backend
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Team</CardTitle>
            <CardDescription>Statistiche dettagliate per ogni squadra di lavoro</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-slate-500">
              Statistiche team saranno disponibili quando collegati al backend
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
