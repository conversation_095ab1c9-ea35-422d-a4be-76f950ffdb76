{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "42552a44eabf3c0f037eccd495115946", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8942d03dc85b276a61151c1b3ce355608e7a7dad1ef857835e1c1c0722c7df33", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "530f1a72e503cf91db20a9546a0848559ea6817d092c3edf7f1708c576ec3061"}}}, "sortedMiddleware": ["/"], "functions": {}}