{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\WorkLogsList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Typography, Grid, Card, CardContent, CardHeader, TextField, Select, MenuItem, FormControl, InputLabel, Button, Paper, Chip, Avatar, Divider, Alert, CircularProgress, IconButton, Tooltip, Badge, LinearProgress, Pagination, Stack, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Assignment, Person, Schedule, Engineering, TrendingUp, FilterList, Clear, Edit, Delete, Visibility, Add, ExpandMore, Speed, Timer, Group, LocationOn, Build, Assessment } from '@mui/icons-material';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WorkLogsList = ({\n  onEdit,\n  onDelete,\n  onCreateNew\n}) => {\n  _s();\n  const [workLogs, setWorkLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    activity_type: '',\n    operator_id: '',\n    id_cantiere: '',\n    start_date: '',\n    end_date: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total_count: 0\n  });\n  const [operators, setOperators] = useState([]);\n  const [cantieri, setCantieri] = useState([]);\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n  useEffect(() => {\n    loadWorkLogs();\n  }, [filters, pagination.page]);\n  const loadInitialData = async () => {\n    try {\n      const [operatorsRes, cantieriRes] = await Promise.all([axiosInstance.get('/responsabili'), axiosInstance.get('/cantieri')]);\n      setOperators(operatorsRes.data || []);\n      setCantieri(cantieriRes.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n    }\n  };\n  const loadWorkLogs = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: pagination.page,\n        per_page: pagination.per_page,\n        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value !== ''))\n      };\n      const response = await axiosInstance.get('/v1/work-logs', {\n        params\n      });\n      setWorkLogs(response.data.work_logs || []);\n      setPagination(prev => ({\n        ...prev,\n        total_count: response.data.total_count || 0\n      }));\n    } catch (error) {\n      console.error('Errore nel caricamento work logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFilterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    })); // Reset alla prima pagina\n  };\n  const clearFilters = () => {\n    setFilters({\n      activity_type: '',\n      operator_id: '',\n      id_cantiere: '',\n      start_date: '',\n      end_date: ''\n    });\n  };\n  const formatDateTime = dateString => {\n    return new Date(dateString).toLocaleString('it-IT', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const formatDuration = minutes => {\n    if (!minutes) return '-';\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n  };\n  const getActivityIcon = activity => {\n    switch (activity) {\n      case 'Posa':\n        return '🔧';\n      case 'Collegamento':\n        return '🔌';\n      case 'Certificazione':\n        return '✅';\n      default:\n        return '📝';\n    }\n  };\n  const getConditionColor = condition => {\n    switch (condition) {\n      case 'Normale':\n        return 'bg-green-100 text-green-800';\n      case 'Spazi Ristretti':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'In Altezza':\n        return 'bg-orange-100 text-orange-800';\n      case 'Esterno':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const totalPages = Math.ceil(pagination.total_count / pagination.per_page);\n  if (loading && workLogs.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: \"Caricamento work logs...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-800\",\n          children: \"\\uD83D\\uDCDD Work Logs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onCreateNew,\n          className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: \"\\u2795 Nuovo Work Log\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-bold text-blue-600\",\n            children: pagination.total_count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Totale Work Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-bold text-green-600\",\n            children: workLogs.reduce((sum, log) => sum + (log.quantity || 0), 0).toFixed(1)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Quantit\\xE0 Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-bold text-orange-600\",\n            children: workLogs.reduce((sum, log) => sum + (log.total_man_hours || 0), 0).toFixed(1)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Ore-Uomo Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-bold text-purple-600\",\n            children: workLogs.length > 0 ? (workLogs.reduce((sum, log) => sum + (log.productivity_per_hour || 0), 0) / workLogs.length).toFixed(2) : '0.00'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Produttivit\\xE0 Media\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-800 mb-4\",\n        children: \"\\uD83D\\uDD0D Filtri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Attivit\\xE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"activity_type\",\n            value: filters.activity_type,\n            onChange: handleFilterChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Tutte\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Posa\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Collegamento\",\n              children: \"Collegamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Certificazione\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Operatore\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"operator_id\",\n            value: filters.operator_id,\n            onChange: handleFilterChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Tutti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), operators.map(op => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: op.id_responsabile,\n              children: op.nome_responsabile\n            }, op.id_responsabile, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"id_cantiere\",\n            value: filters.id_cantiere,\n            onChange: handleFilterChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Tutti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cantiere.id_cantiere,\n              children: cantiere.commessa\n            }, cantiere.id_cantiere, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Data Inizio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            name: \"start_date\",\n            value: filters.start_date,\n            onChange: handleFilterChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Data Fine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            name: \"end_date\",\n            value: filters.end_date,\n            onChange: handleFilterChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 flex justify-end\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearFilters,\n          className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: \"\\uD83D\\uDDD1\\uFE0F Pulisci Filtri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n      children: workLogs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-lg\",\n          children: \"Nessun work log trovato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm mt-2\",\n          children: Object.values(filters).some(v => v !== '') ? 'Prova a modificare i filtri o' : 'Inizia creando il tuo primo work log'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onCreateNew,\n          className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: \"\\u2795 Crea Primo Work Log\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Attivit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Operatore\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Quantit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Durata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Produttivit\\xE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Condizioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Azioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: workLogs.map(log => {\n                var _log$quantity, _log$productivity_per, _log$productivity_per2;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"hover:bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-lg mr-2\",\n                        children: getActivityIcon(log.activity_type)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 370,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm font-medium text-gray-900\",\n                          children: log.activity_type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 372,\n                          columnNumber: 29\n                        }, this), log.sub_activity_detail && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-500\",\n                          children: log.sub_activity_detail\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 376,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 371,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                    children: [\"Operatore #\", log.operator_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                    children: [(_log$quantity = log.quantity) === null || _log$quantity === void 0 ? void 0 : _log$quantity.toFixed(1), \" \", log.activity_type === 'Posa' ? 'm' : 'unità']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                    children: [formatDuration(log.duration_minutes), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [log.number_of_operators_on_task, \" operatori\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 391,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                    children: [(_log$productivity_per = log.productivity_per_hour) === null || _log$productivity_per === void 0 ? void 0 : _log$productivity_per.toFixed(2), \" /h\", /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [(_log$productivity_per2 = log.productivity_per_person_per_hour) === null || _log$productivity_per2 === void 0 ? void 0 : _log$productivity_per2.toFixed(2), \" /h/op\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConditionColor(log.environmental_conditions)}`,\n                      children: log.environmental_conditions\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: log.tools_used\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                    children: formatDateTime(log.start_timestamp)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => onEdit(log),\n                      className: \"text-blue-600 hover:text-blue-900\",\n                      title: \"Modifica\",\n                      children: \"\\u270F\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => onDelete(log.id),\n                      className: \"text-red-600 hover:text-red-900\",\n                      title: \"Elimina\",\n                      children: \"\\uD83D\\uDDD1\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this)]\n                }, log.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-between sm:hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setPagination(prev => ({\n                ...prev,\n                page: Math.max(1, prev.page - 1)\n              })),\n              disabled: pagination.page === 1,\n              className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\",\n              children: \"Precedente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setPagination(prev => ({\n                ...prev,\n                page: Math.min(totalPages, prev.page + 1)\n              })),\n              disabled: pagination.page === totalPages,\n              className: \"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\",\n              children: \"Successiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700\",\n                children: [\"Mostrando\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: (pagination.page - 1) * pagination.per_page + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 23\n                }, this), ' ', \"a\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: Math.min(pagination.page * pagination.per_page, pagination.total_count)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 23\n                }, this), ' ', \"di\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: pagination.total_count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 23\n                }, this), ' ', \"risultati\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"nav\", {\n                className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setPagination(prev => ({\n                    ...prev,\n                    page: Math.max(1, prev.page - 1)\n                  })),\n                  disabled: pagination.page === 1,\n                  className: \"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\",\n                  children: \"\\u2039\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 23\n                }, this), Array.from({\n                  length: Math.min(5, totalPages)\n                }, (_, i) => {\n                  const pageNum = i + 1;\n                  return /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setPagination(prev => ({\n                      ...prev,\n                      page: pageNum\n                    })),\n                    className: `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${pagination.page === pageNum ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`,\n                    children: pageNum\n                  }, pageNum, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 27\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setPagination(prev => ({\n                    ...prev,\n                    page: Math.min(totalPages, prev.page + 1)\n                  })),\n                  disabled: pagination.page === totalPages,\n                  className: \"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\",\n                  children: \"\\u203A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkLogsList, \"pPTC4B+JwM6T3aACacbQ0ADKGXM=\");\n_c = WorkLogsList;\nexport default WorkLogsList;\nvar _c;\n$RefreshReg$(_c, \"WorkLogsList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Paper", "Chip", "Avatar", "Divider", "<PERSON><PERSON>", "CircularProgress", "IconButton", "<PERSON><PERSON><PERSON>", "Badge", "LinearProgress", "Pagination", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Assignment", "Person", "Schedule", "Engineering", "TrendingUp", "FilterList", "Clear", "Edit", "Delete", "Visibility", "Add", "ExpandMore", "Speed", "Timer", "Group", "LocationOn", "Build", "Assessment", "axiosInstance", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WorkLogsList", "onEdit", "onDelete", "onCreateNew", "_s", "workLogs", "setWorkLogs", "loading", "setLoading", "filters", "setFilters", "activity_type", "operator_id", "id_cantiere", "start_date", "end_date", "pagination", "setPagination", "page", "per_page", "total_count", "operators", "setOperators", "cantieri", "set<PERSON><PERSON><PERSON>", "loadInitialData", "loadWorkLogs", "operatorsRes", "cantieriRes", "Promise", "all", "get", "data", "error", "console", "params", "Object", "fromEntries", "entries", "filter", "_", "value", "response", "work_logs", "prev", "handleFilterChange", "e", "name", "target", "clearFilters", "formatDateTime", "dateString", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "getActivityIcon", "activity", "getConditionColor", "condition", "totalPages", "ceil", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "reduce", "sum", "log", "quantity", "toFixed", "total_man_hours", "productivity_per_hour", "onChange", "map", "op", "id_responsabile", "nome_responsabile", "cantiere", "commessa", "type", "values", "some", "v", "_log$quantity", "_log$productivity_per", "_log$productivity_per2", "sub_activity_detail", "duration_minutes", "number_of_operators_on_task", "productivity_per_person_per_hour", "environmental_conditions", "tools_used", "start_timestamp", "title", "id", "max", "disabled", "min", "Array", "from", "i", "pageNum", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/WorkLogsList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Button,\n  Paper,\n  Chip,\n  Avatar,\n  Divider,\n  Alert,\n  CircularProgress,\n  IconButton,\n  Tooltip,\n  Badge,\n  LinearProgress,\n  Pagination,\n  Stack,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Assignment,\n  Person,\n  Schedule,\n  Engineering,\n  TrendingUp,\n  FilterList,\n  Clear,\n  Edit,\n  Delete,\n  Visibility,\n  Add,\n  ExpandMore,\n  Speed,\n  Timer,\n  Group,\n  LocationOn,\n  Build,\n  Assessment\n} from '@mui/icons-material';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst WorkLogsList = ({ onEdit, onDelete, onCreateNew }) => {\n  const [workLogs, setWorkLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    activity_type: '',\n    operator_id: '',\n    id_cantiere: '',\n    start_date: '',\n    end_date: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total_count: 0\n  });\n  const [operators, setOperators] = useState([]);\n  const [cantieri, setCantieri] = useState([]);\n\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n\n  useEffect(() => {\n    loadWorkLogs();\n  }, [filters, pagination.page]);\n\n  const loadInitialData = async () => {\n    try {\n      const [operatorsRes, cantieriRes] = await Promise.all([\n        axiosInstance.get('/responsabili'),\n        axiosInstance.get('/cantieri')\n      ]);\n\n      setOperators(operatorsRes.data || []);\n      setCantieri(cantieriRes.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati iniziali:', error);\n    }\n  };\n\n  const loadWorkLogs = async () => {\n    try {\n      setLoading(true);\n      \n      const params = {\n        page: pagination.page,\n        per_page: pagination.per_page,\n        ...Object.fromEntries(\n          Object.entries(filters).filter(([_, value]) => value !== '')\n        )\n      };\n\n      const response = await axiosInstance.get('/v1/work-logs', { params });\n      \n      setWorkLogs(response.data.work_logs || []);\n      setPagination(prev => ({\n        ...prev,\n        total_count: response.data.total_count || 0\n      }));\n\n    } catch (error) {\n      console.error('Errore nel caricamento work logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilterChange = (e) => {\n    const { name, value } = e.target;\n    setFilters(prev => ({ ...prev, [name]: value }));\n    setPagination(prev => ({ ...prev, page: 1 })); // Reset alla prima pagina\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      activity_type: '',\n      operator_id: '',\n      id_cantiere: '',\n      start_date: '',\n      end_date: ''\n    });\n  };\n\n  const formatDateTime = (dateString) => {\n    return new Date(dateString).toLocaleString('it-IT', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatDuration = (minutes) => {\n    if (!minutes) return '-';\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n  };\n\n  const getActivityIcon = (activity) => {\n    switch (activity) {\n      case 'Posa': return '🔧';\n      case 'Collegamento': return '🔌';\n      case 'Certificazione': return '✅';\n      default: return '📝';\n    }\n  };\n\n  const getConditionColor = (condition) => {\n    switch (condition) {\n      case 'Normale': return 'bg-green-100 text-green-800';\n      case 'Spazi Ristretti': return 'bg-yellow-100 text-yellow-800';\n      case 'In Altezza': return 'bg-orange-100 text-orange-800';\n      case 'Esterno': return 'bg-blue-100 text-blue-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const totalPages = Math.ceil(pagination.total_count / pagination.per_page);\n\n  if (loading && workLogs.length === 0) {\n    return (\n      <div className=\"flex justify-center items-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n        <span className=\"ml-2\">Caricamento work logs...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header con statistiche */}\n      <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-bold text-gray-800\">📝 Work Logs</h2>\n          <button\n            onClick={onCreateNew}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            ➕ Nuovo Work Log\n          </button>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-blue-600\">{pagination.total_count}</p>\n            <p className=\"text-sm text-gray-600\">Totale Work Logs</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-green-600\">\n              {workLogs.reduce((sum, log) => sum + (log.quantity || 0), 0).toFixed(1)}\n            </p>\n            <p className=\"text-sm text-gray-600\">Quantità Totale</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-orange-600\">\n              {workLogs.reduce((sum, log) => sum + (log.total_man_hours || 0), 0).toFixed(1)}\n            </p>\n            <p className=\"text-sm text-gray-600\">Ore-Uomo Totali</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-purple-600\">\n              {workLogs.length > 0 ? \n                (workLogs.reduce((sum, log) => sum + (log.productivity_per_hour || 0), 0) / workLogs.length).toFixed(2) \n                : '0.00'\n              }\n            </p>\n            <p className=\"text-sm text-gray-600\">Produttività Media</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Filtri */}\n      <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">🔍 Filtri</h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Attività</label>\n            <select\n              name=\"activity_type\"\n              value={filters.activity_type}\n              onChange={handleFilterChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Tutte</option>\n              <option value=\"Posa\">Posa</option>\n              <option value=\"Collegamento\">Collegamento</option>\n              <option value=\"Certificazione\">Certificazione</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Operatore</label>\n            <select\n              name=\"operator_id\"\n              value={filters.operator_id}\n              onChange={handleFilterChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Tutti</option>\n              {operators.map(op => (\n                <option key={op.id_responsabile} value={op.id_responsabile}>\n                  {op.nome_responsabile}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Cantiere</label>\n            <select\n              name=\"id_cantiere\"\n              value={filters.id_cantiere}\n              onChange={handleFilterChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Tutti</option>\n              {cantieri.map(cantiere => (\n                <option key={cantiere.id_cantiere} value={cantiere.id_cantiere}>\n                  {cantiere.commessa}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Data Inizio</label>\n            <input\n              type=\"date\"\n              name=\"start_date\"\n              value={filters.start_date}\n              onChange={handleFilterChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Data Fine</label>\n            <input\n              type=\"date\"\n              name=\"end_date\"\n              value={filters.end_date}\n              onChange={handleFilterChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n        </div>\n\n        <div className=\"mt-4 flex justify-end\">\n          <button\n            onClick={clearFilters}\n            className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            🗑️ Pulisci Filtri\n          </button>\n        </div>\n      </div>\n\n      {/* Lista Work Logs */}\n      <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n        {workLogs.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">📝</div>\n            <p className=\"text-gray-500 text-lg\">Nessun work log trovato</p>\n            <p className=\"text-gray-400 text-sm mt-2\">\n              {Object.values(filters).some(v => v !== '') \n                ? 'Prova a modificare i filtri o' \n                : 'Inizia creando il tuo primo work log'\n              }\n            </p>\n            <button\n              onClick={onCreateNew}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              ➕ Crea Primo Work Log\n            </button>\n          </div>\n        ) : (\n          <>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Attività\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Operatore\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Quantità\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Durata\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Produttività\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Condizioni\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Data\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Azioni\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {workLogs.map((log) => (\n                    <tr key={log.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <span className=\"text-lg mr-2\">{getActivityIcon(log.activity_type)}</span>\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {log.activity_type}\n                            </div>\n                            {log.sub_activity_detail && (\n                              <div className=\"text-sm text-gray-500\">\n                                {log.sub_activity_detail}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        Operatore #{log.operator_id}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {log.quantity?.toFixed(1)} {log.activity_type === 'Posa' ? 'm' : 'unità'}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {formatDuration(log.duration_minutes)}\n                        <div className=\"text-xs text-gray-500\">\n                          {log.number_of_operators_on_task} operatori\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {log.productivity_per_hour?.toFixed(2)} /h\n                        <div className=\"text-xs text-gray-500\">\n                          {log.productivity_per_person_per_hour?.toFixed(2)} /h/op\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConditionColor(log.environmental_conditions)}`}>\n                          {log.environmental_conditions}\n                        </span>\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {log.tools_used}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {formatDateTime(log.start_timestamp)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                        <button\n                          onClick={() => onEdit(log)}\n                          className=\"text-blue-600 hover:text-blue-900\"\n                          title=\"Modifica\"\n                        >\n                          ✏️\n                        </button>\n                        <button\n                          onClick={() => onDelete(log.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                          title=\"Elimina\"\n                        >\n                          🗑️\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Paginazione */}\n            {totalPages > 1 && (\n              <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200\">\n                <div className=\"flex-1 flex justify-between sm:hidden\">\n                  <button\n                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n                    disabled={pagination.page === 1}\n                    className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                  >\n                    Precedente\n                  </button>\n                  <button\n                    onClick={() => setPagination(prev => ({ ...prev, page: Math.min(totalPages, prev.page + 1) }))}\n                    disabled={pagination.page === totalPages}\n                    className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                  >\n                    Successiva\n                  </button>\n                </div>\n                <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-700\">\n                      Mostrando{' '}\n                      <span className=\"font-medium\">\n                        {(pagination.page - 1) * pagination.per_page + 1}\n                      </span>{' '}\n                      a{' '}\n                      <span className=\"font-medium\">\n                        {Math.min(pagination.page * pagination.per_page, pagination.total_count)}\n                      </span>{' '}\n                      di{' '}\n                      <span className=\"font-medium\">{pagination.total_count}</span>{' '}\n                      risultati\n                    </p>\n                  </div>\n                  <div>\n                    <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                      <button\n                        onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n                        disabled={pagination.page === 1}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                      >\n                        ‹\n                      </button>\n                      \n                      {/* Numeri di pagina */}\n                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                        const pageNum = i + 1;\n                        return (\n                          <button\n                            key={pageNum}\n                            onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}\n                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                              pagination.page === pageNum\n                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\n                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                            }`}\n                          >\n                            {pageNum}\n                          </button>\n                        );\n                      })}\n                      \n                      <button\n                        onClick={() => setPagination(prev => ({ ...prev, page: Math.min(totalPages, prev.page + 1) }))}\n                        disabled={pagination.page === totalPages}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                      >\n                        ›\n                      </button>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default WorkLogsList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,UAAU,QACL,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4D,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC;IACrCgE,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC;IAC3CuE,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd6E,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN7E,SAAS,CAAC,MAAM;IACd8E,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACjB,OAAO,EAAEO,UAAU,CAACE,IAAI,CAAC,CAAC;EAE9B,MAAMO,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAM,CAACE,YAAY,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpDnC,aAAa,CAACoC,GAAG,CAAC,eAAe,CAAC,EAClCpC,aAAa,CAACoC,GAAG,CAAC,WAAW,CAAC,CAC/B,CAAC;MAEFT,YAAY,CAACK,YAAY,CAACK,IAAI,IAAI,EAAE,CAAC;MACrCR,WAAW,CAACI,WAAW,CAACI,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D;EACF,CAAC;EAED,MAAMP,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM2B,MAAM,GAAG;QACbjB,IAAI,EAAEF,UAAU,CAACE,IAAI;QACrBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;QAC7B,GAAGiB,MAAM,CAACC,WAAW,CACnBD,MAAM,CAACE,OAAO,CAAC7B,OAAO,CAAC,CAAC8B,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,KAAKA,KAAK,KAAK,EAAE,CAC7D;MACF,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAM/C,aAAa,CAACoC,GAAG,CAAC,eAAe,EAAE;QAAEI;MAAO,CAAC,CAAC;MAErE7B,WAAW,CAACoC,QAAQ,CAACV,IAAI,CAACW,SAAS,IAAI,EAAE,CAAC;MAC1C1B,aAAa,CAAC2B,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPxB,WAAW,EAAEsB,QAAQ,CAACV,IAAI,CAACZ,WAAW,IAAI;MAC5C,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAM;MAAEC,IAAI;MAAEN;IAAM,CAAC,GAAGK,CAAC,CAACE,MAAM;IAChCtC,UAAU,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACG,IAAI,GAAGN;IAAM,CAAC,CAAC,CAAC;IAChDxB,aAAa,CAAC2B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1B,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC;EAED,MAAM+B,YAAY,GAAGA,CAAA,KAAM;IACzBvC,UAAU,CAAC;MACTC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmC,cAAc,GAAIC,UAAU,IAAK;IACrC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;MAClDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,GAAG;IACxB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAOC,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKG,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;EACtD,CAAC;EAED,MAAMC,eAAe,GAAIC,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,cAAc;QAAE,OAAO,IAAI;MAChC,KAAK,gBAAgB;QAAE,OAAO,GAAG;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIC,SAAS,IAAK;IACvC,QAAQA,SAAS;MACf,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,iBAAiB;QAAE,OAAO,+BAA+B;MAC9D,KAAK,YAAY;QAAE,OAAO,+BAA+B;MACzD,KAAK,SAAS;QAAE,OAAO,2BAA2B;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMC,UAAU,GAAGP,IAAI,CAACQ,IAAI,CAACtD,UAAU,CAACI,WAAW,GAAGJ,UAAU,CAACG,QAAQ,CAAC;EAE1E,IAAIZ,OAAO,IAAIF,QAAQ,CAACkE,MAAM,KAAK,CAAC,EAAE;IACpC,oBACE1E,OAAA;MAAK2E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD5E,OAAA;QAAK2E,SAAS,EAAC;MAA8D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpFhF,OAAA;QAAM2E,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAEV;EAEA,oBACEhF,OAAA;IAAK2E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5E,OAAA;MAAK2E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD5E,OAAA;QAAK2E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5E,OAAA;UAAI2E,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEhF,OAAA;UACEiF,OAAO,EAAE3E,WAAY;UACrBqE,SAAS,EAAC,mHAAmH;UAAAC,QAAA,EAC9H;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD5E,OAAA;UAAK2E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5E,OAAA;YAAG2E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEzD,UAAU,CAACI;UAAW;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EhF,OAAA;YAAG2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5E,OAAA;YAAG2E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC7CpE,QAAQ,CAAC0E,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACJhF,OAAA;YAAG2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5E,OAAA;YAAG2E,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAC9CpE,QAAQ,CAAC0E,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACG,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACD,OAAO,CAAC,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACJhF,OAAA;YAAG2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5E,OAAA;YAAG2E,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAC9CpE,QAAQ,CAACkE,MAAM,GAAG,CAAC,GAClB,CAAClE,QAAQ,CAAC0E,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACI,qBAAqB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGhF,QAAQ,CAACkE,MAAM,EAAEY,OAAO,CAAC,CAAC,CAAC,GACrG;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAET,CAAC,eACJhF,OAAA;YAAG2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD5E,OAAA;QAAI2E,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEvEhF,OAAA;QAAK2E,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnE5E,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAO2E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChFhF,OAAA;YACEkD,IAAI,EAAC,eAAe;YACpBN,KAAK,EAAEhC,OAAO,CAACE,aAAc;YAC7B2E,QAAQ,EAAEzC,kBAAmB;YAC7B2B,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBAElH5E,OAAA;cAAQ4C,KAAK,EAAC,EAAE;cAAAgC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/BhF,OAAA;cAAQ4C,KAAK,EAAC,MAAM;cAAAgC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClChF,OAAA;cAAQ4C,KAAK,EAAC,cAAc;cAAAgC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClDhF,OAAA;cAAQ4C,KAAK,EAAC,gBAAgB;cAAAgC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhF,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAO2E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjFhF,OAAA;YACEkD,IAAI,EAAC,aAAa;YAClBN,KAAK,EAAEhC,OAAO,CAACG,WAAY;YAC3B0E,QAAQ,EAAEzC,kBAAmB;YAC7B2B,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBAElH5E,OAAA;cAAQ4C,KAAK,EAAC,EAAE;cAAAgC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC9BxD,SAAS,CAACkE,GAAG,CAACC,EAAE,iBACf3F,OAAA;cAAiC4C,KAAK,EAAE+C,EAAE,CAACC,eAAgB;cAAAhB,QAAA,EACxDe,EAAE,CAACE;YAAiB,GADVF,EAAE,CAACC,eAAe;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEvB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhF,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAO2E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChFhF,OAAA;YACEkD,IAAI,EAAC,aAAa;YAClBN,KAAK,EAAEhC,OAAO,CAACI,WAAY;YAC3ByE,QAAQ,EAAEzC,kBAAmB;YAC7B2B,SAAS,EAAC,wGAAwG;YAAAC,QAAA,gBAElH5E,OAAA;cAAQ4C,KAAK,EAAC,EAAE;cAAAgC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC9BtD,QAAQ,CAACgE,GAAG,CAACI,QAAQ,iBACpB9F,OAAA;cAAmC4C,KAAK,EAAEkD,QAAQ,CAAC9E,WAAY;cAAA4D,QAAA,EAC5DkB,QAAQ,CAACC;YAAQ,GADPD,QAAQ,CAAC9E,WAAW;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEzB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhF,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAO2E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnFhF,OAAA;YACEgG,IAAI,EAAC,MAAM;YACX9C,IAAI,EAAC,YAAY;YACjBN,KAAK,EAAEhC,OAAO,CAACK,UAAW;YAC1BwE,QAAQ,EAAEzC,kBAAmB;YAC7B2B,SAAS,EAAC;UAAwG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhF,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAO2E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjFhF,OAAA;YACEgG,IAAI,EAAC,MAAM;YACX9C,IAAI,EAAC,UAAU;YACfN,KAAK,EAAEhC,OAAO,CAACM,QAAS;YACxBuE,QAAQ,EAAEzC,kBAAmB;YAC7B2B,SAAS,EAAC;UAAwG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC5E,OAAA;UACEiF,OAAO,EAAE7B,YAAa;UACtBuB,SAAS,EAAC,gIAAgI;UAAAC,QAAA,EAC3I;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,+CAA+C;MAAAC,QAAA,EAC3DpE,QAAQ,CAACkE,MAAM,KAAK,CAAC,gBACpB1E,OAAA;QAAK2E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5E,OAAA;UAAK2E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvChF,OAAA;UAAG2E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEhF,OAAA;UAAG2E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtCrC,MAAM,CAAC0D,MAAM,CAACrF,OAAO,CAAC,CAACsF,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,GACvC,+BAA+B,GAC/B;QAAsC;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzC,CAAC,eACJhF,OAAA;UACEiF,OAAO,EAAE3E,WAAY;UACrBqE,SAAS,EAAC,wHAAwH;UAAAC,QAAA,EACnI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENhF,OAAA,CAAAE,SAAA;QAAA0E,QAAA,gBACE5E,OAAA;UAAK2E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B5E,OAAA;YAAO2E,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpD5E,OAAA;cAAO2E,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B5E,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRhF,OAAA;cAAO2E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDpE,QAAQ,CAACkF,GAAG,CAAEN,GAAG;gBAAA,IAAAgB,aAAA,EAAAC,qBAAA,EAAAC,sBAAA;gBAAA,oBAChBtG,OAAA;kBAAiB2E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC3C5E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzC5E,OAAA;sBAAK2E,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC5E,OAAA;wBAAM2E,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAER,eAAe,CAACgB,GAAG,CAACtE,aAAa;sBAAC;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC1EhF,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAK2E,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAC/CQ,GAAG,CAACtE;wBAAa;0BAAA+D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,EACLI,GAAG,CAACmB,mBAAmB,iBACtBvG,OAAA;0BAAK2E,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EACnCQ,GAAG,CAACmB;wBAAmB;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhF,OAAA;oBAAI2E,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,GAAC,aACrD,EAACQ,GAAG,CAACrE,WAAW;kBAAA;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACLhF,OAAA;oBAAI2E,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,IAAAwB,aAAA,GAC9DhB,GAAG,CAACC,QAAQ,cAAAe,aAAA,uBAAZA,aAAA,CAAcd,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACF,GAAG,CAACtE,aAAa,KAAK,MAAM,GAAG,GAAG,GAAG,OAAO;kBAAA;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACLhF,OAAA;oBAAI2E,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,GAC9Dd,cAAc,CAACsB,GAAG,CAACoB,gBAAgB,CAAC,eACrCxG,OAAA;sBAAK2E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnCQ,GAAG,CAACqB,2BAA2B,EAAC,YACnC;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhF,OAAA;oBAAI2E,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,IAAAyB,qBAAA,GAC9DjB,GAAG,CAACI,qBAAqB,cAAAa,qBAAA,uBAAzBA,qBAAA,CAA2Bf,OAAO,CAAC,CAAC,CAAC,EAAC,KACvC,eAAAtF,OAAA;sBAAK2E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,IAAA0B,sBAAA,GACnClB,GAAG,CAACsB,gCAAgC,cAAAJ,sBAAA,uBAApCA,sBAAA,CAAsChB,OAAO,CAAC,CAAC,CAAC,EAAC,QACpD;oBAAA;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhF,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACzC5E,OAAA;sBAAM2E,SAAS,EAAE,4DAA4DL,iBAAiB,CAACc,GAAG,CAACuB,wBAAwB,CAAC,EAAG;sBAAA/B,QAAA,EAC5HQ,GAAG,CAACuB;oBAAwB;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACPhF,OAAA;sBAAK2E,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EACxCQ,GAAG,CAACwB;oBAAU;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhF,OAAA;oBAAI2E,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,EAC9DvB,cAAc,CAAC+B,GAAG,CAACyB,eAAe;kBAAC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACLhF,OAAA;oBAAI2E,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,gBACvE5E,OAAA;sBACEiF,OAAO,EAAEA,CAAA,KAAM7E,MAAM,CAACgF,GAAG,CAAE;sBAC3BT,SAAS,EAAC,mCAAmC;sBAC7CmC,KAAK,EAAC,UAAU;sBAAAlC,QAAA,EACjB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACThF,OAAA;sBACEiF,OAAO,EAAEA,CAAA,KAAM5E,QAAQ,CAAC+E,GAAG,CAAC2B,EAAE,CAAE;sBAChCpC,SAAS,EAAC,iCAAiC;sBAC3CmC,KAAK,EAAC,SAAS;sBAAAlC,QAAA,EAChB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GA5DEI,GAAG,CAAC2B,EAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6DX,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGLR,UAAU,GAAG,CAAC,iBACbxE,OAAA;UAAK2E,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC5F5E,OAAA;YAAK2E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5E,OAAA;cACEiF,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1B,IAAI,EAAE4C,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAEjE,IAAI,CAAC1B,IAAI,GAAG,CAAC;cAAE,CAAC,CAAC,CAAE;cACtF4F,QAAQ,EAAE9F,UAAU,CAACE,IAAI,KAAK,CAAE;cAChCsD,SAAS,EAAC,+JAA+J;cAAAC,QAAA,EAC1K;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThF,OAAA;cACEiF,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1B,IAAI,EAAE4C,IAAI,CAACiD,GAAG,CAAC1C,UAAU,EAAEzB,IAAI,CAAC1B,IAAI,GAAG,CAAC;cAAE,CAAC,CAAC,CAAE;cAC/F4F,QAAQ,EAAE9F,UAAU,CAACE,IAAI,KAAKmD,UAAW;cACzCG,SAAS,EAAC,oKAAoK;cAAAC,QAAA,EAC/K;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1E5E,OAAA;cAAA4E,QAAA,eACE5E,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAC1B,EAAC,GAAG,eACb5E,OAAA;kBAAM2E,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAC1B,CAACzD,UAAU,CAACE,IAAI,GAAG,CAAC,IAAIF,UAAU,CAACG,QAAQ,GAAG;gBAAC;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,EAAC,GAAG,EAAC,GACX,EAAC,GAAG,eACLhF,OAAA;kBAAM2E,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAC1BX,IAAI,CAACiD,GAAG,CAAC/F,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACG,QAAQ,EAAEH,UAAU,CAACI,WAAW;gBAAC;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,EAAC,GAAG,EAAC,IACV,EAAC,GAAG,eACNhF,OAAA;kBAAM2E,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEzD,UAAU,CAACI;gBAAW;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAAC,GAAG,EAAC,WAEpE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhF,OAAA;cAAA4E,QAAA,eACE5E,OAAA;gBAAK2E,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBACxE5E,OAAA;kBACEiF,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAAC2B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE1B,IAAI,EAAE4C,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAEjE,IAAI,CAAC1B,IAAI,GAAG,CAAC;kBAAE,CAAC,CAAC,CAAE;kBACtF4F,QAAQ,EAAE9F,UAAU,CAACE,IAAI,KAAK,CAAE;kBAChCsD,SAAS,EAAC,iKAAiK;kBAAAC,QAAA,EAC5K;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAGRmC,KAAK,CAACC,IAAI,CAAC;kBAAE1C,MAAM,EAAET,IAAI,CAACiD,GAAG,CAAC,CAAC,EAAE1C,UAAU;gBAAE,CAAC,EAAE,CAAC7B,CAAC,EAAE0E,CAAC,KAAK;kBACzD,MAAMC,OAAO,GAAGD,CAAC,GAAG,CAAC;kBACrB,oBACErH,OAAA;oBAEEiF,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAAC2B,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE1B,IAAI,EAAEiG;oBAAQ,CAAC,CAAC,CAAE;oBACnE3C,SAAS,EAAE,0EACTxD,UAAU,CAACE,IAAI,KAAKiG,OAAO,GACvB,+CAA+C,GAC/C,yDAAyD,EAC5D;oBAAA1C,QAAA,EAEF0C;kBAAO,GARHA,OAAO;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASN,CAAC;gBAEb,CAAC,CAAC,eAEFhF,OAAA;kBACEiF,OAAO,EAAEA,CAAA,KAAM7D,aAAa,CAAC2B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE1B,IAAI,EAAE4C,IAAI,CAACiD,GAAG,CAAC1C,UAAU,EAAEzB,IAAI,CAAC1B,IAAI,GAAG,CAAC;kBAAE,CAAC,CAAC,CAAE;kBAC/F4F,QAAQ,EAAE9F,UAAU,CAACE,IAAI,KAAKmD,UAAW;kBACzCG,SAAS,EAAC,iKAAiK;kBAAAC,QAAA,EAC5K;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,eACD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CA5cIJ,YAAY;AAAAoH,EAAA,GAAZpH,YAAY;AA8clB,eAAeA,YAAY;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}