{"version": 3, "names": ["isCompatTag", "tagName", "test"], "sources": ["../../../src/validators/react/isCompatTag.ts"], "sourcesContent": ["export default function isCompatTag(tagName?: string): boolean {\n  // Must start with a lowercase ASCII letter\n  return !!tagName && /^[a-z]/.test(tagName);\n}\n"], "mappings": ";;;;;;AAAe,SAASA,WAAWA,CAACC,OAAgB,EAAW;EAE7D,OAAO,CAAC,CAACA,OAAO,IAAI,QAAQ,CAACC,IAAI,CAACD,OAAO,CAAC;AAC5C", "ignoreList": []}